#!/bin/bash

echo "🚀 Starting AI Navigator Scrapers System"
echo "========================================"

# Check if backend is running
if curl -s http://localhost:8001/api/health > /dev/null 2>&1; then
    echo "✅ Backend is already running on port 8001"
else
    echo "🔧 Starting backend server..."
    cd backend
    python3 working_server.py &
    BACKEND_PID=$!
    echo "Backend started with PID: $BACKEND_PID"
    cd ..
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    for i in {1..10}; do
        if curl -s http://localhost:8001/api/health > /dev/null 2>&1; then
            echo "✅ Backend is ready!"
            break
        fi
        sleep 1
    done
fi

# Check if frontend is running
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is already running on port 3000"
else
    echo "🌐 Starting frontend..."
    cd frontend
    
    # Try different node paths
    if command -v npm > /dev/null 2>&1; then
        npm start &
    elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
        /Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm start &
    else
        echo "❌ npm not found. Please start frontend manually:"
        echo "   cd frontend && npm start"
        exit 1
    fi
    
    FRONTEND_PID=$!
    echo "Frontend started with PID: $FRONTEND_PID"
    cd ..
    
    # Wait for frontend to start
    echo "⏳ Waiting for frontend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✅ Frontend is ready!"
            break
        fi
        sleep 1
    done
fi

echo ""
echo "🎉 System Status:"
echo "=================="

# Check backend
if curl -s http://localhost:8001/api/health > /dev/null 2>&1; then
    echo "✅ Backend: http://localhost:8001 (RUNNING)"
else
    echo "❌ Backend: http://localhost:8001 (NOT RUNNING)"
fi

# Check frontend
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend: http://localhost:3000 (RUNNING)"
else
    echo "❌ Frontend: http://localhost:3000 (NOT RUNNING)"
fi

echo ""
echo "🔧 Quick Test:"
echo "=============="

# Test spiders endpoint
SPIDERS=$(curl -s http://localhost:8001/api/spiders 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Spiders API: Working"
    echo "   Available spiders: $(echo $SPIDERS | grep -o '"[^"]*"' | tr -d '"' | tr '\n' ' ')"
else
    echo "❌ Spiders API: Failed"
fi

# Test capabilities
CAPABILITIES=$(curl -s http://localhost:8001/api/capabilities 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Capabilities API: Working"
    ENHANCED=$(echo $CAPABILITIES | grep -o '"enhanced_scraping":[^,}]*' | cut -d: -f2)
    echo "   Enhanced scraping: $ENHANCED"
else
    echo "❌ Capabilities API: Failed"
fi

echo ""
echo "🌐 Ready to Use:"
echo "================"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Go to 'Scraping' tab to start traditional scraping"
echo "3. Go to 'Enhanced' tab to use the enhance button"
echo "4. Both buttons should now work!"
echo ""
echo "Press Ctrl+C to stop both servers"

# Keep script running
wait
