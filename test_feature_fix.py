#!/usr/bin/env python3
"""
Test the feature extraction fix by processing a single entity
"""

import requests
import json
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_feature_extraction():
    """Test that features are now being extracted after the fallback fix"""
    
    # Test URL - a simple AI tool
    test_url = "https://www.notion.so"
    
    logger.info(f"🧪 Testing feature extraction fix with: {test_url}")
    
    # Start enhanced scraping
    response = requests.post("http://localhost:8001/api/start-enhanced-scraping", json={
        "tools": [
            {
                "name": "Notion",
                "url": test_url
            }
        ],
        "use_parallel": False,
        "use_phase3": True
    })
    
    if response.status_code != 200:
        logger.error(f"Failed to start scraping: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return False
    
    job_data = response.json()
    job_id = job_data.get('job_id')
    logger.info(f"✅ Started scraping job: {job_id}")
    
    # Monitor job progress
    max_wait = 120  # 2 minutes max
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status_response = requests.get(f"http://localhost:8001/api/job-status/{job_id}")
        
        if status_response.status_code != 200:
            logger.error(f"Failed to get job status: {status_response.status_code}")
            return False
        
        status_data = status_response.json()
        status = status_data.get('status')
        
        logger.info(f"Job status: {status}")
        
        if status == 'completed':
            logger.info("✅ Job completed successfully!")
            
            # Check the results
            results = status_data.get('results', {})
            logger.info(f"🔍 Full results structure: {json.dumps(results, indent=2)}")
            
            successful_results = results.get('successful_results', [])
            failed_results = results.get('failed_results', [])
            
            logger.info(f"🎯 Successful results: {len(successful_results)}")
            logger.info(f"🎯 Failed results: {len(failed_results)}")
            
            if successful_results:
                result_info = successful_results[0]
                entity_data = result_info.get('data', {})
                
                features_mapped = result_info.get('features_mapped', 0)
                categories_mapped = result_info.get('categories_mapped', 0)
                tags_mapped = result_info.get('tags_mapped', 0)
                
                # Also check the actual entity data
                actual_feature_ids = entity_data.get('feature_ids', [])
                actual_category_ids = entity_data.get('category_ids', [])
                actual_tag_ids = entity_data.get('tag_ids', [])
                key_features = entity_data.get('tool_details', {}).get('key_features', [])
                
                logger.info(f"🎯 Entity: {entity_data.get('name', 'Unknown')}")
                logger.info(f"🎯 Server reported - Features: {features_mapped}, Categories: {categories_mapped}, Tags: {tags_mapped}")
                logger.info(f"🎯 Actual entity data - Feature IDs: {len(actual_feature_ids)}, Category IDs: {len(actual_category_ids)}, Tag IDs: {len(actual_tag_ids)}")
                logger.info(f"🎯 Key features in tool_details: {len(key_features)}")
                
                if len(actual_feature_ids) > 0:
                    logger.info("✅ SUCCESS: Features are being mapped to feature_ids!")
                    logger.info(f"   Feature IDs: {actual_feature_ids[:3]}...")
                    return True
                elif len(key_features) > 0:
                    logger.info("⚠️  PARTIAL SUCCESS: Key features exist but not mapped to feature_ids")
                    logger.info(f"   Key features: {key_features[:3]}...")
                    logger.error("❌ Feature extraction works but feature ID mapping is broken")
                    return False
                else:
                    logger.error("❌ NO FEATURES: Neither feature_ids nor key_features found")
                    return False
            else:
                logger.error("❌ No successful results returned")
                if failed_results:
                    logger.error(f"❌ Failed result: {failed_results[0].get('error', 'Unknown error')}")
                return False
                
        elif status == 'failed':
            logger.error(f"❌ Job failed: {status_data.get('error', 'Unknown error')}")
            return False
        
        time.sleep(5)  # Wait 5 seconds before checking again
    
    logger.error("❌ Job timed out")
    return False

if __name__ == "__main__":
    success = test_feature_extraction()
    if success:
        print("\n🎉 FEATURE EXTRACTION FIX SUCCESSFUL!")
    else:
        print("\n💥 FEATURE EXTRACTION STILL BROKEN")