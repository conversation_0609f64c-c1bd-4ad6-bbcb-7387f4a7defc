"""
Simple test for Enhanced Feature Mapper without full configuration
Tests the rule-based mapping logic
"""

import sys
sys.path.append('/app')

from enhanced_feature_mapper import EnhancedFeatureMapper
import logging

# Mock AI Navigator Client
class MockAINavigatorClient:
    def get_features(self):
        # Return mock features that match our mapping rules
        return [
            {"id": "feat-1", "name": "Natural Language Processing", "description": "NLP capabilities"},
            {"id": "feat-2", "name": "Drag and Drop", "description": "Drag and drop interface"},
            {"id": "feat-3", "name": "API Integration", "description": "API integration capabilities"},
            {"id": "feat-4", "name": "Machine Learning", "description": "ML capabilities"},
            {"id": "feat-5", "name": "Data Visualization", "description": "Data visualization features"},
            {"id": "feat-6", "name": "Workflow Automation", "description": "Automation features"},
            {"id": "feat-7", "name": "Real-time Collaboration", "description": "Collaboration features"},
            {"id": "feat-8", "name": "Template Library", "description": "Template features"},
            {"id": "feat-9", "name": "Text Generation", "description": "Text generation capabilities"},
            {"id": "feat-10", "name": "Analytics", "description": "Analytics capabilities"}
        ]

def test_rule_based_mapping():
    """Test rule-based feature mapping"""
    
    print("🔧 TESTING RULE-BASED FEATURE MAPPING")
    print("=" * 50)
    
    # Initialize with mock client
    mock_client = MockAINavigatorClient()
    mapper = EnhancedFeatureMapper(mock_client, "dummy-api-key")
    
    # Test cases
    test_cases = [
        {
            "name": "Canva-like Tool",
            "capabilities": ["drag-and-drop interface", "templates", "design tools"],
            "use_cases": ["social media graphics", "presentations"],
            "description": "Drag-and-drop design tool with templates",
            "expected_features": ["drag and drop", "template library"]
        },
        {
            "name": "Zapier-like Tool", 
            "capabilities": ["API integration", "workflow automation", "webhooks"],
            "use_cases": ["business automation", "data synchronization"],
            "description": "Automation platform with API integrations",
            "expected_features": ["api integration", "workflow automation"]
        },
        {
            "name": "ChatGPT-like Tool",
            "capabilities": ["natural language processing", "text generation", "AI responses"],
            "use_cases": ["content creation", "writing assistance"],
            "description": "AI-powered text generation and NLP tool",
            "expected_features": ["natural language processing", "text generation"]
        }
    ]
    
    total_expected = 0
    total_correct = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['name']}")
        print(f"   Capabilities: {test_case['capabilities']}")
        print(f"   Expected: {test_case['expected_features']}")
        
        # Test rule-based mapping
        rule_results = mapper._rule_based_feature_mapping(
            test_case['capabilities'],
            test_case['use_cases'],
            test_case['description']
        )
        
        print(f"   Found {len(rule_results)} features:")
        predicted_features = []
        for feature in rule_results:
            print(f"     - {feature['feature_name']} (confidence: {feature['confidence']:.2f})")
            predicted_features.append(feature['feature_name'])
        
        # Calculate accuracy
        expected_set = set(test_case['expected_features'])
        predicted_set = set(predicted_features)
        intersection = expected_set.intersection(predicted_set)
        
        if expected_set:
            accuracy = len(intersection) / len(expected_set) * 100
            print(f"   Accuracy: {accuracy:.1f}% ({len(intersection)}/{len(expected_set)} correct)")
            
            total_expected += len(expected_set)
            total_correct += len(intersection)
    
    # Calculate overall accuracy
    overall_accuracy = (total_correct / total_expected * 100) if total_expected > 0 else 0
    print(f"\n📊 RULE-BASED MAPPING RESULTS:")
    print(f"   Total correct: {total_correct}/{total_expected}")
    print(f"   Overall accuracy: {overall_accuracy:.1f}%")
    
    if overall_accuracy >= 70:  # Lower threshold for rule-based only
        print("   ✅ RULE-BASED MAPPING WORKING WELL")
        return True
    else:
        print("   ⚠️  Rule-based mapping needs improvement")
        return False

def test_feature_taxonomy_loading():
    """Test feature taxonomy loading"""
    
    print("\n📚 TESTING FEATURE TAXONOMY LOADING")
    print("=" * 50)
    
    mock_client = MockAINavigatorClient()
    mapper = EnhancedFeatureMapper(mock_client, "dummy-api-key")
    
    print(f"Loaded {len(mapper.features_taxonomy)} features:")
    for feature_name, feature_id in list(mapper.features_taxonomy.items())[:5]:
        print(f"  - {feature_name}: {feature_id}")
    
    if len(mapper.features_taxonomy) > 0:
        print("   ✅ Feature taxonomy loaded successfully")
        return True
    else:
        print("   ❌ Failed to load feature taxonomy")
        return False

def test_mapping_rules():
    """Test the mapping rules coverage"""
    
    print("\n📋 TESTING MAPPING RULES COVERAGE")
    print("=" * 50)
    
    mock_client = MockAINavigatorClient()
    mapper = EnhancedFeatureMapper(mock_client, "dummy-api-key")
    
    # Check how many taxonomy features have mapping rules
    taxonomy_features = set(mapper.features_taxonomy.keys())
    rule_features = set(mapper.feature_mapping_rules.keys())
    
    covered_features = taxonomy_features.intersection(rule_features)
    coverage_percentage = len(covered_features) / len(taxonomy_features) * 100
    
    print(f"Taxonomy features: {len(taxonomy_features)}")
    print(f"Rule features: {len(rule_features)}")
    print(f"Covered features: {len(covered_features)}")
    print(f"Coverage: {coverage_percentage:.1f}%")
    
    print(f"\nCovered features:")
    for feature in sorted(covered_features):
        print(f"  - {feature}")
    
    if coverage_percentage >= 50:  # At least 50% coverage
        print("   ✅ Good mapping rules coverage")
        return True
    else:
        print("   ⚠️  Low mapping rules coverage")
        return False

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    print("🎯 ENHANCED FEATURE MAPPER TESTING")
    print("=" * 60)
    
    # Run tests
    taxonomy_success = test_feature_taxonomy_loading()
    rules_success = test_mapping_rules()
    mapping_success = test_rule_based_mapping()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Taxonomy loading: {'✅ PASS' if taxonomy_success else '❌ FAIL'}")
    print(f"   Mapping rules: {'✅ PASS' if rules_success else '❌ FAIL'}")
    print(f"   Rule-based mapping: {'✅ PASS' if mapping_success else '❌ FAIL'}")
    
    if taxonomy_success and rules_success and mapping_success:
        print("   🎉 ALL TESTS PASSED!")
        print("   📈 Feature mapper is ready for integration")
    else:
        print("   ⚠️  Some tests failed - needs improvement")
