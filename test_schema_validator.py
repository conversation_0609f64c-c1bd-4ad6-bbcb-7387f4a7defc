"""
Test script for Schema Validator
Tests validation and normalization with various data scenarios
"""

import sys
sys.path.append('/app')

from schema_validator import SchemaValidator, ValidationSeverity
import json

def test_schema_validator():
    """Test the schema validator with various data scenarios"""
    
    print("📋 TESTING SCHEMA VALIDATOR")
    print("=" * 50)
    
    validator = SchemaValidator()
    
    # Test cases with different validation scenarios
    test_cases = [
        {
            "name": "Valid AI Tool",
            "data": {
                "name": "Test AI Tool",
                "website_url": "https://example.com",
                "entity_type_id": "uuid-123",
                "short_description": "A great AI tool for testing",
                "description": "This is a comprehensive description of the AI tool that is long enough to meet validation requirements and provides detailed information about its capabilities.",
                "logo_url": "https://example.com/logo.png",
                "documentation_url": "https://example.com/docs",
                "contact_url": "https://example.com/contact",
                "privacy_policy_url": "https://example.com/privacy",
                "founded_year": 2020,
                "pricing_model": "freemium",
                "price_range": "medium",
                "technical_level": "intermediate",
                "employee_count_range": "11-50",
                "funding_stage": "series a",
                "affiliate_status": "none",
                "category_ids": [],
                "tag_ids": [],
                "feature_ids": [],
                "social_links": {"twitter": "example"},
                "tool_details": {"has_api": True}
            },
            "expected_errors": 0,
            "expected_compliance": 100.0
        },
        {
            "name": "Missing Required Fields",
            "data": {
                "name": "Incomplete Tool",
                # Missing website_url, entity_type_id, short_description, description
                "logo_url": "https://example.com/logo.png"
            },
            "expected_errors": 5,  # 4 base + 1 entity-specific required fields
            "expected_compliance": 0.0
        },
        {
            "name": "Invalid Enums and URLs",
            "data": {
                "name": "Invalid Tool",
                "website_url": "not-a-url",
                "entity_type_id": "uuid-123",
                "short_description": "Short desc",
                "description": "This is a valid description that meets the minimum length requirements for validation.",
                "logo_url": "invalid-url",
                "pricing_model": "invalid_pricing",
                "price_range": "invalid_range",
                "technical_level": "invalid_level",
                "founded_year": 2050,  # Future year
                "tool_details": {"has_api": True}
            },
            "expected_errors": 6,  # Invalid URLs and enums
            "expected_compliance": 50.0  # Should be medium due to multiple medium/high issues
        },
        {
            "name": "Normalizable Enums",
            "data": {
                "name": "Normalizable Tool",
                "website_url": "https://example.com",
                "entity_type_id": "uuid-123",
                "short_description": "Tool with normalizable enums",
                "description": "This tool has enum values that can be normalized to valid schema values automatically.",
                "pricing_model": "free tier",  # Should normalize to FREEMIUM
                "price_range": "cheap",        # Should normalize to LOW
                "technical_level": "easy",     # Should normalize to BEGINNER
                "employee_count_range": "small", # Should normalize to C1_10
                "funding_stage": "seed round",   # Should normalize to SEED
                "tool_details": {"has_api": True}
            },
            "expected_errors": 0,
            "expected_compliance": 100.0
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Validate and normalize
            normalized_data, errors = validator.validate_and_normalize(
                test_case['data'], 
                entity_type='ai-tool'
            )
            
            # Get validation summary
            summary = validator.get_validation_summary(errors)
            
            print(f"   Errors found: {len(errors)}")
            print(f"   Compliance score: {summary['compliance_score']:.1f}%")
            print(f"   Status: {summary['status']}")
            
            # Show errors by severity
            if errors:
                print(f"   Error breakdown:")
                for severity in ['critical', 'high', 'medium', 'low']:
                    count = summary['by_severity'].get(severity, 0)
                    if count > 0:
                        print(f"     {severity.upper()}: {count}")
                
                # Show first few errors
                print(f"   Sample errors:")
                for error in errors[:3]:
                    print(f"     - {error.field}: {error.message}")
            
            # Check if normalization worked
            if test_case['name'] == "Normalizable Enums":
                print(f"   Normalization results:")
                print(f"     pricing_model: {normalized_data.get('pricing_model')}")
                print(f"     price_range: {normalized_data.get('price_range')}")
                print(f"     technical_level: {normalized_data.get('technical_level')}")
                print(f"     employee_count_range: {normalized_data.get('employee_count_range')}")
                print(f"     funding_stage: {normalized_data.get('funding_stage')}")
            
            # Validate test expectations
            error_count_match = len(errors) == test_case['expected_errors']
            compliance_close = abs(summary['compliance_score'] - test_case['expected_compliance']) <= 10
            
            if error_count_match and compliance_close:
                print(f"   ✅ Test passed")
                passed_tests += 1
            else:
                print(f"   ❌ Test failed")
                print(f"      Expected {test_case['expected_errors']} errors, got {len(errors)}")
                print(f"      Expected ~{test_case['expected_compliance']}% compliance, got {summary['compliance_score']:.1f}%")
            
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
    
    # Overall results
    success_rate = (passed_tests / total_tests * 100)
    print(f"\n📊 SCHEMA VALIDATOR TEST RESULTS:")
    print(f"   Tests passed: {passed_tests}/{total_tests}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("   ✅ Schema validator working well")
    else:
        print("   ❌ Schema validator needs improvement")
    
    return success_rate >= 75

def test_enum_normalization():
    """Test enum normalization specifically"""
    
    print("\n🔄 TESTING ENUM NORMALIZATION")
    print("=" * 50)
    
    validator = SchemaValidator()
    
    # Test enum mappings
    enum_tests = [
        ("pricing_model", "free tier", "FREEMIUM"),
        ("pricing_model", "monthly", "SUBSCRIPTION"),
        ("price_range", "cheap", "LOW"),
        ("price_range", "expensive", "HIGH"),
        ("technical_level", "easy", "BEGINNER"),
        ("technical_level", "complex", "ADVANCED"),
        ("employee_count_range", "startup", "C1_10"),
        ("employee_count_range", "enterprise", "C501_1000"),
        ("funding_stage", "series a", "SERIES_A"),
        ("funding_stage", "ipo", "PUBLIC")
    ]
    
    correct_normalizations = 0
    total_normalizations = len(enum_tests)
    
    for field, input_value, expected_output in enum_tests:
        test_data = {
            "name": "Test Tool",
            "website_url": "https://example.com",
            "entity_type_id": "uuid-123",
            "short_description": "Test description",
            "description": "This is a test description that meets the minimum length requirements.",
            field: input_value,
            "tool_details": {"has_api": True}
        }
        
        normalized_data, errors = validator.validate_and_normalize(test_data)
        actual_output = normalized_data.get(field)
        
        if actual_output == expected_output:
            print(f"   ✅ {field}: '{input_value}' -> '{actual_output}'")
            correct_normalizations += 1
        else:
            print(f"   ❌ {field}: '{input_value}' -> '{actual_output}' (expected '{expected_output}')")
    
    normalization_accuracy = (correct_normalizations / total_normalizations * 100)
    print(f"\n   Normalization accuracy: {normalization_accuracy:.1f}% ({correct_normalizations}/{total_normalizations})")
    
    return normalization_accuracy >= 90

def test_validation_performance():
    """Test validation performance with larger datasets"""
    
    print("\n⚡ TESTING VALIDATION PERFORMANCE")
    print("=" * 50)
    
    validator = SchemaValidator()
    
    # Create test data
    test_data = {
        "name": "Performance Test Tool",
        "website_url": "https://example.com",
        "entity_type_id": "uuid-123",
        "short_description": "Performance testing tool",
        "description": "This is a comprehensive description for performance testing that meets all requirements.",
        "logo_url": "https://example.com/logo.png",
        "pricing_model": "freemium",
        "price_range": "medium",
        "technical_level": "intermediate",
        "tool_details": {"has_api": True}
    }
    
    import time
    
    # Test validation speed
    start_time = time.time()
    iterations = 100
    
    for _ in range(iterations):
        normalized_data, errors = validator.validate_and_normalize(test_data)
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / iterations * 1000  # Convert to milliseconds
    
    print(f"   Validated {iterations} items in {total_time:.3f}s")
    print(f"   Average validation time: {avg_time:.2f}ms per item")
    
    # Performance target: < 50ms per validation
    if avg_time < 50:
        print("   ✅ Performance target met (<50ms per validation)")
        return True
    else:
        print("   ⚠️  Performance below target (>50ms per validation)")
        return False

if __name__ == "__main__":
    print("📋 SCHEMA VALIDATION AND STANDARDIZATION TESTING")
    print("=" * 60)
    
    # Run all tests
    validator_success = test_schema_validator()
    normalization_success = test_enum_normalization()
    performance_success = test_validation_performance()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Schema validation: {'✅ PASS' if validator_success else '❌ FAIL'}")
    print(f"   Enum normalization: {'✅ PASS' if normalization_success else '❌ FAIL'}")
    print(f"   Performance: {'✅ PASS' if performance_success else '❌ FAIL'}")
    
    if validator_success and normalization_success and performance_success:
        print("\n   🎉 ALL TESTS PASSED!")
        print("   📈 Schema validator ready for production")
        print("   ✅ 100% schema compliance achievable")
    else:
        print("\n   ⚠️  Some tests failed - needs improvement")
