"""
Schema Validation and Standardization Service
Ensures 100% schema compliance with comprehensive validation and error reporting
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import re
from datetime import datetime

class ValidationSeverity(Enum):
    """Validation error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ValidationError:
    """Represents a validation error"""
    def __init__(self, field: str, message: str, severity: ValidationSeverity, 
                 current_value: Any = None, expected_value: Any = None):
        self.field = field
        self.message = message
        self.severity = severity
        self.current_value = current_value
        self.expected_value = expected_value
        self.timestamp = datetime.now()

class SchemaValidator:
    """
    Comprehensive schema validator with 100% compliance validation
    Ensures all data matches AI Navigator API schema requirements
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Define enum mappings for standardization
        self.enum_mappings = {
            'employee_count_range': {
                'valid_values': ['C1_10', 'C11_50', 'C51_200', 'C201_500', 'C501_1000', 'C1001_5000', 'C5001_PLUS'],
                'mappings': {
                    '1-10': 'C1_10', '1 to 10': 'C1_10', 'small': 'C1_10', 'startup': 'C1_10',
                    '11-50': 'C11_50', '11 to 50': 'C11_50', 'small-medium': 'C11_50',
                    '51-200': 'C51_200', '51 to 200': 'C51_200', 'medium': 'C51_200',
                    '201-500': 'C201_500', '201 to 500': 'C201_500', 'large': 'C201_500',
                    '501-1000': 'C501_1000', '501 to 1000': 'C501_1000', 'enterprise': 'C501_1000',
                    '1001-5000': 'C1001_5000', '1001 to 5000': 'C1001_5000', 'big': 'C1001_5000',
                    '5000+': 'C5001_PLUS', '5001+': 'C5001_PLUS', 'massive': 'C5001_PLUS'
                }
            },
            'funding_stage': {
                'valid_values': ['PRE_SEED', 'SEED', 'SERIES_A', 'SERIES_B', 'SERIES_C', 'SERIES_D_PLUS', 'PUBLIC'],
                'mappings': {
                    'pre-seed': 'PRE_SEED', 'preseed': 'PRE_SEED', 'pre seed': 'PRE_SEED',
                    'seed': 'SEED', 'seed round': 'SEED',
                    'series a': 'SERIES_A', 'series-a': 'SERIES_A', 'a round': 'SERIES_A',
                    'series b': 'SERIES_B', 'series-b': 'SERIES_B', 'b round': 'SERIES_B',
                    'series c': 'SERIES_C', 'series-c': 'SERIES_C', 'c round': 'SERIES_C',
                    'series d': 'SERIES_D_PLUS', 'series-d': 'SERIES_D_PLUS', 'd round': 'SERIES_D_PLUS',
                    'public': 'PUBLIC', 'ipo': 'PUBLIC', 'publicly traded': 'PUBLIC'
                }
            },
            'pricing_model': {
                'valid_values': ['FREE', 'FREEMIUM', 'SUBSCRIPTION', 'PAY_PER_USE', 'ONE_TIME_PURCHASE', 'CONTACT_SALES', 'OPEN_SOURCE'],
                'mappings': {
                    'free': 'FREE', 'no cost': 'FREE', 'gratis': 'FREE',
                    'freemium': 'FREEMIUM', 'free tier': 'FREEMIUM', 'free + paid': 'FREEMIUM',
                    'subscription': 'SUBSCRIPTION', 'monthly': 'SUBSCRIPTION', 'yearly': 'SUBSCRIPTION', 'recurring': 'SUBSCRIPTION',
                    'pay per use': 'PAY_PER_USE', 'pay-per-use': 'PAY_PER_USE', 'usage-based': 'PAY_PER_USE',
                    'one time': 'ONE_TIME_PURCHASE', 'one-time': 'ONE_TIME_PURCHASE', 'single payment': 'ONE_TIME_PURCHASE',
                    'contact sales': 'CONTACT_SALES', 'enterprise': 'CONTACT_SALES', 'custom pricing': 'CONTACT_SALES',
                    'open source': 'OPEN_SOURCE', 'open-source': 'OPEN_SOURCE', 'oss': 'OPEN_SOURCE'
                }
            },
            'price_range': {
                'valid_values': ['FREE', 'LOW', 'MEDIUM', 'HIGH', 'ENTERPRISE'],
                'mappings': {
                    'free': 'FREE', '$0': 'FREE', 'no cost': 'FREE',
                    'low': 'LOW', 'cheap': 'LOW', 'affordable': 'LOW', 'budget': 'LOW',
                    'medium': 'MEDIUM', 'moderate': 'MEDIUM', 'standard': 'MEDIUM', 'mid-range': 'MEDIUM',
                    'high': 'HIGH', 'expensive': 'HIGH', 'premium': 'HIGH', 'costly': 'HIGH',
                    'enterprise': 'ENTERPRISE', 'custom': 'ENTERPRISE', 'contact sales': 'ENTERPRISE'
                }
            },
            'technical_level': {
                'valid_values': ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],
                'mappings': {
                    'beginner': 'BEGINNER', 'easy': 'BEGINNER', 'simple': 'BEGINNER', 'basic': 'BEGINNER',
                    'intermediate': 'INTERMEDIATE', 'medium': 'INTERMEDIATE', 'moderate': 'INTERMEDIATE',
                    'advanced': 'ADVANCED', 'hard': 'ADVANCED', 'complex': 'ADVANCED', 'difficult': 'ADVANCED',
                    'expert': 'EXPERT', 'professional': 'EXPERT', 'enterprise': 'EXPERT', 'research': 'EXPERT'
                }
            },
            'learning_curve': {
                'valid_values': ['LOW', 'MEDIUM', 'HIGH'],
                'mappings': {
                    'low': 'LOW', 'easy': 'LOW', 'quick': 'LOW', 'simple': 'LOW',
                    'medium': 'MEDIUM', 'moderate': 'MEDIUM', 'average': 'MEDIUM',
                    'high': 'HIGH', 'steep': 'HIGH', 'difficult': 'HIGH', 'complex': 'HIGH'
                }
            },
            'affiliate_status': {
                'valid_values': ['NONE', 'APPLIED', 'APPROVED', 'REJECTED'],
                'mappings': {
                    'none': 'NONE', 'no': 'NONE', 'not applied': 'NONE',
                    'applied': 'APPLIED', 'pending': 'APPLIED', 'submitted': 'APPLIED',
                    'approved': 'APPROVED', 'accepted': 'APPROVED', 'active': 'APPROVED',
                    'rejected': 'REJECTED', 'denied': 'REJECTED', 'declined': 'REJECTED'
                }
            }
        }
        
        # Define required fields for different entity types
        self.required_fields = {
            'base': ['name', 'website_url', 'entity_type_id', 'short_description', 'description'],
            'ai-tool': ['tool_details'],
            'research-paper': ['paper_details'],
            'hardware': ['hardware_details'],
            'job': ['job_details'],
            'event': ['event_details']
        }
        
        # Define field type constraints
        self.field_types = {
            'name': str,
            'website_url': str,
            'entity_type_id': str,
            'short_description': str,
            'description': str,
            'logo_url': str,
            'documentation_url': str,
            'contact_url': str,
            'privacy_policy_url': str,
            'founded_year': int,
            'category_ids': list,
            'tag_ids': list,
            'feature_ids': list,
            'social_links': dict
        }
    
    def validate_and_normalize(self, enhanced_data: Dict[str, Any], entity_type: str = 'ai-tool') -> Tuple[Dict[str, Any], List[ValidationError]]:
        """
        Validate and normalize enhanced data against schema
        
        Args:
            enhanced_data: Raw enhanced data
            entity_type: Type of entity being validated
            
        Returns:
            Tuple of (normalized_data, validation_errors)
        """
        
        self.logger.info(f"Validating and normalizing {entity_type} data")
        
        normalized_data = enhanced_data.copy()
        validation_errors = []
        
        # Step 1: Validate required fields
        validation_errors.extend(self._validate_required_fields(normalized_data, entity_type))
        
        # Step 2: Validate and normalize field types
        validation_errors.extend(self._validate_field_types(normalized_data))
        
        # Step 3: Normalize enums
        validation_errors.extend(self._normalize_enums(normalized_data))
        
        # Step 4: Validate URLs
        validation_errors.extend(self._validate_urls(normalized_data))
        
        # Step 5: Validate constraints
        validation_errors.extend(self._validate_constraints(normalized_data, entity_type))
        
        # Step 6: Clean and standardize data
        normalized_data = self._clean_and_standardize(normalized_data)
        
        self.logger.info(f"Validation complete: {len(validation_errors)} errors found")
        return normalized_data, validation_errors
    
    def _validate_required_fields(self, data: Dict[str, Any], entity_type: str) -> List[ValidationError]:
        """Validate that required fields are present"""
        errors = []
        
        # Check base required fields
        for field in self.required_fields['base']:
            if field not in data or not data[field]:
                errors.append(ValidationError(
                    field=field,
                    message=f"Required field '{field}' is missing or empty",
                    severity=ValidationSeverity.CRITICAL,
                    current_value=data.get(field),
                    expected_value="Non-empty value"
                ))
        
        # Check entity-specific required fields
        if entity_type in self.required_fields:
            for field in self.required_fields[entity_type]:
                if field not in data or not data[field]:
                    errors.append(ValidationError(
                        field=field,
                        message=f"Required {entity_type} field '{field}' is missing or empty",
                        severity=ValidationSeverity.HIGH,
                        current_value=data.get(field),
                        expected_value="Non-empty value"
                    ))
        
        return errors
    
    def _validate_field_types(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate field types match expected types"""
        errors = []
        
        for field, expected_type in self.field_types.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    errors.append(ValidationError(
                        field=field,
                        message=f"Field '{field}' has incorrect type",
                        severity=ValidationSeverity.MEDIUM,
                        current_value=f"{type(data[field]).__name__}: {data[field]}",
                        expected_value=f"{expected_type.__name__}"
                    ))
        
        return errors
    
    def _normalize_enums(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Normalize enum values to match schema"""
        errors = []
        
        for field, enum_config in self.enum_mappings.items():
            if field in data and data[field] is not None:
                current_value = str(data[field]).lower().strip()
                
                # Check if already valid
                if data[field] in enum_config['valid_values']:
                    continue
                
                # Try to map to valid value
                if current_value in enum_config['mappings']:
                    old_value = data[field]
                    data[field] = enum_config['mappings'][current_value]
                    self.logger.debug(f"Normalized {field}: '{old_value}' -> '{data[field]}'")
                else:
                    # Invalid enum value
                    errors.append(ValidationError(
                        field=field,
                        message=f"Invalid enum value for '{field}'",
                        severity=ValidationSeverity.HIGH,
                        current_value=data[field],
                        expected_value=enum_config['valid_values']
                    ))
        
        return errors
    
    def _validate_urls(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate URL fields"""
        errors = []
        
        url_fields = ['website_url', 'logo_url', 'documentation_url', 'contact_url', 'privacy_policy_url']
        
        for field in url_fields:
            if field in data and data[field]:
                url = data[field]
                if not self._is_valid_url(url):
                    errors.append(ValidationError(
                        field=field,
                        message=f"Invalid URL format for '{field}'",
                        severity=ValidationSeverity.MEDIUM,
                        current_value=url,
                        expected_value="Valid HTTP/HTTPS URL"
                    ))
        
        return errors
    
    def _validate_constraints(self, data: Dict[str, Any], entity_type: str) -> List[ValidationError]:
        """Validate field constraints and business rules"""
        errors = []
        
        # Validate description length
        if 'description' in data and data['description']:
            desc_length = len(data['description'])
            if desc_length < 50:
                errors.append(ValidationError(
                    field='description',
                    message="Description too short",
                    severity=ValidationSeverity.MEDIUM,
                    current_value=f"{desc_length} characters",
                    expected_value="At least 50 characters"
                ))
            elif desc_length > 2000:
                errors.append(ValidationError(
                    field='description',
                    message="Description too long",
                    severity=ValidationSeverity.LOW,
                    current_value=f"{desc_length} characters",
                    expected_value="At most 2000 characters"
                ))
        
        # Validate short description length
        if 'short_description' in data and data['short_description']:
            short_desc_length = len(data['short_description'])
            if short_desc_length > 200:
                errors.append(ValidationError(
                    field='short_description',
                    message="Short description too long",
                    severity=ValidationSeverity.MEDIUM,
                    current_value=f"{short_desc_length} characters",
                    expected_value="At most 200 characters"
                ))
        
        # Validate founded year
        if 'founded_year' in data and data['founded_year']:
            current_year = datetime.now().year
            if data['founded_year'] < 1900 or data['founded_year'] > current_year:
                errors.append(ValidationError(
                    field='founded_year',
                    message="Invalid founded year",
                    severity=ValidationSeverity.MEDIUM,
                    current_value=data['founded_year'],
                    expected_value=f"Between 1900 and {current_year}"
                ))
        
        return errors
    
    def _clean_and_standardize(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and standardize data"""
        
        # Clean string fields
        string_fields = ['name', 'short_description', 'description']
        for field in string_fields:
            if field in data and isinstance(data[field], str):
                data[field] = data[field].strip()
        
        # Ensure arrays are not None
        array_fields = ['category_ids', 'tag_ids', 'feature_ids']
        for field in array_fields:
            if field not in data or data[field] is None:
                data[field] = []
        
        # Set default affiliate status
        if 'affiliate_status' not in data:
            data['affiliate_status'] = 'NONE'
        
        return data
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def get_validation_summary(self, errors: List[ValidationError]) -> Dict[str, Any]:
        """Get validation summary statistics"""
        
        if not errors:
            return {
                'total_errors': 0,
                'by_severity': {},
                'compliance_score': 100.0,
                'status': 'VALID'
            }
        
        by_severity = {}
        for error in errors:
            severity = error.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # Calculate compliance score (critical errors = 0%, high = 20%, medium = 50%, low = 80%)
        critical_count = by_severity.get('critical', 0)
        high_count = by_severity.get('high', 0)
        medium_count = by_severity.get('medium', 0)
        low_count = by_severity.get('low', 0)
        
        if critical_count > 0:
            compliance_score = 0.0
        elif high_count > 0:
            compliance_score = max(0, 80 - (high_count * 10))
        elif medium_count > 0:
            compliance_score = max(60, 90 - (medium_count * 5))
        else:
            compliance_score = max(80, 95 - (low_count * 2))
        
        status = 'VALID' if compliance_score >= 95 else 'NEEDS_IMPROVEMENT' if compliance_score >= 70 else 'INVALID'
        
        return {
            'total_errors': len(errors),
            'by_severity': by_severity,
            'compliance_score': compliance_score,
            'status': status
        }
