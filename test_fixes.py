#!/usr/bin/env python3
"""
Test script for the specific fixes:
1. Entity existence check (should work now)
2. RefLink using actual website URL (should work)
3. Features taxonomy system (feature_ids should be populated)
4. Improved tags taxonomy classification (should avoid Cloud-Based)
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_single_tool():
    """Test with a single tool to verify all fixes"""
    
    test_tool = {
        "name": "Test Fix Tool",
        "url": "https://example.com/test-fix-tool"
    }
    
    logger.info("🧪 Testing single tool with all fixes...")
    
    # Send request to enhanced scraping endpoint
    response = requests.post(
        f"{BASE_URL}/api/start-enhanced-scraping",
        json={
            "tools": [test_tool],
            "use_parallel": False,
            "use_phase3": True
        }
    )
    
    if response.status_code != 200:
        logger.error(f"❌ Failed to start enhanced scraping: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return None
    
    result = response.json()
    job_id = result.get('job_id')
    logger.info(f"✅ Enhanced scraping started with job ID: {job_id}")
    
    # Monitor job
    max_attempts = 30  # 2.5 minutes max
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            
            if response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {response.status_code}")
                break
            
            status = response.json()
            progress = status.get('progress', 0)
            state = status.get('status', 'unknown')
            
            logger.info(f"📈 Job {job_id}: {state} - {progress:.1f}% complete")
            
            if state in ['completed', 'failed']:
                logger.info(f"🏁 Job {job_id} finished with state: {state}")
                
                # Analyze results
                results = status.get('results', [])
                errors = status.get('errors', [])
                
                logger.info(f"📊 Results: {len(results)} items, {len(errors)} errors")
                
                if results:
                    result = results[0]
                    logger.info("🔍 Analyzing first result:")
                    
                    # Check refLink
                    ref_link = result.get('ref_link', '')
                    if ref_link and not any(redirect in ref_link for redirect in ['futuretools.link', 'redirect']):
                        logger.info(f"✅ RefLink fix: {ref_link}")
                    else:
                        logger.warning(f"⚠️  RefLink issue: {ref_link}")
                    
                    # Check feature_ids
                    feature_ids = result.get('feature_ids', [])
                    logger.info(f"✅ Feature IDs populated: {len(feature_ids)} features")
                    
                    # Check tags
                    tag_ids = result.get('tag_ids', [])
                    logger.info(f"✅ Tag IDs populated: {len(tag_ids)} tags")
                    
                    # Check if entity was saved successfully
                    if 'id' in result:
                        logger.info(f"✅ Entity saved successfully with ID: {result['id']}")
                    else:
                        logger.warning("⚠️  Entity may not have been saved")
                
                if errors:
                    logger.warning("⚠️  Errors encountered:")
                    for error in errors[:3]:  # Show first 3 errors
                        logger.warning(f"   ❌ {error}")
                
                return status
            
            time.sleep(5)  # Wait 5 seconds between checks
            attempt += 1
            
        except Exception as e:
            logger.error(f"❌ Error monitoring job: {str(e)}")
            break
    
    logger.warning(f"⏰ Job monitoring timed out")
    return None

def main():
    """Main test function"""
    logger.info("🧪 Starting AI Navigator Scrapers Fixes Test")
    logger.info("=" * 50)
    
    final_status = test_single_tool()
    
    if final_status:
        logger.info("✅ Test completed successfully!")
    else:
        logger.error("❌ Test failed!")
    
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
