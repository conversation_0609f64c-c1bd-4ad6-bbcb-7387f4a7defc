#!/usr/bin/env python3
"""
Comprehensive test for frontend-backend integration
Tests both enhance button and scraper functionality
"""

import requests
import json
import time
import sys

def test_backend_health():
    """Test backend health endpoint"""
    print("🔍 Testing Backend Health...")
    try:
        response = requests.get("http://localhost:8001/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Backend is healthy")
            print(f"   - Traditional scraping: {data.get('traditional_scraping')}")
            print(f"   - Enhanced scraping: {data.get('enhanced_scraping')}")
            print(f"   - Phase 3 components: {data.get('phase3_components')}")
            return True
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Health check error: {str(e)}")
        return False

def test_spiders_endpoint():
    """Test spiders endpoint"""
    print("\n🕷️ Testing Spiders Endpoint...")
    try:
        response = requests.get("http://localhost:8001/api/spiders")
        if response.status_code == 200:
            data = response.json()
            spiders = data.get('spiders', [])
            print(f"   ✅ Found {len(spiders)} spiders")
            for spider in spiders:
                print(f"   - {spider}")
            return len(spiders) > 0
        else:
            print(f"   ❌ Spiders endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Spiders endpoint error: {str(e)}")
        return False

def test_enhanced_scraping():
    """Test enhanced scraping (enhance button functionality)"""
    print("\n🚀 Testing Enhanced Scraping (Enhance Button)...")
    try:
        job_request = {
            "tools": [
                {"name": "ChatGPT", "url": "https://chat.openai.com"},
                {"name": "Test Tool", "url": "https://www.test.co"}
            ],
            "use_parallel": True,
            "use_phase3": True
        }
        
        response = requests.post("http://localhost:8001/api/start-enhanced-scraping", json=job_request)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                job_id = data.get('job_id')
                print(f"   ✅ Enhanced scraping job started successfully")
                print(f"   - Job ID: {job_id}")
                print(f"   - Total tools: {data.get('total_tools')}")
                print(f"   - Processing mode: {data.get('processing_mode')}")
                print(f"   - Phase 3 enabled: {data.get('phase3_enabled')}")
                print(f"   - Estimated time: {data.get('estimated_time', 0):.1f}s")
                return job_id
            else:
                print(f"   ❌ Enhanced scraping failed: {data.get('message')}")
                return None
        else:
            print(f"   ❌ Enhanced scraping request failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"   ❌ Enhanced scraping error: {str(e)}")
        return None

def test_traditional_scraping():
    """Test traditional scraping (scraper starting functionality)"""
    print("\n🔧 Testing Traditional Scraping (Start Scraping)...")
    try:
        job_request = {
            "spider_name": "futuretools_complete",
            "max_items": 3
        }
        
        response = requests.post("http://localhost:8001/api/start-scraping", json=job_request)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                job_id = data.get('job_id')
                print(f"   ✅ Traditional scraping job started successfully")
                print(f"   - Job ID: {job_id}")
                print(f"   - Spider: {data.get('spider_name')}")
                print(f"   - Max items: {data.get('max_items')}")
                return job_id
            else:
                print(f"   ❌ Traditional scraping failed: {data.get('message')}")
                return None
        else:
            print(f"   ❌ Traditional scraping request failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"   ❌ Traditional scraping error: {str(e)}")
        return None

def test_job_status(job_id, job_type="enhanced"):
    """Test job status endpoint"""
    print(f"\n📊 Testing Job Status for {job_type} job...")
    try:
        response = requests.get(f"http://localhost:8001/api/job-status/{job_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Job status retrieved successfully")
            print(f"   - Status: {data.get('status')}")
            print(f"   - Progress: {data.get('progress', 0):.1f}%")
            if job_type == "enhanced":
                print(f"   - Total tools: {data.get('total_tools')}")
                print(f"   - Phase 3 enabled: {data.get('phase3_enabled')}")
            return True
        else:
            print(f"   ❌ Job status request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Job status error: {str(e)}")
        return False

def test_capabilities():
    """Test capabilities endpoint"""
    print("\n⚙️ Testing Capabilities Endpoint...")
    try:
        response = requests.get("http://localhost:8001/api/capabilities")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Capabilities retrieved successfully")
            print(f"   - Traditional scraping: {data.get('traditional_scraping')}")
            print(f"   - Enhanced scraping: {data.get('enhanced_scraping')}")
            phase3 = data.get('phase3_features', {})
            print(f"   - Phase 3 features:")
            for feature, enabled in phase3.items():
                print(f"     * {feature}: {enabled}")
            return True
        else:
            print(f"   ❌ Capabilities request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Capabilities error: {str(e)}")
        return False

def main():
    """Run comprehensive integration test"""
    print("🧪 AI Navigator Scrapers - Frontend/Backend Integration Test")
    print("=" * 60)
    
    # Test backend health
    if not test_backend_health():
        print("\n❌ Backend health check failed. Make sure backend is running on port 8001.")
        sys.exit(1)
    
    # Test spiders endpoint
    if not test_spiders_endpoint():
        print("\n❌ Spiders endpoint failed.")
        sys.exit(1)
    
    # Test capabilities
    if not test_capabilities():
        print("\n❌ Capabilities endpoint failed.")
        sys.exit(1)
    
    # Test enhanced scraping (enhance button)
    enhanced_job_id = test_enhanced_scraping()
    if not enhanced_job_id:
        print("\n❌ Enhanced scraping test failed.")
        sys.exit(1)
    
    # Test traditional scraping (start scraping)
    traditional_job_id = test_traditional_scraping()
    if not traditional_job_id:
        print("\n❌ Traditional scraping test failed.")
        sys.exit(1)
    
    # Test job status for both jobs
    time.sleep(2)  # Give jobs a moment to start
    
    if not test_job_status(enhanced_job_id, "enhanced"):
        print("\n❌ Enhanced job status test failed.")
        sys.exit(1)
    
    if not test_job_status(traditional_job_id, "traditional"):
        print("\n❌ Traditional job status test failed.")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("✅ Enhance button functionality: WORKING")
    print("✅ Start scraping functionality: WORKING")
    print("✅ Backend API endpoints: WORKING")
    print("✅ Frontend should be fully functional")
    print("\n🌐 Frontend URL: http://localhost:3000")
    print("🔧 Backend URL: http://localhost:8001")

if __name__ == "__main__":
    main()
