"""
Dynamic Prompt Optimization
Context-aware prompt generation with performance tracking and quality-based adaptation
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import re

class PromptType(Enum):
    """Types of prompts for different enhancement tasks"""
    BASIC_ENHANCEMENT = "basic_enhancement"
    TECHNICAL_CLASSIFICATION = "technical_classification"
    FEATURE_EXTRACTION = "feature_extraction"
    PRICING_ANALYSIS = "pricing_analysis"
    COMPANY_RESEARCH = "company_research"

class PromptComplexity(Enum):
    """Prompt complexity levels"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

@dataclass
class PromptTemplate:
    """Template for generating prompts"""
    prompt_type: PromptType
    complexity: PromptComplexity
    template: str
    expected_tokens: int
    success_rate: float = 0.0
    avg_quality_score: float = 0.0
    usage_count: int = 0

@dataclass
class PromptPerformance:
    """Performance metrics for a prompt"""
    prompt_id: str
    success: bool
    quality_score: float
    response_time: float
    tokens_used: int
    timestamp: float

class DynamicPromptOptimizer:
    """
    Context-aware prompt generation with performance tracking
    Adapts prompts based on quality and success metrics
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Prompt templates library
        self.prompt_templates = {}
        self.performance_history = []
        self.context_adaptations = {}
        
        # Performance thresholds
        self.min_success_rate = 0.8
        self.min_quality_score = 0.7
        self.adaptation_threshold = 10  # Minimum attempts before adaptation
        
        # Initialize default templates
        self._initialize_prompt_templates()
    
    def _initialize_prompt_templates(self):
        """Initialize default prompt templates"""
        
        # Basic Enhancement Templates
        self.add_prompt_template(PromptTemplate(
            prompt_type=PromptType.BASIC_ENHANCEMENT,
            complexity=PromptComplexity.SIMPLE,
            template="""
            Analyze "{tool_name}" from {website_url}:
            
            Description: {description}
            
            Provide JSON with: short_description, key_features, pricing_model, technical_level.
            """,
            expected_tokens=200
        ))
        
        self.add_prompt_template(PromptTemplate(
            prompt_type=PromptType.BASIC_ENHANCEMENT,
            complexity=PromptComplexity.DETAILED,
            template="""
            Comprehensive analysis of "{tool_name}" (Website: {website_url}):
            
            Current Description: {description}
            
            Research and provide detailed information in JSON format:
            {{
                "short_description": "Compelling 1-2 sentence description",
                "description": "Comprehensive 3-4 paragraph description",
                "key_features": ["feature1", "feature2", "feature3", "feature4", "feature5"],
                "use_cases": ["use_case1", "use_case2", "use_case3"],
                "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES|OPEN_SOURCE",
                "price_range": "FREE|LOW|MEDIUM|HIGH|ENTERPRISE",
                "technical_level": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
                "target_audience": ["audience1", "audience2", "audience3"],
                "has_free_tier": true/false,
                "has_api": true/false,
                "integrations": ["integration1", "integration2"],
                "founded_year": year,
                "employee_count_range": "C1_10|C11_50|C51_200|C201_500|C501_1000|C1001_5000|C5001_PLUS"
            }}
            
            Return only valid JSON.
            """,
            expected_tokens=500
        ))
        
        # Technical Classification Templates
        self.add_prompt_template(PromptTemplate(
            prompt_type=PromptType.TECHNICAL_CLASSIFICATION,
            complexity=PromptComplexity.SIMPLE,
            template="""
            Classify the technical difficulty of "{tool_name}":
            
            Description: {description}
            Features: {key_features}
            
            Return: BEGINNER, INTERMEDIATE, ADVANCED, or EXPERT
            
            BEGINNER: No-code, drag-and-drop, anyone can use
            INTERMEDIATE: Some technical knowledge, API usage, basic configuration
            ADVANCED: Programming skills, complex setup, custom development
            EXPERT: Deep technical expertise, research-level, specialized knowledge
            """,
            expected_tokens=100
        ))
        
        # Feature Extraction Templates
        self.add_prompt_template(PromptTemplate(
            prompt_type=PromptType.FEATURE_EXTRACTION,
            complexity=PromptComplexity.DETAILED,
            template="""
            Extract key features from "{tool_name}":
            
            Description: {description}
            Website: {website_url}
            
            Identify 5-8 key features that make this tool unique and valuable.
            Focus on:
            - Core functionality
            - AI/ML capabilities
            - Integration options
            - User experience features
            - Technical capabilities
            
            Return as JSON array: ["feature1", "feature2", "feature3", ...]
            """,
            expected_tokens=300
        ))
        
        # Pricing Analysis Templates
        self.add_prompt_template(PromptTemplate(
            prompt_type=PromptType.PRICING_ANALYSIS,
            complexity=PromptComplexity.COMPREHENSIVE,
            template="""
            Analyze pricing for "{tool_name}" from {website_url}:
            
            Research the current pricing structure and provide:
            {{
                "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES|OPEN_SOURCE",
                "price_range": "FREE|LOW|MEDIUM|HIGH|ENTERPRISE",
                "has_free_tier": true/false,
                "pricing_details": "Specific pricing information",
                "trial_available": true/false
            }}
            
            Price ranges:
            - FREE: $0
            - LOW: $1-50/month
            - MEDIUM: $51-200/month  
            - HIGH: $201-1000/month
            - ENTERPRISE: $1000+/month or custom pricing
            """,
            expected_tokens=250
        ))
    
    def add_prompt_template(self, template: PromptTemplate):
        """Add a prompt template to the library"""
        template_id = f"{template.prompt_type.value}_{template.complexity.value}"
        self.prompt_templates[template_id] = template
        self.logger.debug(f"Added prompt template: {template_id}")
    
    def generate_optimized_prompt(self, prompt_type: PromptType, context: Dict[str, Any], 
                                complexity: Optional[PromptComplexity] = None) -> Tuple[str, str]:
        """
        Generate optimized prompt based on context and performance history
        
        Args:
            prompt_type: Type of prompt needed
            context: Context data for prompt generation
            complexity: Desired complexity level (auto-selected if None)
            
        Returns:
            Tuple of (prompt_text, prompt_id)
        """
        
        # Auto-select complexity if not specified
        if complexity is None:
            complexity = self._select_optimal_complexity(prompt_type, context)
        
        # Get best performing template
        template = self._get_best_template(prompt_type, complexity)
        
        # Generate prompt from template
        prompt_text = self._render_template(template, context)
        
        # Apply context-specific adaptations
        prompt_text = self._apply_context_adaptations(prompt_text, context)
        
        prompt_id = f"{template.prompt_type.value}_{template.complexity.value}_{int(time.time())}"
        
        self.logger.info(f"Generated optimized prompt: {prompt_id}")
        return prompt_text, prompt_id
    
    def _select_optimal_complexity(self, prompt_type: PromptType, context: Dict[str, Any]) -> PromptComplexity:
        """Select optimal complexity based on context and performance"""
        
        # Check if we have performance data for this prompt type
        type_performance = [p for p in self.performance_history 
                          if prompt_type.value in p.prompt_id]
        
        if len(type_performance) < self.adaptation_threshold:
            # Not enough data, use heuristics
            return self._heuristic_complexity_selection(context)
        
        # Analyze performance by complexity
        complexity_scores = {}
        for complexity in PromptComplexity:
            complexity_performance = [p for p in type_performance 
                                    if complexity.value in p.prompt_id]
            
            if complexity_performance:
                avg_quality = sum(p.quality_score for p in complexity_performance) / len(complexity_performance)
                success_rate = sum(1 for p in complexity_performance if p.success) / len(complexity_performance)
                
                # Combined score (quality * success_rate)
                complexity_scores[complexity] = avg_quality * success_rate
        
        if complexity_scores:
            best_complexity = max(complexity_scores, key=complexity_scores.get)
            self.logger.info(f"Selected {best_complexity.value} based on performance")
            return best_complexity
        
        return self._heuristic_complexity_selection(context)
    
    def _heuristic_complexity_selection(self, context: Dict[str, Any]) -> PromptComplexity:
        """Select complexity using heuristics when no performance data available"""
        
        # Check context richness
        description_length = len(context.get('description', ''))
        has_features = bool(context.get('key_features'))
        has_url = bool(context.get('website_url'))
        
        # Rich context can handle comprehensive prompts
        if description_length > 200 and has_features and has_url:
            return PromptComplexity.COMPREHENSIVE
        elif description_length > 50 and has_url:
            return PromptComplexity.DETAILED
        else:
            return PromptComplexity.SIMPLE
    
    def _get_best_template(self, prompt_type: PromptType, complexity: PromptComplexity) -> PromptTemplate:
        """Get the best performing template for given type and complexity"""
        
        template_id = f"{prompt_type.value}_{complexity.value}"
        
        if template_id in self.prompt_templates:
            return self.prompt_templates[template_id]
        
        # Fallback to any template of the same type
        fallback_templates = [t for t in self.prompt_templates.values() 
                            if t.prompt_type == prompt_type]
        
        if fallback_templates:
            # Return the one with highest success rate
            return max(fallback_templates, key=lambda t: t.success_rate)
        
        # Create a basic fallback template
        return PromptTemplate(
            prompt_type=prompt_type,
            complexity=complexity,
            template="Analyze {tool_name}: {description}. Provide relevant information.",
            expected_tokens=100
        )
    
    def _render_template(self, template: PromptTemplate, context: Dict[str, Any]) -> str:
        """Render template with context data"""
        
        try:
            # Ensure required fields have defaults
            safe_context = {
                'tool_name': context.get('tool_name', 'Unknown Tool'),
                'website_url': context.get('website_url', 'No URL provided'),
                'description': context.get('description', 'No description available'),
                'key_features': context.get('key_features', []),
                **context  # Include all other context fields
            }
            
            # Format key_features as string if it's a list
            if isinstance(safe_context['key_features'], list):
                safe_context['key_features'] = ', '.join(safe_context['key_features'])
            
            return template.template.format(**safe_context)
            
        except KeyError as e:
            self.logger.error(f"Missing context field for template: {e}")
            # Return a basic prompt as fallback
            return f"Analyze {context.get('tool_name', 'this tool')}: {context.get('description', 'No description')}"
    
    def _apply_context_adaptations(self, prompt: str, context: Dict[str, Any]) -> str:
        """Apply context-specific adaptations to the prompt"""
        
        # Add entity type specific instructions
        entity_type = context.get('entity_type', 'ai-tool')
        
        if entity_type == 'research-paper':
            prompt += "\n\nFocus on academic and research aspects."
        elif entity_type == 'hardware':
            prompt += "\n\nEmphasize technical specifications and performance."
        elif entity_type == 'job':
            prompt += "\n\nHighlight role requirements and company information."
        
        # Add industry-specific context
        if 'machine learning' in context.get('description', '').lower():
            prompt += "\n\nConsider ML/AI specific features and technical requirements."
        
        return prompt
    
    def record_performance(self, prompt_id: str, success: bool, quality_score: float, 
                         response_time: float, tokens_used: int):
        """Record performance metrics for a prompt"""
        
        performance = PromptPerformance(
            prompt_id=prompt_id,
            success=success,
            quality_score=quality_score,
            response_time=response_time,
            tokens_used=tokens_used,
            timestamp=time.time()
        )
        
        self.performance_history.append(performance)
        
        # Update template statistics
        self._update_template_stats(prompt_id, success, quality_score)
        
        # Trigger adaptation if needed
        self._check_adaptation_triggers(prompt_id)
        
        self.logger.debug(f"Recorded performance for {prompt_id}: success={success}, quality={quality_score:.2f}")
    
    def _update_template_stats(self, prompt_id: str, success: bool, quality_score: float):
        """Update template statistics"""
        
        # Extract template info from prompt_id
        parts = prompt_id.split('_')
        if len(parts) >= 2:
            template_id = f"{parts[0]}_{parts[1]}"
            
            if template_id in self.prompt_templates:
                template = self.prompt_templates[template_id]
                
                # Update running averages
                template.usage_count += 1
                
                # Update success rate
                old_success_rate = template.success_rate
                template.success_rate = ((old_success_rate * (template.usage_count - 1)) + (1 if success else 0)) / template.usage_count
                
                # Update quality score
                old_quality = template.avg_quality_score
                template.avg_quality_score = ((old_quality * (template.usage_count - 1)) + quality_score) / template.usage_count
    
    def _check_adaptation_triggers(self, prompt_id: str):
        """Check if prompt adaptation is needed"""
        
        # Get recent performance for this prompt type
        prompt_type = prompt_id.split('_')[0]
        recent_performance = [p for p in self.performance_history[-20:] 
                            if prompt_type in p.prompt_id]
        
        if len(recent_performance) >= self.adaptation_threshold:
            avg_quality = sum(p.quality_score for p in recent_performance) / len(recent_performance)
            success_rate = sum(1 for p in recent_performance if p.success) / len(recent_performance)
            
            # Trigger adaptation if performance is below threshold
            if avg_quality < self.min_quality_score or success_rate < self.min_success_rate:
                self.logger.warning(f"Performance below threshold for {prompt_type}, triggering adaptation")
                self._adapt_prompts(prompt_type)
    
    def _adapt_prompts(self, prompt_type: str):
        """Adapt prompts based on performance feedback"""
        
        # This is a simplified adaptation - in a full implementation,
        # this could involve more sophisticated prompt engineering
        
        adaptation_key = f"{prompt_type}_adaptation_{int(time.time())}"
        self.context_adaptations[adaptation_key] = {
            'timestamp': time.time(),
            'adaptation_type': 'performance_based',
            'prompt_type': prompt_type
        }
        
        self.logger.info(f"Applied adaptation for {prompt_type}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        
        if not self.performance_history:
            return {'message': 'No performance data available'}
        
        # Overall statistics
        total_attempts = len(self.performance_history)
        successful_attempts = sum(1 for p in self.performance_history if p.success)
        avg_quality = sum(p.quality_score for p in self.performance_history) / total_attempts
        avg_response_time = sum(p.response_time for p in self.performance_history) / total_attempts
        
        # Performance by prompt type
        type_performance = {}
        for prompt_type in PromptType:
            type_history = [p for p in self.performance_history 
                          if prompt_type.value in p.prompt_id]
            
            if type_history:
                type_performance[prompt_type.value] = {
                    'attempts': len(type_history),
                    'success_rate': sum(1 for p in type_history if p.success) / len(type_history) * 100,
                    'avg_quality': sum(p.quality_score for p in type_history) / len(type_history),
                    'avg_response_time': sum(p.response_time for p in type_history) / len(type_history)
                }
        
        return {
            'total_attempts': total_attempts,
            'overall_success_rate': successful_attempts / total_attempts * 100,
            'avg_quality_score': avg_quality,
            'avg_response_time': avg_response_time,
            'performance_by_type': type_performance,
            'adaptations_applied': len(self.context_adaptations),
            'template_count': len(self.prompt_templates)
        }
