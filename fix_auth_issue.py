#!/usr/bin/env python3
"""
Fix JWT Authentication Issue
This script will:
1. Force a fresh login to get a new JWT token
2. Test the authentication
3. Verify API access
"""

import requests
import json
import logging
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_navigator_client import AINavigatorClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_direct_login():
    """Test direct login to AI Navigator API"""
    logger.info("🔐 Testing direct login to AI Navigator API...")
    
    # Use the same credentials as the client
    login_data = {
        "email": "<EMAIL>",
        "password": "testtest"
    }
    
    base_url = "https://ai-nav.onrender.com"
    
    try:
        response = requests.post(
            f"{base_url}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        logger.info(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ Direct login successful!")
            
            session = data.get('session', {})
            access_token = session.get('access_token')
            expires_in = session.get('expires_in', 3600)
            
            if access_token:
                logger.info(f"✅ Access token received (expires in {expires_in} seconds)")
                logger.info(f"Token preview: {access_token[:20]}...")
                return access_token
            else:
                logger.error("❌ No access token in response")
                logger.error(f"Session data: {json.dumps(session, indent=2)}")
        else:
            logger.error(f"❌ Login failed with status {response.status_code}")
            try:
                error_data = response.json()
                logger.error(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                logger.error(f"Error text: {response.text}")
    
    except Exception as e:
        logger.error(f"❌ Exception during direct login: {str(e)}")
    
    return None

def test_api_access(access_token):
    """Test API access with the token"""
    logger.info("🧪 Testing API access with token...")
    
    base_url = "https://ai-nav.onrender.com"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test a simple API endpoint
    try:
        response = requests.get(
            f"{base_url}/entities",
            headers=headers,
            params={"limit": 1},
            timeout=10
        )
        
        logger.info(f"API test response status: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ API access successful!")
            data = response.json()
            logger.info(f"API response preview: {str(data)[:100]}...")
            return True
        else:
            logger.error(f"❌ API access failed with status {response.status_code}")
            try:
                error_data = response.json()
                logger.error(f"API error response: {json.dumps(error_data, indent=2)}")
            except:
                logger.error(f"API error text: {response.text}")
    
    except Exception as e:
        logger.error(f"❌ Exception during API test: {str(e)}")
    
    return False

def test_client_authentication():
    """Test the AINavigatorClient authentication"""
    logger.info("🔧 Testing AINavigatorClient authentication...")
    
    try:
        # Create a fresh client instance
        client = AINavigatorClient()
        
        # Force token refresh
        logger.info("Forcing token refresh...")
        success = client._refresh_token()
        
        if success:
            logger.info("✅ Client token refresh successful!")
            logger.info(f"Token preview: {client.access_token[:20] if client.access_token else 'None'}...")
            
            # Test a simple API call
            logger.info("Testing client API call...")
            headers = client._get_headers()
            
            response = requests.get(
                f"{client.base_url}/entities",
                headers=headers,
                params={"limit": 1},
                timeout=10
            )
            
            logger.info(f"Client API test status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ Client API access successful!")
                return True
            else:
                logger.error(f"❌ Client API access failed: {response.status_code}")
                try:
                    error_data = response.json()
                    logger.error(f"Client API error: {json.dumps(error_data, indent=2)}")
                except:
                    logger.error(f"Client API error text: {response.text}")
        else:
            logger.error("❌ Client token refresh failed!")
    
    except Exception as e:
        logger.error(f"❌ Exception testing client: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
    
    return False

def main():
    """Main function to diagnose and fix auth issues"""
    logger.info("🚀 Starting JWT Authentication Diagnosis and Fix")
    logger.info("=" * 60)
    
    # Step 1: Test direct login
    access_token = test_direct_login()
    
    if access_token:
        # Step 2: Test API access with direct token
        api_success = test_api_access(access_token)
        
        if api_success:
            logger.info("✅ Direct authentication working!")
        else:
            logger.error("❌ Direct API access failed even with valid token")
    else:
        logger.error("❌ Direct login failed - check credentials")
    
    logger.info("-" * 60)
    
    # Step 3: Test client authentication
    client_success = test_client_authentication()
    
    if client_success:
        logger.info("✅ Client authentication working!")
    else:
        logger.error("❌ Client authentication failed")
    
    logger.info("=" * 60)
    
    if access_token and client_success:
        logger.info("🎉 Authentication issue resolved!")
        logger.info("You can now run your scrapers again.")
    else:
        logger.error("❌ Authentication issue persists")
        logger.error("Possible solutions:")
        logger.error("1. Check if AI Navigator API is accessible")
        logger.error("2. Verify admin credentials are correct")
        logger.error("3. Check if there are any API changes")

if __name__ == "__main__":
    main()
