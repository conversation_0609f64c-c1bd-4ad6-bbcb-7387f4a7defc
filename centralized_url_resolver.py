"""
Centralized URL Resolution Service
Handles all URL resolution, redirect following, canonical URL detection, and tracking parameter removal
"""

import logging
import re
import requests
from typing import Optional, Dict, List
from urllib.parse import urljoin, urlparse, parse_qs, urlunparse
from bs4 import BeautifulSoup
import time

class CentralizedURLResolver:
    """Centralized service for robust URL resolution and redirect following"""
    
    def __init__(self, timeout: int = 15, max_redirects: int = 10):
        self.logger = logging.getLogger(__name__)
        self.timeout = timeout
        self.max_redirects = max_redirects
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def resolve_final_url(self, url: str, follow_meta_refresh: bool = True) -> str:
        """
        Resolve URL to its final destination after all redirects
        
        Args:
            url: Initial URL to resolve
            follow_meta_refresh: Whether to follow meta refresh redirects
            
        Returns:
            Final resolved URL
        """
        try:
            self.logger.info(f"Resolving URL: {url}")
            
            # Step 1: Follow HTTP redirects
            final_url = self._follow_http_redirects(url)
            
            # Step 2: Check for meta refresh redirects if enabled
            if follow_meta_refresh and final_url:
                meta_refresh_url = self._check_meta_refresh(final_url)
                if meta_refresh_url:
                    final_url = meta_refresh_url
            
            # Step 3: Get canonical URL if available
            canonical_url = self._get_canonical_url(final_url)
            if canonical_url:
                final_url = canonical_url
            
            # Step 4: Clean tracking parameters
            final_url = self._clean_tracking_parameters(final_url)
            
            self.logger.info(f"Final resolved URL: {final_url}")
            return final_url
            
        except Exception as e:
            self.logger.error(f"Error resolving URL {url}: {str(e)}")
            return url
    
    def _follow_http_redirects(self, url: str) -> str:
        """Follow HTTP redirects to get final URL"""
        try:
            response = self.session.head(
                url, 
                allow_redirects=True, 
                timeout=self.timeout
            )
            return response.url
        except Exception as e:
            self.logger.warning(f"HTTP redirect following failed for {url}: {str(e)}")
            return url
    
    def _check_meta_refresh(self, url: str) -> Optional[str]:
        """Check for meta refresh redirects in HTML"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            
            # Check for meta refresh redirect
            meta_refresh_pattern = r'<meta[^>]*http-equiv=["\']refresh["\'][^>]*content=["\'][^>]*url=([^"\'>\s]+)'
            match = re.search(meta_refresh_pattern, response.text, re.IGNORECASE)
            
            if match:
                redirect_url = match.group(1)
                # Handle relative URLs
                if not redirect_url.startswith(('http://', 'https://')):
                    redirect_url = urljoin(url, redirect_url)
                
                self.logger.info(f"Found meta refresh redirect: {redirect_url}")
                return redirect_url
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Meta refresh check failed for {url}: {str(e)}")
            return None
    
    def _get_canonical_url(self, url: str) -> Optional[str]:
        """Extract canonical URL from HTML if available"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for canonical link
            canonical_link = soup.find('link', rel='canonical')
            if canonical_link and canonical_link.get('href'):
                canonical_url = canonical_link['href']
                
                # Handle relative URLs
                if not canonical_url.startswith(('http://', 'https://')):
                    canonical_url = urljoin(url, canonical_url)
                
                self.logger.info(f"Found canonical URL: {canonical_url}")
                return canonical_url
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Canonical URL extraction failed for {url}: {str(e)}")
            return None
    
    def _clean_tracking_parameters(self, url: str) -> str:
        """Remove tracking parameters from URL"""
        try:
            parsed = urlparse(url)
            
            if not parsed.query:
                return url
            
            # Parameters to remove
            tracking_params = {
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'ref', 'referrer', 'source', 'campaign', 'medium',
                'gclid', 'fbclid', 'msclkid', 'twclid',
                '_ga', '_gid', '_gac', '_gl',
                'mc_cid', 'mc_eid',
                'hsCtaTracking', 'hsa_acc', 'hsa_cam', 'hsa_grp', 'hsa_ad', 'hsa_src', 'hsa_tgt', 'hsa_kw', 'hsa_mt', 'hsa_net', 'hsa_ver',
                'igshid', 'igsh'
            }
            
            # Parse query parameters
            query_params = parse_qs(parsed.query, keep_blank_values=True)
            
            # Remove tracking parameters
            clean_params = {
                key: value for key, value in query_params.items()
                if not any(tracking_param in key.lower() for tracking_param in tracking_params)
            }
            
            # Rebuild URL
            if clean_params:
                # Flatten parameter values
                clean_query_parts = []
                for key, values in clean_params.items():
                    for value in values:
                        clean_query_parts.append(f"{key}={value}")
                clean_query = '&'.join(clean_query_parts)
            else:
                clean_query = ''
            
            clean_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path,
                parsed.params,
                clean_query,
                parsed.fragment
            ))
            
            return clean_url
            
        except Exception as e:
            self.logger.warning(f"URL cleaning failed for {url}: {str(e)}")
            return url
    
    def resolve_futuretools_redirect(self, futuretools_url: str) -> str:
        """
        Specifically handle FutureTools redirect URLs
        
        Args:
            futuretools_url: FutureTools redirect URL
            
        Returns:
            Actual tool website URL
        """
        try:
            self.logger.info(f"Resolving FutureTools redirect: {futuretools_url}")
            
            # First try to get the page content
            response = self.session.get(futuretools_url, timeout=self.timeout)
            
            # Strategy 1: Look for meta refresh redirect
            meta_refresh_pattern = r'<meta[^>]*http-equiv=["\']refresh["\'][^>]*content=["\'][^>]*url=([^"\'>\s]+)'
            match = re.search(meta_refresh_pattern, response.text, re.IGNORECASE)
            
            if match:
                actual_url = match.group(1)
                actual_url = self._clean_tracking_parameters(actual_url)
                self.logger.info(f"Found actual URL via meta refresh: {actual_url}")
                return actual_url
            
            # Strategy 2: Look for JavaScript redirects
            js_redirect_patterns = [
                r'window\.location\.href\s*=\s*["\']([^"\']+)["\']',
                r'window\.location\s*=\s*["\']([^"\']+)["\']',
                r'location\.href\s*=\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in js_redirect_patterns:
                match = re.search(pattern, response.text, re.IGNORECASE)
                if match:
                    actual_url = match.group(1)
                    if actual_url.startswith(('http://', 'https://')):
                        actual_url = self._clean_tracking_parameters(actual_url)
                        self.logger.info(f"Found actual URL via JavaScript: {actual_url}")
                        return actual_url
            
            # Strategy 3: Look for data attributes or hidden form fields
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for data attributes
            for attr in ['data-url', 'data-target', 'data-redirect', 'data-destination']:
                element = soup.find(attrs={attr: True})
                if element:
                    actual_url = element.get(attr)
                    if actual_url and actual_url.startswith(('http://', 'https://')):
                        actual_url = self._clean_tracking_parameters(actual_url)
                        self.logger.info(f"Found actual URL via {attr}: {actual_url}")
                        return actual_url
            
            # Strategy 4: Follow HTTP redirects as fallback
            final_url = self._follow_http_redirects(futuretools_url)
            if final_url != futuretools_url:
                final_url = self._clean_tracking_parameters(final_url)
                self.logger.info(f"Found actual URL via HTTP redirect: {final_url}")
                return final_url
            
            self.logger.warning(f"Could not resolve FutureTools redirect for: {futuretools_url}")
            return futuretools_url
            
        except Exception as e:
            self.logger.error(f"Error resolving FutureTools redirect {futuretools_url}: {str(e)}")
            return futuretools_url
    
    def is_redirect_url(self, url: str) -> bool:
        """Check if URL is a redirect URL that should be resolved"""
        redirect_indicators = [
            'futuretools.link',
            '/goto/',
            '/redirect/',
            '/track/',
            '/recommends/',
            'bit.ly',
            'tinyurl.com',
            't.co'
        ]
        
        return any(indicator in url for indicator in redirect_indicators)
    
    def batch_resolve_urls(self, urls: List[str]) -> Dict[str, str]:
        """Resolve multiple URLs in batch with rate limiting"""
        resolved_urls = {}
        
        for i, url in enumerate(urls):
            try:
                resolved_urls[url] = self.resolve_final_url(url)
                
                # Rate limiting
                if i < len(urls) - 1:
                    time.sleep(0.5)
                    
            except Exception as e:
                self.logger.error(f"Error resolving URL {url}: {str(e)}")
                resolved_urls[url] = url
        
        return resolved_urls
