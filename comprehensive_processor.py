"""
Comprehensive Processor for enhanced data extraction
"""

import json
import logging
import requests
from typing import Dict, Any
from entity_data import EntityData

logger = logging.getLogger(__name__)

class ComprehensiveProcessor:
    def __init__(self, api_key: str, base_url: str = "https://api.perplexity.ai/chat/completions"):
        self.api_key = api_key
        self.base_url = base_url

    def gap_fill_data(self, entity_data: EntityData) -> Dict[str, Any]:
        """Use Perplexity CHEAPEST model for minimal gap filling only"""
        try:
            prompt = self._create_minimal_gap_filling_prompt(entity_data)
            
            response = requests.post(
                self.base_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "sonar",  # CHEAPEST MODEL
                    "messages": [
                        {"role": "system", "content": "You are a research assistant. Provide only missing factual data in JSON format. Be concise."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 800  # REDUCED TOKENS = LOWER COST
                },
                timeout=30  # REDUCED TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Extract JSON from response
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    gap_data = json.loads(content[json_start:json_end])
                    logger.info(f"  💰 Perplexity gap-filled data for {entity_data.name} (cost-optimized)")
                    return gap_data
            
            logger.warning(f"  ⚠️ Perplexity gap-filling failed for {entity_data.name}")
            return {}
            
        except Exception as e:
            logger.error(f"  ❌ Perplexity error: {str(e)}")
            return {}
    
    def _create_minimal_gap_filling_prompt(self, entity_data: EntityData) -> str:
        """Create prompt for MINIMAL gap filling only - cost optimized"""
        return f"""
Research ONLY missing factual data for "{entity_data.name}" (website: {entity_data.website_url}).

Return ONLY these specific fields in JSON (skip fields you can't find):

{{
  "founded_year": 2023,
  "employee_count_range": "C1_10|C11_50|C51_200|C201_500|C501_1000|C1001_5000|C5001_PLUS",
  "funding_stage": "PRE_SEED|SEED|SERIES_A|SERIES_B|SERIES_C|SERIES_D_PLUS|PUBLIC",
  "location_summary": "City, Country",
  "pricing_details": "Brief pricing info",
  "social_links": {{
    "twitter": "handle_only",
    "linkedin": "company_slug"
  }}
}}

ONLY provide factual data you can verify. Skip anything uncertain. Be brief and cost-efficient.
"""