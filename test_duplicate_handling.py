#!/usr/bin/env python3
"""
Test duplicate entity handling to verify:
1. Entity existence check works
2. Unique name generation works
3. No more unique constraint violations
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_duplicate_handling():
    """Test duplicate entity handling"""
    
    # Test with a tool that likely already exists
    test_tools = [
        {
            "name": "ChatGPT",  # This likely exists
            "url": "https://chat.openai.com"
        },
        {
            "name": "Test Duplicate Tool",  # This is new
            "url": "https://example.com/test-duplicate"
        }
    ]
    
    logger.info("🧪 Testing duplicate entity handling...")
    
    # Send request to enhanced scraping endpoint
    response = requests.post(
        f"{BASE_URL}/api/start-enhanced-scraping",
        json={
            "tools": test_tools,
            "use_parallel": False,
            "use_phase3": True
        }
    )
    
    if response.status_code != 200:
        logger.error(f"❌ Failed to start enhanced scraping: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return None
    
    result = response.json()
    job_id = result.get('job_id')
    logger.info(f"✅ Enhanced scraping started with job ID: {job_id}")
    
    # Monitor job
    max_attempts = 30  # 2.5 minutes max
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            
            if response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {response.status_code}")
                break
            
            status = response.json()
            progress = status.get('progress', 0)
            state = status.get('status', 'unknown')
            
            logger.info(f"📈 Job {job_id}: {state} - {progress:.1f}% complete")
            
            if state in ['completed', 'failed']:
                logger.info(f"🏁 Job {job_id} finished with state: {state}")
                
                # Analyze results for duplicate handling
                results = status.get('results', [])
                errors = status.get('errors', [])
                
                logger.info(f"📊 Results: {len(results)} items, {len(errors)} errors")
                
                # Check for duplicate handling evidence
                duplicate_checks = 0
                unique_names = 0
                successful_saves = 0
                
                for result in results:
                    if isinstance(result, dict):
                        # Check if entity was saved successfully
                        if 'id' in result:
                            successful_saves += 1
                            logger.info(f"✅ Successfully saved: {result.get('name')} (ID: {result.get('id')})")
                        
                        # Check for unique name generation
                        name = result.get('name', '')
                        if any(suffix in name for suffix in [' 2', ' 3', '_', '20']):
                            unique_names += 1
                            logger.info(f"🔄 Unique name generated: {name}")
                
                # Check errors for duplicate constraint violations
                constraint_errors = 0
                for error in errors:
                    error_str = str(error).lower()
                    if 'unique constraint' in error_str or 'duplicate' in error_str:
                        constraint_errors += 1
                        logger.error(f"❌ Constraint violation: {error}")
                
                # Summary
                logger.info("📋 DUPLICATE HANDLING SUMMARY:")
                logger.info(f"   ✅ Successful saves: {successful_saves}")
                logger.info(f"   🔄 Unique names generated: {unique_names}")
                logger.info(f"   ❌ Constraint violations: {constraint_errors}")
                
                if constraint_errors == 0:
                    logger.info("🎉 SUCCESS: No unique constraint violations!")
                else:
                    logger.error("❌ FAILURE: Still getting constraint violations")
                
                return status
            
            time.sleep(5)  # Wait 5 seconds between checks
            attempt += 1
            
        except Exception as e:
            logger.error(f"❌ Error monitoring job: {str(e)}")
            break
    
    logger.warning(f"⏰ Job monitoring timed out")
    return None

def main():
    """Main test function"""
    logger.info("🧪 Starting Duplicate Entity Handling Test")
    logger.info("=" * 50)
    
    final_status = test_duplicate_handling()
    
    if final_status:
        logger.info("✅ Test completed!")
    else:
        logger.error("❌ Test failed!")
    
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
