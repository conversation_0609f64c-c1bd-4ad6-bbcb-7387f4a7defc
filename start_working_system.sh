#!/bin/bash

# Start Working AI Navigator Scrapers System
echo "🚀 Starting Working AI Navigator Scrapers System"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "backend/working_server.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: backend/working_server.py"
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Check if backend port is already in use
if check_port 8001; then
    echo "⚠️  Port 8001 is already in use. Stopping existing backend..."
    lsof -ti:8001 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Check if frontend port is already in use
if check_port 3000; then
    echo "⚠️  Port 3000 is already in use. Stopping existing frontend..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo ""
echo "🔧 Starting Working Backend Server..."
echo "   URL: http://localhost:8001"
echo "   Features: Traditional + Enhanced Scraping"

# Start backend in background
python3 backend/working_server.py &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if check_port 8001; then
    echo "   ✅ Working backend server started successfully (PID: $BACKEND_PID)"
else
    echo "   ❌ Failed to start working backend server"
    exit 1
fi

echo ""
echo "🌐 Starting Frontend Development Server..."
echo "   URL: http://localhost:3000"
echo "   Features: Enhanced UI with Phase 3 Integration"

# Change to frontend directory and start
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "   📦 Installing frontend dependencies..."
    if command -v npm >/dev/null 2>&1; then
        npm install
    elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
        /Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm install
    else
        echo "❌ Error: npm not found. Please install Node.js and npm"
        exit 1
    fi
fi

# Start frontend
echo "   🚀 Starting React development server..."
if command -v npm >/dev/null 2>&1; then
    npm start &
elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
    export PATH="/Users/<USER>/.nvm/versions/node/v22.14.0/bin:$PATH"
    npm start &
else
    echo "❌ Error: npm not found. Please install Node.js and npm"
    exit 1
fi

FRONTEND_PID=$!

# Wait for frontend to start
sleep 5

echo ""
echo "🎉 Working AI Navigator Scrapers System Started!"
echo "=================================================="
echo ""
echo "📊 System Status:"
echo "   ✅ Working Backend: http://localhost:8001"
echo "   ✅ Frontend UI: http://localhost:3000"
echo ""
echo "🚀 Available Features:"
echo "   ✅ Traditional Scrapy Spiders (futuretools, toolify, taaft)"
echo "   ✅ Enhanced Phase 3 Analysis (demo mode)"
echo "   ✅ Real-time Job Monitoring"
echo "   ✅ Performance Dashboard"
echo ""
echo "🎯 Next Steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Try the 'Scraping' tab for traditional scrapers"
echo "   3. Try the 'Enhanced' tab for Phase 3 features"
echo "   4. Monitor real-time progress and results"
echo ""
echo "⚠️  To stop the system:"
echo "   Press Ctrl+C or run: pkill -f 'working_server.py' && pkill -f 'react-scripts'"
echo ""

# Keep script running to show logs
echo "📋 System Logs (Press Ctrl+C to stop):"
echo "======================================="

# Wait for user to stop
wait
