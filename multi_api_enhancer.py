"""
Multi-API Enhancement Strategy
Intelligent result merging, fallback strategies, and cost optimization
"""

import logging
import json
import time
import requests
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
# import asyncio
# import aiohttp  # Not available, using requests instead

class APIProvider(Enum):
    """Supported API providers"""
    PERPLEXITY = "perplexity"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GROQ = "groq"

class APIStatus(Enum):
    """API status for monitoring"""
    ACTIVE = "active"
    DEGRADED = "degraded"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"

@dataclass
class APIConfig:
    """Configuration for an API provider"""
    provider: APIProvider
    api_key: str
    base_url: str
    model: str
    cost_per_token: float
    rate_limit: int  # requests per minute
    timeout: int = 30
    max_tokens: int = 1000

@dataclass
class APIResponse:
    """Response from an API provider"""
    provider: APIProvider
    success: bool
    data: Optional[Dict[str, Any]]
    error: Optional[str]
    response_time: float
    tokens_used: int
    cost: float

class MultiAPIEnhancer:
    """
    Multi-API enhancement framework with intelligent result merging
    Provides fallback strategies and cost optimization
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # API configurations
        self.api_configs = {}
        self.api_status = {}
        self.api_usage_stats = {}
        
        # Enhancement strategies
        self.enhancement_strategies = {
            'primary_with_fallback': self._primary_with_fallback_strategy,
            'parallel_merge': self._parallel_merge_strategy,
            'cost_optimized': self._cost_optimized_strategy,
            'quality_focused': self._quality_focused_strategy
        }
        
        # Result merging weights
        self.merge_weights = {
            APIProvider.PERPLEXITY: 0.4,  # Good for research and factual data
            APIProvider.OPENAI: 0.3,      # Good for structured output
            APIProvider.ANTHROPIC: 0.2,   # Good for analysis
            APIProvider.GROQ: 0.1         # Fast but less accurate
        }
        
        # Initialize default configurations
        self._initialize_default_configs()
    
    def _initialize_default_configs(self):
        """Initialize default API configurations"""
        
        # Perplexity configuration
        self.add_api_config(APIConfig(
            provider=APIProvider.PERPLEXITY,
            api_key="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",
            base_url="https://api.perplexity.ai/chat/completions",
            model="sonar",
            cost_per_token=0.0001,
            rate_limit=60,
            max_tokens=1000
        ))
        
        # Initialize status tracking
        for provider in APIProvider:
            self.api_status[provider] = APIStatus.ACTIVE
            self.api_usage_stats[provider] = {
                'requests_made': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'avg_response_time': 0.0
            }
    
    def add_api_config(self, config: APIConfig):
        """Add API configuration"""
        self.api_configs[config.provider] = config
        self.logger.info(f"Added API config for {config.provider.value}")
    
    def enhance_with_strategy_sync(self, tool_name: str, website_url: str,
                                 description: str, strategy: str = 'primary_with_fallback') -> Dict[str, Any]:
        """Synchronous version of enhance_with_strategy"""
        return self.enhance_with_strategy(tool_name, website_url, description, strategy)

    def enhance_with_strategy(self, tool_name: str, website_url: str,
                            description: str, strategy: str = 'primary_with_fallback') -> Dict[str, Any]:
        """
        Enhance tool data using specified strategy
        
        Args:
            tool_name: Name of the tool
            website_url: Tool website URL
            description: Tool description
            strategy: Enhancement strategy to use
            
        Returns:
            Enhanced data dictionary
        """
        
        self.logger.info(f"Enhancing {tool_name} using {strategy} strategy")
        
        if strategy not in self.enhancement_strategies:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        start_time = time.time()
        
        try:
            result = self.enhancement_strategies[strategy](tool_name, website_url, description)

            processing_time = time.time() - start_time
            self.logger.info(f"Enhancement completed in {processing_time:.2f}s using {strategy}")

            return result

        except Exception as e:
            self.logger.error(f"Enhancement failed with {strategy}: {str(e)}")
            # Fallback to simplest strategy
            if strategy != 'primary_with_fallback':
                return self._primary_with_fallback_strategy(tool_name, website_url, description)
            raise
    
    def _primary_with_fallback_strategy(self, tool_name: str, website_url: str, description: str) -> Dict[str, Any]:
        """Primary API with fallback strategy"""

        # Try primary API (Perplexity)
        primary_response = self._call_api(APIProvider.PERPLEXITY, tool_name, website_url, description)
        
        if primary_response.success:
            self.logger.info("Primary API (Perplexity) succeeded")
            return primary_response.data
        
        # Fallback to other available APIs
        self.logger.warning("Primary API failed, trying fallbacks")
        
        for provider in [APIProvider.OPENAI, APIProvider.ANTHROPIC, APIProvider.GROQ]:
            if provider in self.api_configs and self.api_status[provider] != APIStatus.FAILED:
                fallback_response = self._call_api(provider, tool_name, website_url, description)
                if fallback_response.success:
                    self.logger.info(f"Fallback API ({provider.value}) succeeded")
                    return fallback_response.data
        
        # If all APIs fail, return basic data
        self.logger.error("All APIs failed, returning basic data")
        return self._generate_basic_fallback_data(tool_name, website_url, description)
    
    async def _parallel_merge_strategy(self, tool_name: str, website_url: str, description: str) -> Dict[str, Any]:
        """Parallel API calls with intelligent merging"""
        
        # Call multiple APIs in parallel
        tasks = []
        available_providers = [p for p in self.api_configs.keys() if self.api_status[p] != APIStatus.FAILED]
        
        for provider in available_providers[:3]:  # Limit to 3 for cost control
            task = self._call_api(provider, tool_name, website_url, description)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful responses
        successful_responses = [r for r in responses if isinstance(r, APIResponse) and r.success]
        
        if not successful_responses:
            return self._generate_basic_fallback_data(tool_name, website_url, description)
        
        # Merge responses intelligently
        merged_data = self._merge_api_responses(successful_responses)
        
        self.logger.info(f"Merged data from {len(successful_responses)} APIs")
        return merged_data
    
    async def _cost_optimized_strategy(self, tool_name: str, website_url: str, description: str) -> Dict[str, Any]:
        """Cost-optimized strategy using cheapest available API"""
        
        # Sort APIs by cost
        available_apis = [(p, c) for p, c in self.api_configs.items() if self.api_status[p] != APIStatus.FAILED]
        available_apis.sort(key=lambda x: x[1].cost_per_token)
        
        for provider, config in available_apis:
            response = await self._call_api(provider, tool_name, website_url, description)
            if response.success:
                self.logger.info(f"Cost-optimized strategy used {provider.value} (${response.cost:.4f})")
                return response.data
        
        return self._generate_basic_fallback_data(tool_name, website_url, description)
    
    async def _quality_focused_strategy(self, tool_name: str, website_url: str, description: str) -> Dict[str, Any]:
        """Quality-focused strategy using best available API"""
        
        # Try APIs in order of quality (based on merge weights)
        quality_order = sorted(self.api_configs.keys(), key=lambda p: self.merge_weights.get(p, 0), reverse=True)
        
        for provider in quality_order:
            if self.api_status[provider] != APIStatus.FAILED:
                response = await self._call_api(provider, tool_name, website_url, description)
                if response.success:
                    self.logger.info(f"Quality-focused strategy used {provider.value}")
                    return response.data
        
        return self._generate_basic_fallback_data(tool_name, website_url, description)
    
    async def _call_api(self, provider: APIProvider, tool_name: str, website_url: str, description: str) -> APIResponse:
        """Call specific API provider"""
        
        if provider not in self.api_configs:
            return APIResponse(provider, False, None, "API not configured", 0, 0, 0)
        
        config = self.api_configs[provider]
        start_time = time.time()
        
        try:
            # Build prompt
            prompt = self._build_enhancement_prompt(tool_name, website_url, description)
            
            # Make API call
            if provider == APIProvider.PERPLEXITY:
                response_data = await self._call_perplexity(config, prompt)
            else:
                # Placeholder for other APIs
                response_data = await self._call_generic_api(config, prompt)
            
            response_time = time.time() - start_time
            
            # Parse response
            if response_data:
                parsed_data = self._parse_api_response(response_data, provider)
                tokens_used = len(prompt.split()) + len(str(parsed_data).split())  # Rough estimate
                cost = tokens_used * config.cost_per_token
                
                # Update stats
                self._update_api_stats(provider, True, response_time, tokens_used, cost)
                
                return APIResponse(provider, True, parsed_data, None, response_time, tokens_used, cost)
            else:
                self._update_api_stats(provider, False, response_time, 0, 0)
                return APIResponse(provider, False, None, "Empty response", response_time, 0, 0)
        
        except Exception as e:
            response_time = time.time() - start_time
            self._update_api_stats(provider, False, response_time, 0, 0)
            self.logger.error(f"API call to {provider.value} failed: {str(e)}")
            return APIResponse(provider, False, None, str(e), response_time, 0, 0)
    
    async def _call_perplexity(self, config: APIConfig, prompt: str) -> Optional[Dict[str, Any]]:
        """Call Perplexity API"""
        
        headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": config.max_tokens,
            "temperature": 0.1
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(config.base_url, headers=headers, json=data, timeout=config.timeout) as response:
                if response.status == 200:
                    result = await response.json()
                    return result['choices'][0]['message']['content']
                else:
                    self.logger.error(f"Perplexity API error: {response.status}")
                    return None
    
    async def _call_generic_api(self, config: APIConfig, prompt: str) -> Optional[Dict[str, Any]]:
        """Generic API call (placeholder for other providers)"""
        # This would be implemented for each specific API provider
        self.logger.info(f"Generic API call to {config.provider.value} (not implemented)")
        return None
    
    def _build_enhancement_prompt(self, tool_name: str, website_url: str, description: str) -> str:
        """Build enhancement prompt for API calls"""
        
        return f"""
        Analyze and enhance information about "{tool_name}":
        
        Website: {website_url}
        Description: {description}
        
        Provide comprehensive information in JSON format:
        {{
            "short_description": "Brief compelling description",
            "description": "Detailed description",
            "key_features": ["feature1", "feature2", "feature3"],
            "use_cases": ["use_case1", "use_case2"],
            "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES|OPEN_SOURCE",
            "price_range": "FREE|LOW|MEDIUM|HIGH|ENTERPRISE",
            "technical_level": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
            "target_audience": ["audience1", "audience2"],
            "has_free_tier": true/false,
            "has_api": true/false,
            "integrations": ["integration1", "integration2"],
            "founded_year": year,
            "employee_count_range": "C1_10|C11_50|C51_200|C201_500|C501_1000|C1001_5000|C5001_PLUS",
            "funding_stage": "PRE_SEED|SEED|SERIES_A|SERIES_B|SERIES_C|SERIES_D_PLUS|PUBLIC"
        }}
        
        Return only the JSON, no other text.
        """
    
    def _parse_api_response(self, response_data: str, provider: APIProvider) -> Dict[str, Any]:
        """Parse API response into structured data"""
        
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_data, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                # Fallback parsing
                return {"raw_response": response_data}
        except json.JSONDecodeError:
            return {"raw_response": response_data}
    
    def _merge_api_responses(self, responses: List[APIResponse]) -> Dict[str, Any]:
        """Intelligently merge multiple API responses"""
        
        if len(responses) == 1:
            return responses[0].data
        
        merged_data = {}
        
        # Merge each field using weighted voting
        all_fields = set()
        for response in responses:
            if response.data:
                all_fields.update(response.data.keys())
        
        for field in all_fields:
            field_values = []
            field_weights = []
            
            for response in responses:
                if response.data and field in response.data:
                    field_values.append(response.data[field])
                    field_weights.append(self.merge_weights.get(response.provider, 0.1))
            
            if field_values:
                merged_data[field] = self._merge_field_values(field, field_values, field_weights)
        
        return merged_data
    
    def _merge_field_values(self, field: str, values: List[Any], weights: List[float]) -> Any:
        """Merge field values using weights"""
        
        if not values:
            return None
        
        # For lists, merge and deduplicate
        if isinstance(values[0], list):
            merged_list = []
            for value_list in values:
                merged_list.extend(value_list)
            return list(set(merged_list))  # Remove duplicates
        
        # For strings, use weighted selection (highest weight wins)
        if isinstance(values[0], str):
            max_weight_idx = weights.index(max(weights))
            return values[max_weight_idx]
        
        # For numbers, use weighted average
        if isinstance(values[0], (int, float)):
            weighted_sum = sum(v * w for v, w in zip(values, weights))
            total_weight = sum(weights)
            return weighted_sum / total_weight if total_weight > 0 else values[0]
        
        # For booleans, use majority vote with weights
        if isinstance(values[0], bool):
            true_weight = sum(w for v, w in zip(values, weights) if v)
            false_weight = sum(w for v, w in zip(values, weights) if not v)
            return true_weight > false_weight
        
        # Default: return first value
        return values[0]
    
    def _generate_basic_fallback_data(self, tool_name: str, website_url: str, description: str) -> Dict[str, Any]:
        """Generate basic fallback data when all APIs fail"""
        
        return {
            "short_description": f"{tool_name} - AI tool for enhanced productivity",
            "description": description or f"{tool_name} is an AI-powered tool designed to improve workflow efficiency.",
            "key_features": ["AI-powered", "User-friendly", "Productivity enhancement"],
            "use_cases": ["Business automation", "Personal productivity"],
            "pricing_model": "FREEMIUM",
            "price_range": "MEDIUM",
            "technical_level": "INTERMEDIATE",
            "target_audience": ["Professionals", "Businesses"],
            "has_free_tier": True,
            "has_api": False,
            "integrations": [],
            "founded_year": None,
            "employee_count_range": "C11_50",
            "funding_stage": "SEED"
        }
    
    def _update_api_stats(self, provider: APIProvider, success: bool, response_time: float, tokens: int, cost: float):
        """Update API usage statistics"""
        
        stats = self.api_usage_stats[provider]
        stats['requests_made'] += 1
        
        if success:
            stats['successful_requests'] += 1
        else:
            stats['failed_requests'] += 1
        
        stats['total_tokens'] += tokens
        stats['total_cost'] += cost
        
        # Update average response time
        total_requests = stats['requests_made']
        stats['avg_response_time'] = ((stats['avg_response_time'] * (total_requests - 1)) + response_time) / total_requests
        
        # Update API status based on recent performance
        success_rate = stats['successful_requests'] / stats['requests_made']
        if success_rate < 0.5:
            self.api_status[provider] = APIStatus.FAILED
        elif success_rate < 0.8:
            self.api_status[provider] = APIStatus.DEGRADED
        else:
            self.api_status[provider] = APIStatus.ACTIVE
    
    def get_api_status_report(self) -> Dict[str, Any]:
        """Get comprehensive API status report"""
        
        report = {
            'timestamp': time.time(),
            'providers': {}
        }
        
        for provider in APIProvider:
            if provider in self.api_usage_stats:
                stats = self.api_usage_stats[provider]
                report['providers'][provider.value] = {
                    'status': self.api_status[provider].value,
                    'requests_made': stats['requests_made'],
                    'success_rate': stats['successful_requests'] / max(stats['requests_made'], 1) * 100,
                    'avg_response_time': stats['avg_response_time'],
                    'total_cost': stats['total_cost'],
                    'total_tokens': stats['total_tokens']
                }
        
        return report
