"""
Test script for Dynamic Prompt Optimizer
Tests context-aware prompt generation and performance tracking
"""

import sys
sys.path.append('/app')

from dynamic_prompt_optimizer import DynamicPromptOptimizer, PromptType, PromptComplexity
import time

def test_prompt_generation():
    """Test prompt generation with different contexts"""
    
    print("🎯 TESTING DYNAMIC PROMPT GENERATION")
    print("=" * 50)
    
    optimizer = DynamicPromptOptimizer()
    
    # Test contexts with varying richness
    test_contexts = [
        {
            "name": "Rich Context",
            "context": {
                "tool_name": "Canva",
                "website_url": "https://canva.com",
                "description": "Canva is a comprehensive graphic design platform that enables users to create professional-quality designs through an intuitive drag-and-drop interface. It offers thousands of templates, stock photos, and design elements.",
                "key_features": ["drag-and-drop", "templates", "collaboration", "brand kit"],
                "entity_type": "ai-tool"
            },
            "expected_complexity": PromptComplexity.COMPREHENSIVE
        },
        {
            "name": "Medium Context",
            "context": {
                "tool_name": "GitHub Copilot",
                "website_url": "https://github.com/features/copilot",
                "description": "AI-powered code completion tool",
                "entity_type": "ai-tool"
            },
            "expected_complexity": PromptComplexity.DETAILED
        },
        {
            "name": "Minimal Context",
            "context": {
                "tool_name": "Unknown Tool",
                "description": "AI tool"
            },
            "expected_complexity": PromptComplexity.SIMPLE
        }
    ]
    
    successful_generations = 0
    total_tests = len(test_contexts) * len(PromptType)
    
    for context_test in test_contexts:
        print(f"\n📝 Testing {context_test['name']}")
        print("-" * 30)
        
        context = context_test['context']
        
        for prompt_type in PromptType:
            try:
                # Generate prompt
                prompt_text, prompt_id = optimizer.generate_optimized_prompt(
                    prompt_type, context
                )
                
                print(f"   {prompt_type.value}:")
                print(f"     ID: {prompt_id}")
                print(f"     Length: {len(prompt_text)} chars")
                print(f"     Contains tool name: {'✅' if context['tool_name'] in prompt_text else '❌'}")
                
                # Validate prompt quality
                if (len(prompt_text) > 50 and 
                    context['tool_name'] in prompt_text and
                    prompt_id.startswith(prompt_type.value)):
                    successful_generations += 1
                    print(f"     ✅ Generation successful")
                else:
                    print(f"     ❌ Generation failed")
                
            except Exception as e:
                print(f"     💥 Error: {str(e)}")
    
    success_rate = (successful_generations / total_tests * 100)
    print(f"\n📊 PROMPT GENERATION RESULTS:")
    print(f"   Successful generations: {successful_generations}/{total_tests}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    return success_rate >= 80

def test_complexity_selection():
    """Test automatic complexity selection"""
    
    print("\n🎚️ TESTING COMPLEXITY SELECTION")
    print("=" * 50)
    
    optimizer = DynamicPromptOptimizer()
    
    # Test complexity selection with different context richness
    complexity_tests = [
        {
            "context": {
                "tool_name": "Rich Tool",
                "website_url": "https://example.com",
                "description": "This is a very detailed description with lots of information about the tool's capabilities, features, and use cases. It provides comprehensive context for analysis." * 2,
                "key_features": ["feature1", "feature2", "feature3"]
            },
            "expected": PromptComplexity.COMPREHENSIVE
        },
        {
            "context": {
                "tool_name": "Medium Tool",
                "website_url": "https://example.com",
                "description": "This tool has moderate description length with some details."
            },
            "expected": PromptComplexity.DETAILED
        },
        {
            "context": {
                "tool_name": "Simple Tool",
                "description": "Basic tool"
            },
            "expected": PromptComplexity.SIMPLE
        }
    ]
    
    correct_selections = 0
    
    for i, test in enumerate(complexity_tests, 1):
        selected_complexity = optimizer._heuristic_complexity_selection(test['context'])
        
        print(f"   Test {i}: {test['context']['tool_name']}")
        print(f"     Context richness: {len(test['context']['description'])} chars, {len(test['context'].get('key_features', []))} features")
        print(f"     Expected: {test['expected'].value}")
        print(f"     Selected: {selected_complexity.value}")
        
        if selected_complexity == test['expected']:
            print(f"     ✅ Correct selection")
            correct_selections += 1
        else:
            print(f"     ❌ Incorrect selection")
    
    accuracy = (correct_selections / len(complexity_tests) * 100)
    print(f"\n   Complexity selection accuracy: {accuracy:.1f}% ({correct_selections}/{len(complexity_tests)})")
    
    return accuracy >= 75

def test_performance_tracking():
    """Test performance tracking and adaptation"""
    
    print("\n📈 TESTING PERFORMANCE TRACKING")
    print("=" * 50)
    
    optimizer = DynamicPromptOptimizer()
    
    # Generate some prompts and record performance
    context = {
        "tool_name": "Test Tool",
        "website_url": "https://example.com",
        "description": "Test description for performance tracking"
    }
    
    # Simulate multiple prompt generations and performance recordings
    performance_data = []
    
    for i in range(15):  # Generate enough data for adaptation threshold
        prompt_text, prompt_id = optimizer.generate_optimized_prompt(
            PromptType.BASIC_ENHANCEMENT, context
        )
        
        # Simulate varying performance
        success = i % 4 != 0  # 75% success rate
        quality_score = 0.8 if success else 0.4
        response_time = 1.0 + (i * 0.1)
        tokens_used = 200 + (i * 10)
        
        optimizer.record_performance(
            prompt_id, success, quality_score, response_time, tokens_used
        )
        
        performance_data.append({
            'prompt_id': prompt_id,
            'success': success,
            'quality_score': quality_score
        })
    
    # Get performance report
    report = optimizer.get_performance_report()
    
    print(f"   Performance Report:")
    print(f"     Total attempts: {report['total_attempts']}")
    print(f"     Overall success rate: {report['overall_success_rate']:.1f}%")
    print(f"     Average quality score: {report['avg_quality_score']:.2f}")
    print(f"     Average response time: {report['avg_response_time']:.2f}s")
    print(f"     Template count: {report['template_count']}")
    print(f"     Adaptations applied: {report['adaptations_applied']}")
    
    # Validate tracking
    expected_attempts = 15
    expected_success_rate = 75.0  # Based on our simulation
    
    tracking_accurate = (
        report['total_attempts'] == expected_attempts and
        abs(report['overall_success_rate'] - expected_success_rate) < 5.0
    )
    
    if tracking_accurate:
        print(f"   ✅ Performance tracking accurate")
        return True
    else:
        print(f"   ❌ Performance tracking inaccurate")
        return False

def test_template_management():
    """Test template management and statistics"""
    
    print("\n📚 TESTING TEMPLATE MANAGEMENT")
    print("=" * 50)
    
    optimizer = DynamicPromptOptimizer()
    
    # Check initial templates
    initial_count = len(optimizer.prompt_templates)
    print(f"   Initial template count: {initial_count}")
    
    # List template types
    template_types = set()
    for template_id, template in optimizer.prompt_templates.items():
        template_types.add(template.prompt_type.value)
        print(f"     {template_id}: {template.prompt_type.value} ({template.complexity.value})")
    
    print(f"   Template types covered: {len(template_types)}")
    
    # Test template retrieval
    test_template = optimizer._get_best_template(PromptType.BASIC_ENHANCEMENT, PromptComplexity.DETAILED)
    
    if test_template:
        print(f"   ✅ Template retrieval working")
        print(f"     Retrieved: {test_template.prompt_type.value} - {test_template.complexity.value}")
        return True
    else:
        print(f"   ❌ Template retrieval failed")
        return False

def test_context_adaptations():
    """Test context-specific adaptations"""
    
    print("\n🔧 TESTING CONTEXT ADAPTATIONS")
    print("=" * 50)
    
    optimizer = DynamicPromptOptimizer()
    
    # Test different entity types
    adaptation_tests = [
        {
            "context": {"entity_type": "research-paper", "tool_name": "Research Tool"},
            "expected_keyword": "academic"
        },
        {
            "context": {"entity_type": "hardware", "tool_name": "Hardware Tool"},
            "expected_keyword": "specifications"
        },
        {
            "context": {"description": "machine learning platform", "tool_name": "ML Tool"},
            "expected_keyword": "ML/AI"
        }
    ]
    
    successful_adaptations = 0
    
    for i, test in enumerate(adaptation_tests, 1):
        prompt_text, prompt_id = optimizer.generate_optimized_prompt(
            PromptType.BASIC_ENHANCEMENT, test['context']
        )
        
        has_adaptation = test['expected_keyword'].lower() in prompt_text.lower()
        
        print(f"   Test {i}: {test['context']['tool_name']}")
        print(f"     Entity type: {test['context'].get('entity_type', 'ai-tool')}")
        print(f"     Expected keyword: {test['expected_keyword']}")
        print(f"     Adaptation applied: {'✅' if has_adaptation else '❌'}")
        
        if has_adaptation:
            successful_adaptations += 1
    
    adaptation_rate = (successful_adaptations / len(adaptation_tests) * 100)
    print(f"\n   Adaptation success rate: {adaptation_rate:.1f}% ({successful_adaptations}/{len(adaptation_tests)})")
    
    return adaptation_rate >= 66  # At least 2/3 should work

if __name__ == "__main__":
    print("🎯 DYNAMIC PROMPT OPTIMIZATION TESTING")
    print("=" * 60)
    
    # Run all tests
    generation_success = test_prompt_generation()
    complexity_success = test_complexity_selection()
    tracking_success = test_performance_tracking()
    template_success = test_template_management()
    adaptation_success = test_context_adaptations()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Prompt generation: {'✅ PASS' if generation_success else '❌ FAIL'}")
    print(f"   Complexity selection: {'✅ PASS' if complexity_success else '❌ FAIL'}")
    print(f"   Performance tracking: {'✅ PASS' if tracking_success else '❌ FAIL'}")
    print(f"   Template management: {'✅ PASS' if template_success else '❌ FAIL'}")
    print(f"   Context adaptations: {'✅ PASS' if adaptation_success else '❌ FAIL'}")
    
    total_passed = sum([generation_success, complexity_success, tracking_success, template_success, adaptation_success])
    
    if total_passed >= 4:
        print("\n   🎉 DYNAMIC PROMPT OPTIMIZER READY!")
        print("   📈 Context-aware prompt generation working")
        print("   🎚️ Performance tracking and adaptation functional")
        print("   🔧 Quality-based optimization implemented")
    else:
        print("\n   ⚠️  Dynamic prompt optimizer needs improvement")
