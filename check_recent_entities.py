#!/usr/bin/env python3

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append('.')

from ai_navigator_client import AINavigatorClient

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_recent_entities():
    """Check recently created entities and their features"""
    print("🔍 Checking Recent Entities and Their Features")
    print("=" * 60)
    
    # Initialize client
    client = AINavigatorClient()
    
    # Test login
    if not client._refresh_token():
        print("❌ Failed to login to AI Navigator API")
        return False
    
    print("✅ Successfully logged in to AI Navigator API")
    
    try:
        import requests
        headers = client._get_headers()
        
        # Get recent entities (last 24 hours)
        print("\n📅 Fetching entities from the last 24 hours...")
        
        # Calculate 24 hours ago
        yesterday = datetime.now() - timedelta(hours=24)
        yesterday_iso = yesterday.isoformat() + "Z"
        
        response = requests.get(
            f"{client.base_url}/entities",
            headers=headers,
            params={
                "limit": 50,
                "createdAtFrom": yesterday_iso,
                "page": 1
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            entities = data.get('data', []) if isinstance(data, dict) else data
            
            print(f"📊 Found {len(entities)} entities created in the last 24 hours")
            
            if not entities:
                print("   No recent entities found")
                return True
            
            # Analyze each entity
            entities_with_features = 0
            entities_without_features = 0
            
            for i, entity in enumerate(entities[:10]):  # Check first 10
                entity_id = entity.get('id')
                entity_name = entity.get('name', 'Unknown')
                created_at = entity.get('createdAt', 'Unknown')
                status = entity.get('status', 'Unknown')
                
                print(f"\n🔍 Entity {i+1}: {entity_name}")
                print(f"   ID: {entity_id}")
                print(f"   Created: {created_at}")
                print(f"   Status: {status}")
                
                # Get detailed entity info
                detail_response = requests.get(
                    f"{client.base_url}/entities/{entity_id}",
                    headers=headers,
                    timeout=10
                )
                
                if detail_response.status_code == 200:
                    entity_details = detail_response.json()
                    features = entity_details.get('features', [])
                    categories = entity_details.get('categories', [])
                    tags = entity_details.get('tags', [])
                    
                    print(f"   📊 Taxonomy:")
                    print(f"      Categories: {len(categories)}")
                    print(f"      Features: {len(features)}")
                    print(f"      Tags: {len(tags)}")
                    
                    if features:
                        entities_with_features += 1
                        print(f"   ✅ Features found:")
                        for feature in features:
                            print(f"      - {feature.get('name')} ({feature.get('id')})")
                    else:
                        entities_without_features += 1
                        print(f"   ❌ No features found")
                    
                    if categories:
                        print(f"   📂 Categories:")
                        for category in categories:
                            print(f"      - {category.get('name')}")
                    
                    if tags:
                        print(f"   🏷️  Tags:")
                        for tag in tags:
                            print(f"      - {tag.get('name')}")
                else:
                    print(f"   ❌ Failed to get entity details: {detail_response.status_code}")
            
            print(f"\n📈 Summary:")
            print(f"   Entities with features: {entities_with_features}")
            print(f"   Entities without features: {entities_without_features}")
            print(f"   Feature success rate: {entities_with_features/(entities_with_features + entities_without_features)*100:.1f}%")
            
            return True
        else:
            print(f"❌ Failed to fetch entities: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking recent entities: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_recent_entities()
    sys.exit(0 if success else 1)