"""
Basic Test Suite for Phase 3 Core Components
Tests the core functionality without external dependencies.
"""

import unittest
import time
from unittest.mock import Mock, patch

# Import Phase 3 core components
from structured_data_extractor import StructuredDataExtractor, StructuredDataType
from advanced_content_analyzer import AdvancedContentAnalyzer, ContentType
from performance_technical_analyzer import PerformanceTechnicalAnalyzer, PerformanceMetric

class TestPhase3CoreComponents(unittest.TestCase):
    """Test core Phase 3 functionality"""
    
    def setUp(self):
        self.structured_extractor = StructuredDataExtractor()
        self.content_analyzer = AdvancedContentAnalyzer()
        self.performance_analyzer = PerformanceTechnicalAnalyzer()
        
        # Sample HTML for testing
        self.sample_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "SoftwareApplication",
                "name": "AI Navigator Test Tool",
                "description": "A comprehensive AI tool for testing"
            }
            </script>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <div class="pricing">
                <h3>Basic Plan</h3>
                <p>$9.99 per month</p>
                <h3>Pro Plan</h3>
                <p>$19.99 per month</p>
            </div>
            <div class="testimonial">
                <blockquote>"This AI tool has revolutionized our workflow!"</blockquote>
                <div class="author">John Smith</div>
                <div class="company">Tech Corp</div>
            </div>
            <div class="features">
                <h3>Key Benefits</h3>
                <ul>
                    <li>AI-powered analysis</li>
                    <li>Real-time processing</li>
                    <li>Advanced algorithms</li>
                </ul>
            </div>
            <div class="requirements">
                <h3>System Requirements</h3>
                <ul>
                    <li>Windows 10 or later</li>
                    <li>4GB RAM minimum</li>
                    <li>Chrome 90+ or Firefox 88+</li>
                </ul>
            </div>
        </body>
        </html>
        """

    def test_structured_data_extraction(self):
        """Test structured data extraction functionality"""
        print("\n🔍 Testing Structured Data Extraction...")
        
        results = self.structured_extractor.extract_all_structured_data(
            "https://test.com", self.sample_html
        )
        
        # Should find JSON-LD, pricing, and other structured data
        self.assertGreater(len(results), 0, "Should extract structured data")
        
        # Check for JSON-LD
        json_ld_results = [r for r in results if r.data_type == StructuredDataType.JSON_LD]
        self.assertGreater(len(json_ld_results), 0, "Should find JSON-LD data")
        
        json_ld = json_ld_results[0]
        self.assertEqual(json_ld.confidence, 0.95, "JSON-LD should have high confidence")
        self.assertIn("@type", json_ld.content, "JSON-LD should contain @type")
        
        # Check for pricing data
        pricing_results = [r for r in results if r.data_type == StructuredDataType.PRICING_TABLE]
        self.assertGreater(len(pricing_results), 0, "Should find pricing data")
        
        # Generate summary
        summary = self.structured_extractor.get_structured_data_summary(results)
        self.assertGreater(summary['total_elements'], 0, "Summary should show elements")
        self.assertGreater(summary['average_confidence'], 0, "Should have confidence scores")
        
        print(f"   ✅ Extracted {len(results)} structured data elements")
        print(f"   ✅ Average confidence: {summary['average_confidence']:.2f}")

    def test_advanced_content_analysis(self):
        """Test advanced content analysis functionality"""
        print("\n📝 Testing Advanced Content Analysis...")
        
        results = self.content_analyzer.analyze_content(
            "https://test.com", self.sample_html
        )
        
        # Should find testimonials, selling points, etc.
        self.assertGreater(len(results), 0, "Should extract content elements")
        
        # Check for testimonials
        testimonials = [r for r in results if r.content_type == ContentType.TESTIMONIAL]
        if testimonials:
            testimonial = testimonials[0]
            self.assertIn("revolutionized", testimonial.content, "Should extract testimonial text")
            self.assertGreater(testimonial.confidence, 0.3, "Should have reasonable confidence")
        
        # Check for selling points
        selling_points = [r for r in results if r.content_type == ContentType.SELLING_POINT]
        self.assertGreater(len(selling_points), 0, "Should find selling points")
        
        # Generate summary
        summary = self.content_analyzer.get_content_analysis_summary(results)
        self.assertGreater(summary['total_elements'], 0, "Summary should show elements")
        
        print(f"   ✅ Analyzed {len(results)} content elements")
        print(f"   ✅ Found {len(testimonials)} testimonials, {len(selling_points)} selling points")

    @patch('requests.get')
    def test_performance_technical_analysis(self, mock_get):
        """Test performance and technical analysis functionality"""
        print("\n⚡ Testing Performance & Technical Analysis...")
        
        # Mock HTTP response for load time test
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = self.sample_html.encode('utf-8')
        mock_get.return_value = mock_response
        
        results = self.performance_analyzer.analyze_performance_and_technical(
            "https://test.com", self.sample_html
        )
        
        # Should find various performance metrics
        self.assertGreater(len(results), 0, "Should extract performance metrics")
        
        # Check for mobile-friendliness
        mobile_results = [r for r in results if r.metric_type == PerformanceMetric.MOBILE_FRIENDLY]
        self.assertGreater(len(mobile_results), 0, "Should analyze mobile-friendliness")
        
        mobile = mobile_results[0]
        self.assertTrue(mobile.value['viewport_meta'], "Should detect viewport meta tag")
        self.assertGreater(mobile.score, 30, "Should score reasonably for mobile")
        
        # Check for technical specs
        tech_specs_results = [r for r in results if r.metric_type == PerformanceMetric.TECHNICAL_SPECS]
        if tech_specs_results:
            tech_specs = tech_specs_results[0]
            self.assertGreater(len(tech_specs.value['system_requirements']), 0, "Should find system requirements")
        
        # Generate summary
        summary = self.performance_analyzer.get_performance_summary(results)
        self.assertIn('overall_score', summary, "Should generate overall score")
        self.assertIn('performance_grade', summary, "Should assign performance grade")
        
        print(f"   ✅ Analyzed {len(results)} performance metrics")
        print(f"   ✅ Overall performance score: {summary['overall_score']}")
        print(f"   ✅ Performance grade: {summary['performance_grade']}")

    def test_integration_workflow(self):
        """Test integrated workflow of all Phase 3 components"""
        print("\n🔄 Testing Integrated Phase 3 Workflow...")
        
        url = "https://test-ai-tool.com"
        
        # Step 1: Extract structured data
        structured_results = self.structured_extractor.extract_all_structured_data(url, self.sample_html)
        
        # Step 2: Analyze content
        content_results = self.content_analyzer.analyze_content(url, self.sample_html)
        
        # Step 3: Analyze performance (mock the HTTP request)
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = self.sample_html.encode('utf-8')
            mock_get.return_value = mock_response
            
            performance_results = self.performance_analyzer.analyze_performance_and_technical(url, self.sample_html)
        
        # Verify all components produced results
        self.assertGreater(len(structured_results), 0, "Structured data extraction should work")
        self.assertGreater(len(content_results), 0, "Content analysis should work")
        self.assertGreater(len(performance_results), 0, "Performance analysis should work")
        
        # Create comprehensive enhancement data
        enhancement_data = {
            'url': url,
            'structured_data': {
                'total_elements': len(structured_results),
                'json_ld_found': any(r.data_type == StructuredDataType.JSON_LD for r in structured_results),
                'pricing_found': any(r.data_type == StructuredDataType.PRICING_TABLE for r in structured_results),
                'summary': self.structured_extractor.get_structured_data_summary(structured_results)
            },
            'content_analysis': {
                'total_elements': len(content_results),
                'testimonials_found': any(r.content_type == ContentType.TESTIMONIAL for r in content_results),
                'selling_points_found': any(r.content_type == ContentType.SELLING_POINT for r in content_results),
                'summary': self.content_analyzer.get_content_analysis_summary(content_results)
            },
            'performance_analysis': {
                'total_metrics': len(performance_results),
                'mobile_friendly': any(r.metric_type == PerformanceMetric.MOBILE_FRIENDLY for r in performance_results),
                'tech_specs_found': any(r.metric_type == PerformanceMetric.TECHNICAL_SPECS for r in performance_results),
                'summary': self.performance_analyzer.get_performance_summary(performance_results)
            }
        }
        
        # Verify comprehensive data
        self.assertTrue(enhancement_data['structured_data']['json_ld_found'], "Should find JSON-LD")
        self.assertTrue(enhancement_data['content_analysis']['selling_points_found'], "Should find selling points")
        self.assertTrue(enhancement_data['performance_analysis']['mobile_friendly'], "Should analyze mobile-friendliness")
        
        print(f"   ✅ Structured data: {enhancement_data['structured_data']['total_elements']} elements")
        print(f"   ✅ Content analysis: {enhancement_data['content_analysis']['total_elements']} elements")
        print(f"   ✅ Performance metrics: {enhancement_data['performance_analysis']['total_metrics']} metrics")
        print(f"   ✅ Overall performance score: {enhancement_data['performance_analysis']['summary']['overall_score']}")

def run_phase3_basic_tests():
    """Run basic Phase 3 tests"""
    print("🧪 RUNNING PHASE 3 BASIC FUNCTIONALITY TESTS")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test class
    tests = unittest.TestLoader().loadTestsFromTestCase(TestPhase3CoreComponents)
    test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 PHASE 3 BASIC TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}")
            print(f"    {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}")
            print(f"    {traceback.split('Exception:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ ALL BASIC TESTS PASSED!")
        print("🎉 Phase 3 core functionality is working correctly!")
        print("\n🚀 PHASE 3 IMPLEMENTATION STATUS:")
        print("   ✅ Structured Data Extraction - COMPLETE")
        print("   ✅ Advanced Content Analysis - COMPLETE") 
        print("   ✅ Performance & Technical Analysis - COMPLETE")
        print("   ✅ Integration Workflow - COMPLETE")
        print("\n📋 Additional components (caching, parallel processing, monitoring)")
        print("   require external dependencies but core functionality is proven.")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_phase3_basic_tests()
    exit(0 if success else 1)
