"""
Test script for Logo Extraction Service
Tests logo detection with real websites
"""

import sys
sys.path.append('/app')

from logo_extraction_service import LogoExtractionService
import time

def test_logo_extraction():
    """Test the logo extraction service with real websites"""
    
    print("🎨 TESTING LOGO EXTRACTION SERVICE")
    print("=" * 50)
    
    # Initialize service
    extractor = LogoExtractionService()
    
    # Test cases with known logos
    test_websites = [
        {
            "name": "OpenAI",
            "url": "https://openai.com",
            "expected_has_logo": True
        },
        {
            "name": "GitHub",
            "url": "https://github.com",
            "expected_has_logo": True
        },
        {
            "name": "Canva",
            "url": "https://canva.com",
            "expected_has_logo": True
        },
        {
            "name": "Notion",
            "url": "https://notion.so",
            "expected_has_logo": True
        },
        {
            "name": "Figma",
            "url": "https://figma.com",
            "expected_has_logo": True
        }
    ]
    
    successful_extractions = 0
    total_tests = len(test_websites)
    
    for i, test_case in enumerate(test_websites, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        
        try:
            # Extract logo
            start_time = time.time()
            logo_url = extractor.extract_logo_url(test_case['url'])
            extraction_time = time.time() - start_time
            
            print(f"   Time: {extraction_time:.2f}s")
            
            if logo_url:
                print(f"   ✅ Logo found: {logo_url}")
                
                # Calculate quality score
                quality_score = extractor.get_logo_quality_score(logo_url)
                print(f"   Quality score: {quality_score:.2f}")
                
                successful_extractions += 1
            else:
                print(f"   ❌ No logo found")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
        
        # Small delay to be respectful
        time.sleep(1)
    
    # Calculate success rate
    success_rate = (successful_extractions / total_tests) * 100
    print(f"\n📊 RESULTS:")
    print(f"   Successful extractions: {successful_extractions}/{total_tests}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("   🎉 TARGET SUCCESS RATE ACHIEVED (80%+)")
    else:
        print("   ⚠️  Below target success rate - needs improvement")
    
    return success_rate >= 80

def test_logo_extraction_with_content():
    """Test logo extraction with sample HTML content"""
    
    print("\n🧪 TESTING WITH SAMPLE HTML CONTENT")
    print("=" * 50)
    
    extractor = LogoExtractionService()
    
    # Sample HTML with different logo patterns
    sample_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta property="og:image" content="/images/company-logo.png">
        <meta name="twitter:image" content="/images/social-logo.jpg">
        <link rel="icon" href="/favicon.svg">
    </head>
    <body>
        <header>
            <img src="/logo.svg" alt="Company Logo" class="logo">
            <nav>
                <img src="/brand-icon.png" id="brand-logo">
            </nav>
        </header>
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "logo": "/structured-logo.png"
        }
        </script>
    </body>
    </html>
    """
    
    base_url = "https://example.com"
    
    print(f"Testing with sample HTML content...")
    logo_url = extractor.extract_logo_url(base_url, sample_html)
    
    if logo_url:
        print(f"✅ Logo extracted: {logo_url}")
        quality_score = extractor.get_logo_quality_score(logo_url)
        print(f"Quality score: {quality_score:.2f}")
    else:
        print("❌ No logo found in sample HTML")
    
    return logo_url is not None

if __name__ == "__main__":
    # Test with real websites
    real_website_success = test_logo_extraction()
    
    # Test with sample content
    sample_content_success = test_logo_extraction_with_content()
    
    print(f"\n🏆 OVERALL RESULTS:")
    print(f"   Real websites: {'✅ PASS' if real_website_success else '❌ FAIL'}")
    print(f"   Sample content: {'✅ PASS' if sample_content_success else '❌ FAIL'}")
    
    if real_website_success and sample_content_success:
        print("   🎉 ALL TESTS PASSED!")
    else:
        print("   ⚠️  Some tests failed - needs improvement")
