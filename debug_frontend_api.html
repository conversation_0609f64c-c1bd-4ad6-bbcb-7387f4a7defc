<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Frontend API Debug Tool</h1>
    <p>This tool tests the exact same API calls that the React frontend makes.</p>
    
    <div>
        <button onclick="testSpiders()">Test Spiders API</button>
        <button onclick="testCapabilities()">Test Capabilities API</button>
        <button onclick="testStatus()">Test Status API</button>
        <button onclick="testServices()">Test Services API</button>
        <button onclick="testEnhancedScraping()">Test Enhanced Scraping</button>
        <button onclick="testTraditionalScraping()">Test Traditional Scraping</button>
        <button onclick="testAll()">Test All APIs</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        function addResult(title, success, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, data: { error: error.message } };
            }
        }
        
        async function testSpiders() {
            console.log('Testing spiders API...');
            const result = await makeRequest(`${API_BASE_URL}/api/spiders`);
            addResult('🕷️ Spiders API', result.success, result.data);
            
            if (result.success && result.data.spiders) {
                console.log('Spiders found:', result.data.spiders);
            }
        }
        
        async function testCapabilities() {
            console.log('Testing capabilities API...');
            const result = await makeRequest(`${API_BASE_URL}/api/capabilities`);
            addResult('⚙️ Capabilities API', result.success, result.data);
            
            if (result.success) {
                console.log('Enhanced scraping available:', result.data.enhanced_scraping);
            }
        }
        
        async function testStatus() {
            console.log('Testing status API...');
            const result = await makeRequest(`${API_BASE_URL}/api/status`);
            addResult('📊 Status API', result.success, result.data);
        }
        
        async function testServices() {
            console.log('Testing services API...');
            const result = await makeRequest(`${API_BASE_URL}/api/test-services`);
            addResult('🔧 Services API', result.success, result.data);
        }
        
        async function testEnhancedScraping() {
            console.log('Testing enhanced scraping...');
            const payload = {
                tools: [
                    { name: 'Test Tool', url: 'https://www.test.co' }
                ],
                use_parallel: true,
                use_phase3: true
            };
            
            const result = await makeRequest(`${API_BASE_URL}/api/start-enhanced-scraping`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            addResult('🚀 Enhanced Scraping', result.success, result.data);
        }
        
        async function testTraditionalScraping() {
            console.log('Testing traditional scraping...');
            const payload = {
                spider_name: 'futuretools_complete',
                max_items: 5
            };
            
            const result = await makeRequest(`${API_BASE_URL}/api/start-scraping`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            addResult('🔧 Traditional Scraping', result.success, result.data);
        }
        
        async function testAll() {
            document.getElementById('results').innerHTML = '';
            console.log('Testing all APIs...');
            
            await testSpiders();
            await testCapabilities();
            await testStatus();
            await testServices();
            await testEnhancedScraping();
            await testTraditionalScraping();
            
            console.log('All tests completed!');
        }
        
        // Auto-run tests when page loads
        window.onload = function() {
            console.log('Page loaded, running initial tests...');
            testAll();
        };
    </script>
</body>
</html>
