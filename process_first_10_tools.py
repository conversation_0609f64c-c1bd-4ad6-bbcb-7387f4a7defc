"""
Process First 10 FutureTools with New Advanced Schema
Test and demonstrate the updated comprehensive enhancement system
"""

import json
import sys
import time
sys.path.append('/app')

from enhanced_item_processor import EnhancedItemProcessor
from ai_navigator_client import AINavigatorClient
from data_enrichment_service import DataEnrichmentService
from taxonomy_service import TaxonomyService
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/first_10_tools_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def process_first_10_tools():
    """
    Process the first 10 FutureTools with new advanced schema
    """
    
    print("🚀 PROCESSING FIRST 10 FUTURETOOLS WITH NEW ADVANCED SCHEMA")
    print("=" * 80)
    
    # Initialize enhanced system
    print("🔧 Initializing advanced processing system...")
    client = AINavigatorClient()
    enrichment = DataEnrichmentService('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')
    taxonomy = TaxonomyService(client)
    processor = EnhancedItemProcessor(client, enrichment, taxonomy)
    
    print("✅ System initialized with:")
    print("   • New advanced schema (25+ fields)")
    print("   • Enhanced tool details (16+ fields)")
    print("   • Review sentiment tracking")
    print("   • Technical specifications")
    print("   • Support channel information")
    print("   • PENDING status workflow")
    
    # Load first 10 tools
    tools_file = '/app/ai-navigator-scrapers/futuretools_highvolume_all.jsonl'
    
    print(f"\n📂 Loading first 10 tools from: {tools_file}")
    
    tools = []
    try:
        with open(tools_file, 'r') as f:
            for i, line in enumerate(f):
                if i >= 10:  # Only process first 10
                    break
                try:
                    tool = json.loads(line.strip())
                    tools.append(tool)
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON on line {i+1}: {str(e)}")
                    continue
    except FileNotFoundError:
        print(f"❌ Tools file not found: {tools_file}")
        return False
    
    print(f"📊 Loaded {len(tools)} tools for advanced processing")
    
    # Show sample tools
    print(f"\n📋 Sample tools to process:")
    for i, tool in enumerate(tools, 1):
        print(f"   {i}. {tool.get('tool_name_on_directory', 'Unknown')} -> {tool.get('external_website_url', 'No URL')}")
    
    # Track comprehensive results
    results = {
        'processed': 0,
        'successful': 0,
        'failed': 0,
        'skipped': 0,
        'api_submissions': 0,
        'entities_created': [],
        'advanced_fields': {
            'total_fields': 0,
            'max_fields': 0,
            'avg_fields': 0
        },
        'new_schema_features': {
            'sentiment_analysis': 0,
            'technical_specs': 0,
            'support_channels': 0,
            'deployment_options': 0
        }
    }
    
    print(f"\n🚀 Starting advanced processing of 10 tools...")
    print("-" * 60)
    
    # Process each tool with advanced enhancement
    for i, tool in enumerate(tools, 1):
        tool_name = tool.get('tool_name_on_directory', f'Unknown-{i}')
        original_url = tool.get('external_website_url', '')
        
        print(f"\n📝 [{i}/10] Processing: {tool_name}")
        print(f"   Original URL: {original_url}")
        
        try:
            start_time = time.time()
            
            # Convert to lead format
            lead_item = {
                'tool_name_on_directory': tool_name,
                'external_website_url': original_url,
                'source_directory': 'futuretools.io'
            }
            
            # Process with advanced schema
            entity_dto = processor.process_lead_item(lead_item)
            
            processing_time = time.time() - start_time
            
            if entity_dto:
                # Analyze advanced schema features
                data_fields = len([k for k, v in entity_dto.items() if v is not None and v != '' and v != []])
                tool_details = entity_dto.get('tool_details', {})
                detail_fields = len([k for k, v in tool_details.items() if v is not None and v != '' and v != []])
                total_fields = data_fields + detail_fields
                
                results['advanced_fields']['total_fields'] += total_fields
                results['advanced_fields']['max_fields'] = max(results['advanced_fields']['max_fields'], total_fields)
                
                # Check new schema features
                website_url = entity_dto.get('website_url', '')
                logo_url = entity_dto.get('logo_url', '')
                status = entity_dto.get('status', '')
                
                # Advanced feature analysis
                has_sentiment = entity_dto.get('scraped_review_sentiment_label') is not None
                has_tech_specs = bool(tool_details.get('programming_languages') or tool_details.get('frameworks'))
                has_support = bool(tool_details.get('support_channels'))
                has_deployment = bool(tool_details.get('deployment_options'))
                
                if has_sentiment:
                    results['new_schema_features']['sentiment_analysis'] += 1
                if has_tech_specs:
                    results['new_schema_features']['technical_specs'] += 1
                if has_support:
                    results['new_schema_features']['support_channels'] += 1
                if has_deployment:
                    results['new_schema_features']['deployment_options'] += 1
                
                # Quality metrics
                is_clean_url = '?ref=' not in website_url and '?utm_' not in website_url
                has_logo = bool(logo_url and logo_url.startswith('http'))
                
                print(f"   ✅ Advanced entity created!")
                print(f"   📊 Total fields: {total_fields} (Entity: {data_fields}, Tool Details: {detail_fields})")
                print(f"   🧹 Clean URL: {is_clean_url} -> {website_url}")
                print(f"   🖼️  Logo: {has_logo} -> {logo_url[:60]}..." if logo_url else "   🖼️  Logo: None")
                print(f"   📋 Status: {status}")
                print(f"   ⏱️  Processing time: {processing_time:.1f}s")
                
                # Show new schema features
                print(f"   🆕 New Schema Features:")
                print(f"      • Sentiment Analysis: {'✅' if has_sentiment else '⚪'}")
                print(f"      • Technical Specs: {'✅' if has_tech_specs else '⚪'}")
                print(f"      • Support Channels: {'✅' if has_support else '⚪'}")
                print(f"      • Deployment Options: {'✅' if has_deployment else '⚪'}")
                
                # Show key enhanced data
                key_features = tool_details.get('key_features', [])
                if key_features:
                    print(f"   ⚡ Key Features ({len(key_features)}): {', '.join(key_features[:3])}...")
                
                programming_langs = tool_details.get('programming_languages', [])
                if programming_langs:
                    print(f"   💻 Programming Languages: {', '.join(programming_langs)}")
                
                # Attempt API submission with new schema
                print(f"   🚀 Submitting to API with advanced schema...")
                api_start = time.time()
                try:
                    api_result = client.create_entity(entity_dto)
                    api_time = time.time() - api_start
                    results['api_submissions'] += 1
                    
                    if api_result:
                        results['successful'] += 1
                        entity_id = api_result.get('id', 'N/A')
                        print(f"   🎉 API SUCCESS! Entity ID: {entity_id}")
                        
                        results['entities_created'].append({
                            'name': entity_dto.get('name'),
                            'entity_id': entity_id,
                            'website_url': website_url,
                            'logo_url': logo_url,
                            'status': status,
                            'total_fields': total_fields,
                            'processing_time': processing_time,
                            'api_time': api_time,
                            'has_sentiment': has_sentiment,
                            'has_tech_specs': has_tech_specs,
                            'has_support': has_support
                        })
                    else:
                        results['failed'] += 1
                        print(f"   ❌ API submission failed")
                        logger.error(f"API submission failed for {tool_name}")
                        
                except Exception as api_error:
                    results['failed'] += 1
                    print(f"   ❌ API error: {str(api_error)[:100]}...")
                    logger.error(f"API error for {tool_name}: {str(api_error)}")
                    
            else:
                results['skipped'] += 1
                print(f"   ⏭️  Skipped (likely duplicate)")
            
            results['processed'] += 1
            
            # Respectful delay
            if i < len(tools):
                print(f"   ⏸️  Processing delay...")
                time.sleep(3)
            
        except Exception as e:
            results['failed'] += 1
            results['processed'] += 1
            error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
            print(f"   ❌ Error: {error_msg}")
            logger.error(f"Error processing {tool_name}: {str(e)}")
    
    # Calculate advanced statistics
    if results['processed'] > 0:
        results['advanced_fields']['avg_fields'] = results['advanced_fields']['total_fields'] / results['processed']
    
    # Generate comprehensive report
    print(f"\n" + "=" * 80)
    print(f"📊 ADVANCED SCHEMA PROCESSING COMPLETE - FIRST 10 TOOLS")
    print("=" * 80)
    
    print(f"📈 PROCESSING SUMMARY:")
    print(f"   Total Processed: {results['processed']}")
    print(f"   ✅ Successfully Created: {results['successful']}")
    print(f"   ❌ Failed: {results['failed']}")
    print(f"   ⏭️  Skipped (Duplicates): {results['skipped']}")
    print(f"   🚀 API Submissions: {results['api_submissions']}")
    
    print(f"\n🆕 NEW ADVANCED SCHEMA METRICS:")
    print(f"   📊 Avg Fields per Tool: {results['advanced_fields']['avg_fields']:.1f}")
    print(f"   🏆 Max Fields Achieved: {results['advanced_fields']['max_fields']}")
    print(f"   🔍 Sentiment Analysis: {results['new_schema_features']['sentiment_analysis']}/10 tools")
    print(f"   💻 Technical Specs: {results['new_schema_features']['technical_specs']}/10 tools")
    print(f"   📞 Support Channels: {results['new_schema_features']['support_channels']}/10 tools")
    print(f"   🚀 Deployment Options: {results['new_schema_features']['deployment_options']}/10 tools")
    
    # Success rates
    if results['processed'] > 0:
        api_success_rate = (results['successful'] / results['api_submissions']) * 100 if results['api_submissions'] > 0 else 0
        schema_utilization = (sum(results['new_schema_features'].values()) / (results['processed'] * 4)) * 100
        
        print(f"\n📊 ADVANCED SUCCESS RATES:")
        print(f"   🚀 API Success Rate: {api_success_rate:.1f}%")
        print(f"   🆕 New Schema Utilization: {schema_utilization:.1f}%")
    
    # Show successful entities
    if results['entities_created']:
        print(f"\n🎉 SUCCESSFULLY CREATED ENTITIES ({len(results['entities_created'])}):")
        print("-" * 60)
        
        for i, entity in enumerate(results['entities_created'], 1):
            advanced_indicators = []
            if entity.get('has_sentiment'):
                advanced_indicators.append("📊")
            if entity.get('has_tech_specs'):
                advanced_indicators.append("💻")
            if entity.get('has_support'):
                advanced_indicators.append("📞")
            
            indicators = " ".join(advanced_indicators) if advanced_indicators else "🔧"
            
            print(f"{i:2}. {indicators} {entity['name']}")
            print(f"    🌐 Website: {entity['website_url']}")
            print(f"    🖼️  Logo: ✅ {entity['logo_url'][:50]}...")
            print(f"    📊 Fields: {entity['total_fields']}")
            print(f"    📋 Status: {entity['status']}")
            print(f"    🆔 Entity ID: {entity['entity_id']}")
            print()
    
    print(f"🎯 ADVANCED SCHEMA VALIDATION:")
    print(f"   ✅ New schema structure: Working")
    print(f"   ✅ Enhanced tool details: {results['advanced_fields']['avg_fields']:.1f} fields avg")
    print(f"   ✅ PENDING status: All entities")
    print(f"   ✅ Advanced features: {sum(results['new_schema_features'].values())} total")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. ✅ First 10 tools processed with advanced schema")
    print(f"   2. 📈 Scale to batches of 50-100 tools")
    print(f"   3. 🌟 Process all 1,877 FutureTools")
    print(f"   4. 🎯 Build world's most comprehensive AI tool directory")
    
    return results

def main():
    """
    Main execution function
    """
    try:
        results = process_first_10_tools()
        
        # Log final results
        logger.info(f"First 10 tools processing completed: {results['successful']} successful, {results['failed']} failed")
        
        print(f"\n💾 Processing log saved to: /app/first_10_tools_processing.log")
        print(f"🎉 FIRST 10 TOOLS PROCESSED WITH ADVANCED SCHEMA!")
        
        # Return success status
        return results['successful'] > 5  # Success if we created 5+ entities
        
    except KeyboardInterrupt:
        print(f"\n⏸️  Processing interrupted by user")
        logger.info("Processing interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        logger.error(f"Fatal error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 Ready to scale to all 1,877 tools!" if success else "\n⚠️  Need to review and fix issues")
    exit_code = 0 if success else 1
    exit(exit_code)