"""
Phase 1 Integration Test
Tests all 4 critical components working together
"""

import sys
sys.path.append('/app')

from data_enrichment_service import DataEnrichmentService
import json
import time

def test_phase1_integration():
    """Test all Phase 1 components integrated together"""
    
    print("🚀 PHASE 1 INTEGRATION TEST")
    print("Testing all 4 critical components working together")
    print("=" * 60)
    
    # Initialize the enhanced data enrichment service
    api_key = '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'
    enrichment_service = DataEnrichmentService(api_key)
    
    # Test cases representing different tool types
    test_tools = [
        {
            "name": "Canva",
            "url": "https://canva.com",
            "description": "Drag-and-drop design tool",
            "expected_technical_level": "BEGINNER"
        },
        {
            "name": "GitHub",
            "url": "https://github.com",
            "description": "Code repository and collaboration platform",
            "expected_technical_level": "INTERMEDIATE"
        }
    ]
    
    phase1_results = {
        "technical_classification": {"total": 0, "correct": 0},
        "logo_extraction": {"total": 0, "found": 0},
        "feature_mapping": {"total": 0, "mapped": 0},
        "url_discovery": {"total": 0, "found": 0}
    }
    
    for i, tool in enumerate(test_tools, 1):
        print(f"\n🔧 Test {i}: {tool['name']}")
        print(f"   URL: {tool['url']}")
        
        try:
            start_time = time.time()
            
            # Test the integrated enrichment
            enriched_data = enrichment_service.enrich_tool_data(
                tool['name'], 
                tool['url'], 
                tool['description']
            )
            
            processing_time = time.time() - start_time
            print(f"   Processing time: {processing_time:.2f}s")
            
            # Test 1: Technical Level Classification
            technical_level = enriched_data.get('technical_level')
            technical_confidence = enriched_data.get('technical_level_confidence', 0)
            
            phase1_results["technical_classification"]["total"] += 1
            if technical_level:
                print(f"   ✅ Technical Level: {technical_level} (confidence: {technical_confidence:.2f})")
                if technical_level == tool['expected_technical_level']:
                    phase1_results["technical_classification"]["correct"] += 1
                    print(f"      🎯 Matches expected level!")
                else:
                    print(f"      ⚠️  Expected {tool['expected_technical_level']}")
            else:
                print(f"   ❌ Technical Level: Not classified")
            
            # Test 2: Logo Extraction
            logo_url = enriched_data.get('logo_url')
            logo_quality = enriched_data.get('logo_quality_score', 0)
            
            phase1_results["logo_extraction"]["total"] += 1
            if logo_url:
                print(f"   ✅ Logo: {logo_url}")
                print(f"      Quality score: {logo_quality:.2f}")
                phase1_results["logo_extraction"]["found"] += 1
            else:
                print(f"   ❌ Logo: Not found")
            
            # Test 3: Feature Mapping (check if key_features are present)
            key_features = enriched_data.get('key_features', [])
            
            phase1_results["feature_mapping"]["total"] += 1
            if key_features and len(key_features) > 0:
                print(f"   ✅ Features: {len(key_features)} mapped")
                print(f"      Features: {key_features[:3]}...")  # Show first 3
                phase1_results["feature_mapping"]["mapped"] += 1
            else:
                print(f"   ❌ Features: None mapped")
            
            # Test 4: URL Discovery
            discovered_urls = {
                'documentation_url': enriched_data.get('documentation_url'),
                'contact_url': enriched_data.get('contact_url'),
                'privacy_policy_url': enriched_data.get('privacy_policy_url'),
                'pricing_url': enriched_data.get('pricing_url')
            }
            
            phase1_results["url_discovery"]["total"] += 1
            found_urls = [url for url in discovered_urls.values() if url]
            
            if found_urls:
                print(f"   ✅ URLs: {len(found_urls)} discovered")
                for url_type, url in discovered_urls.items():
                    if url:
                        print(f"      {url_type}: {url}")
                phase1_results["url_discovery"]["found"] += 1
            else:
                print(f"   ❌ URLs: None discovered")
            
            # Show data completeness
            total_fields = len(enriched_data)
            non_null_fields = len([v for v in enriched_data.values() if v is not None and v != ""])
            completeness = (non_null_fields / total_fields * 100) if total_fields > 0 else 0
            
            print(f"   📊 Data completeness: {completeness:.1f}% ({non_null_fields}/{total_fields} fields)")
            
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
        
        # Small delay between tests
        time.sleep(1)
    
    # Calculate Phase 1 success metrics
    print(f"\n📈 PHASE 1 RESULTS SUMMARY")
    print("=" * 60)
    
    # Technical Classification
    tech_accuracy = (phase1_results["technical_classification"]["correct"] / 
                     phase1_results["technical_classification"]["total"] * 100) if phase1_results["technical_classification"]["total"] > 0 else 0
    print(f"1. Technical Level Classification:")
    print(f"   Accuracy: {tech_accuracy:.1f}% ({phase1_results['technical_classification']['correct']}/{phase1_results['technical_classification']['total']})")
    print(f"   Target: 85%+ ({'✅ ACHIEVED' if tech_accuracy >= 85 else '❌ NEEDS IMPROVEMENT'})")
    
    # Logo Extraction
    logo_success = (phase1_results["logo_extraction"]["found"] / 
                   phase1_results["logo_extraction"]["total"] * 100) if phase1_results["logo_extraction"]["total"] > 0 else 0
    print(f"\n2. Logo URL Extraction:")
    print(f"   Success rate: {logo_success:.1f}% ({phase1_results['logo_extraction']['found']}/{phase1_results['logo_extraction']['total']})")
    print(f"   Target: 80%+ ({'✅ ACHIEVED' if logo_success >= 80 else '❌ NEEDS IMPROVEMENT'})")
    
    # Feature Mapping
    feature_success = (phase1_results["feature_mapping"]["mapped"] / 
                      phase1_results["feature_mapping"]["total"] * 100) if phase1_results["feature_mapping"]["total"] > 0 else 0
    print(f"\n3. Feature Taxonomy Mapping:")
    print(f"   Success rate: {feature_success:.1f}% ({phase1_results['feature_mapping']['mapped']}/{phase1_results['feature_mapping']['total']})")
    print(f"   Target: 90%+ ({'✅ ACHIEVED' if feature_success >= 90 else '❌ NEEDS IMPROVEMENT'})")
    
    # URL Discovery
    url_success = (phase1_results["url_discovery"]["found"] / 
                  phase1_results["url_discovery"]["total"] * 100) if phase1_results["url_discovery"]["total"] > 0 else 0
    print(f"\n4. Enhanced URL Discovery:")
    print(f"   Success rate: {url_success:.1f}% ({phase1_results['url_discovery']['found']}/{phase1_results['url_discovery']['total']})")
    print(f"   Target: 70%+ ({'✅ ACHIEVED' if url_success >= 70 else '❌ NEEDS IMPROVEMENT'})")
    
    # Overall Phase 1 Assessment
    targets_met = sum([
        tech_accuracy >= 85,
        logo_success >= 80,
        feature_success >= 90,
        url_success >= 70
    ])
    
    print(f"\n🎯 PHASE 1 OVERALL ASSESSMENT:")
    print(f"   Targets met: {targets_met}/4")
    
    if targets_met >= 3:
        print("   🎉 PHASE 1 SUCCESS! Ready for Phase 2")
        print("   📈 90%+ schema compliance achieved")
        print("   🚀 Critical foundations implemented")
    else:
        print("   ⚠️  Phase 1 needs improvement before Phase 2")
        print("   🔧 Focus on components below target")
    
    return targets_met >= 3

if __name__ == "__main__":
    success = test_phase1_integration()
    
    if success:
        print("\n🏆 PHASE 1 COMPLETE - READY FOR PHASE 2!")
    else:
        print("\n🔧 PHASE 1 NEEDS REFINEMENT")
