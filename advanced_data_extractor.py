"""
Advanced Data Extractor - Clean social links and comprehensive descriptions
"""

import re
import requests
import logging
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup

class AdvancedDataExtractor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Clean social media patterns (extract handles only)
        self.social_patterns = {
            'twitter': [
                r'twitter\.com/([a-zA-Z0-9_]{1,15})(?:\?|$|/)',
                r'x\.com/([a-zA-Z0-9_]{1,15})(?:\?|$|/)',
                r'@([a-zA-Z0-9_]{1,15})',
            ],
            'linkedin': [
                r'linkedin\.com/company/([a-zA-Z0-9\-]{1,50})(?:\?|$|/)',
                r'linkedin\.com/in/([a-zA-Z0-9\-]{1,50})(?:\?|$|/)',
            ],
            'github': [
                r'github\.com/([a-zA-Z0-9\-_]{1,39})(?:\?|$|/)',
            ],
            'youtube': [
                r'youtube\.com/c/([a-zA-Z0-9\-_]{1,100})(?:\?|$|/)',
                r'youtube\.com/channel/([a-zA-Z0-9\-_]{1,100})(?:\?|$|/)', 
                r'youtube\.com/@([a-zA-Z0-9\-_]{1,100})(?:\?|$|/)',
                r'youtube\.com/user/([a-zA-Z0-9\-_]{1,100})(?:\?|$|/)',
            ],
            'facebook': [
                r'facebook\.com/([a-zA-Z0-9\-_.]{1,100})(?:\?|$|/)',
                r'fb\.com/([a-zA-Z0-9\-_.]{1,100})(?:\?|$|/)',
            ],
            'instagram': [
                r'instagram\.com/([a-zA-Z0-9\-_.]{1,30})(?:\?|$|/)',
            ],
            'discord': [
                r'discord\.gg/([a-zA-Z0-9]{1,20})',
                r'discord\.com/invite/([a-zA-Z0-9]{1,20})',
            ]
        }
        
        # Patterns to exclude from social links (common false positives)
        self.social_exclusions = {
            'twitter': ['home', 'login', 'signup', 'privacy', 'terms', 'intent', 'share', 'hashtag'],
            'linkedin': ['login', 'signup', 'help', 'legal', 'privacy', 'sales', 'talent'],
            'github': ['login', 'join', 'pricing', 'features', 'enterprise', 'team'],
            'youtube': ['watch', 'playlist', 'channel', 'results', 'user'],
            'facebook': ['login', 'signup', 'help', 'privacy', 'pages', 'business'],
            'instagram': ['accounts', 'explore', 'direct'],
            'discord': ['download', 'nitro', 'safety', 'company']
        }

    def extract_clean_social_links(self, website_content: str, website_url: str = "") -> Dict[str, str]:
        """
        Extract clean social media handles/usernames from website content
        Returns clean handles, not full URLs or HTML fragments
        """
        clean_links = {}
        
        # Combine website content and URL for analysis
        full_content = f"{website_content} {website_url}".lower()
        
        for platform, patterns in self.social_patterns.items():
            found_handles = []
            
            for pattern in patterns:
                matches = re.findall(pattern, full_content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        # Clean the match
                        clean_handle = self._clean_social_handle(match, platform)
                        if clean_handle and self._is_valid_social_handle(clean_handle, platform):
                            found_handles.append(clean_handle)
            
            # Take the most common handle (in case of multiple matches)
            if found_handles:
                # Get the most frequent handle
                handle_counts = {}
                for handle in found_handles:
                    handle_counts[handle] = handle_counts.get(handle, 0) + 1
                
                best_handle = max(handle_counts, key=handle_counts.get)
                clean_links[platform] = best_handle
                
                self.logger.info(f"Found {platform} handle: {best_handle}")
        
        return clean_links

    def _clean_social_handle(self, handle: str, platform: str) -> Optional[str]:
        """Clean and validate social media handle"""
        if not handle:
            return None
            
        # Remove common prefixes and suffixes
        handle = handle.strip('/@#.,;:()[]{}"\' \t\n\r')
        
        # Remove URL query parameters
        if '?' in handle:
            handle = handle.split('?')[0]
            
        # Remove URL fragments
        if '#' in handle:
            handle = handle.split('#')[0]
            
        # Platform-specific cleaning
        if platform == 'twitter':
            # Remove common Twitter URL patterns
            handle = re.sub(r'/(status|lists|followers|following).*', '', handle)
            
        elif platform == 'linkedin':
            # Remove LinkedIn URL parameters
            handle = re.sub(r'/\?.*', '', handle)
            
        elif platform == 'youtube':
            # Clean YouTube handles
            handle = re.sub(r'/(videos|playlists|community|channels|about).*', '', handle)
            
        # Final cleanup
        handle = handle.strip('/')
        
        return handle if handle else None

    def _is_valid_social_handle(self, handle: str, platform: str) -> bool:
        """Validate if the handle is a real social media handle"""
        if not handle or len(handle) < 1:
            return False
            
        # Check against exclusion list
        exclusions = self.social_exclusions.get(platform, [])
        if handle.lower() in exclusions:
            return False
            
        # Platform-specific validation
        if platform == 'twitter':
            # Twitter handles: 1-15 chars, alphanumeric + underscore
            return re.match(r'^[a-zA-Z0-9_]{1,15}$', handle) is not None
            
        elif platform == 'linkedin':
            # LinkedIn: alphanumeric + hyphens, reasonable length
            return re.match(r'^[a-zA-Z0-9\-]{1,50}$', handle) is not None
            
        elif platform == 'github':
            # GitHub: alphanumeric + hyphens/underscores, 1-39 chars
            return re.match(r'^[a-zA-Z0-9\-_]{1,39}$', handle) is not None
            
        elif platform == 'youtube':
            # YouTube: various formats, reasonable length
            return len(handle) <= 100 and not handle.startswith(('UC', 'watch'))
            
        elif platform == 'discord':
            # Discord invite codes: alphanumeric, usually 5-20 chars
            return re.match(r'^[a-zA-Z0-9]{5,20}$', handle) is not None
            
        # Default validation
        return len(handle) <= 100 and re.match(r'^[a-zA-Z0-9\-_\.]{1,100}$', handle) is not None

    def create_comprehensive_description(self, enhanced_data: Dict[str, Any], website_data: Dict[str, Any], entity_type: str = 'ai-tool') -> str:
        """
        Create extremely detailed descriptions from multiple sources
        Target: 400-600 characters with maximum detail
        """
        
        description_sources = []
        
        # Primary enhanced description
        if enhanced_data.get('description'):
            desc = enhanced_data['description'].strip()
            if len(desc) > 50:  # Only use substantial descriptions
                description_sources.append(desc)
        
        # Website meta description
        if website_data.get('meta_description'):
            meta_desc = website_data['meta_description'].strip()
            if len(meta_desc) > 30 and meta_desc not in str(description_sources):
                description_sources.append(meta_desc)
        
        # Website about text
        if website_data.get('about_text'):
            about = website_data['about_text'].strip()
            if len(about) > 50 and about not in str(description_sources):
                description_sources.append(about)
        
        # Add entity-type-specific enhancements
        type_specific_additions = self._get_type_specific_additions(enhanced_data, entity_type)
        description_sources.extend(type_specific_additions)
        
        # Combine sources intelligently
        if description_sources:
            # Start with the longest, most detailed source
            description_sources.sort(key=len, reverse=True)
            combined = description_sources[0]
            
            # Add unique information from other sources
            for source in description_sources[1:]:
                if len(combined) < 400:  # Still room for more detail
                    # Extract unique sentences
                    source_sentences = [s.strip() for s in source.split('.') if s.strip()]
                    for sentence in source_sentences:
                        if (sentence not in combined and 
                            len(sentence) > 20 and 
                            len(combined + '. ' + sentence) <= 580):
                            combined += '. ' + sentence
        else:
            # Fallback: create from available data
            combined = self._create_fallback_description(enhanced_data, entity_type)
        
        # Ensure minimum quality and length
        if len(combined) < 200:
            combined = self._enhance_short_description(combined, enhanced_data, entity_type)
        
        # Final cleanup and validation
        combined = self._cleanup_description(combined)
        
        # Ensure maximum length
        if len(combined) > 600:
            combined = combined[:597] + "..."
            
        return combined

    def _get_type_specific_additions(self, enhanced_data: Dict[str, Any], entity_type: str) -> List[str]:
        """Get entity-type-specific description additions"""
        additions = []
        
        if entity_type == 'ai-tool':
            # Add features and use cases for AI tools
            if enhanced_data.get('key_features'):
                features_text = f"Key features include {', '.join(enhanced_data['key_features'][:4])}"
                additions.append(features_text)
                
            if enhanced_data.get('use_cases'):
                use_cases_text = f"Primary use cases include {', '.join(enhanced_data['use_cases'][:3])}"
                additions.append(use_cases_text)
                
            if enhanced_data.get('target_audience'):
                audience_text = f"Designed for {', '.join(enhanced_data['target_audience'][:3])}"
                additions.append(audience_text)
                
        elif entity_type == 'research-paper':
            # Add academic details
            if enhanced_data.get('research_areas'):
                areas_text = f"Research areas include {', '.join(enhanced_data['research_areas'][:3])}"
                additions.append(areas_text)
                
            if enhanced_data.get('methodology'):
                method_text = f"Methodology: {enhanced_data['methodology']}"
                additions.append(method_text)
                
        elif entity_type == 'hardware':
            # Add technical specifications
            specs = enhanced_data.get('specifications', {})
            if specs:
                spec_items = []
                for key, value in list(specs.items())[:3]:
                    spec_items.append(f"{key}: {value}")
                if spec_items:
                    specs_text = f"Specifications include {', '.join(spec_items)}"
                    additions.append(specs_text)
                    
        elif entity_type == 'job':
            # Add job details
            if enhanced_data.get('key_responsibilities'):
                resp_text = f"Key responsibilities include {', '.join(enhanced_data['key_responsibilities'][:3])}"
                additions.append(resp_text)
                
            if enhanced_data.get('required_skills'):
                skills_text = f"Required skills: {', '.join(enhanced_data['required_skills'][:4])}"
                additions.append(skills_text)
                
        elif entity_type == 'event':
            # Add event details
            if enhanced_data.get('topics'):
                topics_text = f"Topics covered include {', '.join(enhanced_data['topics'][:3])}"
                additions.append(topics_text)
                
            if enhanced_data.get('key_speakers'):
                speakers_text = f"Featured speakers include {', '.join(enhanced_data['key_speakers'][:2])}"
                additions.append(speakers_text)
        
        return additions

    def _create_fallback_description(self, enhanced_data: Dict[str, Any], entity_type: str) -> str:
        """Create fallback description when no good sources available"""
        name = enhanced_data.get('name', 'This solution')
        
        if entity_type == 'ai-tool':
            return f"{name} is an innovative AI-powered platform designed to enhance productivity and streamline workflows through intelligent automation capabilities."
            
        elif entity_type == 'research-paper':
            return f"{name} presents cutting-edge research findings and methodological contributions to advance understanding in the field."
            
        elif entity_type == 'hardware':
            return f"{name} delivers high-performance computing capabilities designed for demanding applications and professional workflows."
            
        elif entity_type == 'job':
            return f"{name} represents an exciting career opportunity offering professional growth and the chance to work with cutting-edge technologies."
            
        elif entity_type == 'event':
            return f"{name} brings together industry experts and practitioners to share knowledge and explore the latest developments in the field."
            
        else:
            return f"{name} provides comprehensive solutions designed to meet the evolving needs of modern organizations and professionals."

    def _enhance_short_description(self, description: str, enhanced_data: Dict[str, Any], entity_type: str) -> str:
        """Enhance short descriptions to meet minimum quality standards"""
        
        # Add more context based on available data
        enhancements = []
        
        if enhanced_data.get('categories'):
            enhancements.append(f"specializing in {enhanced_data['categories'][0].lower()}")
            
        if enhanced_data.get('pricing_model'):
            pricing = enhanced_data['pricing_model'].lower()
            if pricing in ['free', 'freemium']:
                enhancements.append("offering free access to core features")
            elif pricing == 'subscription':
                enhancements.append("available through subscription model")
                
        if enhanced_data.get('target_audience'):
            audience = enhanced_data['target_audience'][0]
            enhancements.append(f"designed for {audience.lower()}")
        
        # Add enhancements to description
        if enhancements:
            description += f", {', '.join(enhancements[:2])}"
            
        return description

    def _cleanup_description(self, description: str) -> str:
        """Final cleanup of description text"""
        
        # Remove duplicate sentences
        sentences = description.split('. ')
        unique_sentences = []
        seen = set()
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and sentence.lower() not in seen and len(sentence) > 10:
                unique_sentences.append(sentence)
                seen.add(sentence.lower())
        
        # Rejoin sentences
        description = '. '.join(unique_sentences)
        
        # Fix common issues
        description = re.sub(r'\s+', ' ', description)  # Multiple spaces
        description = re.sub(r'\.+', '.', description)  # Multiple periods
        description = description.strip()
        
        # Ensure proper ending
        if description and not description.endswith('.'):
            description += '.'
            
        return description

    def extract_additional_urls(self, website_content: str, base_url: str) -> Dict[str, Optional[str]]:
        """Extract additional URLs like documentation, contact, privacy policy"""
        
        urls = {
            'documentation_url': None,
            'contact_url': None,
            'privacy_policy_url': None,
            'pricing_url': None,
            'support_url': None
        }
        
        try:
            soup = BeautifulSoup(website_content, 'html.parser')
            
            # Find all links
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link.get('href', '').lower()
                text = link.get_text('', strip=True).lower()
                
                # Convert relative URLs to absolute
                if href.startswith('/'):
                    href = urljoin(base_url, href)
                elif href.startswith('#') or not href.startswith('http'):
                    continue
                
                # Documentation
                if not urls['documentation_url'] and self._is_documentation_link(href, text):
                    urls['documentation_url'] = href
                
                # Contact
                elif not urls['contact_url'] and self._is_contact_link(href, text):
                    urls['contact_url'] = href
                
                # Privacy Policy
                elif not urls['privacy_policy_url'] and self._is_privacy_link(href, text):
                    urls['privacy_policy_url'] = href
                
                # Pricing
                elif not urls['pricing_url'] and self._is_pricing_link(href, text):
                    urls['pricing_url'] = href
                
                # Support
                elif not urls['support_url'] and self._is_support_link(href, text):
                    urls['support_url'] = href
        
        except Exception as e:
            self.logger.warning(f"Error extracting additional URLs: {e}")
        
        return urls

    def _is_documentation_link(self, href: str, text: str) -> bool:
        """Check if link is documentation"""
        doc_indicators = ['docs', 'documentation', 'api', 'guide', 'tutorial', 'help']
        return any(indicator in href for indicator in doc_indicators) or \
               any(indicator in text for indicator in doc_indicators)

    def _is_contact_link(self, href: str, text: str) -> bool:
        """Check if link is contact"""
        contact_indicators = ['contact', 'support', 'help', 'email']
        return any(indicator in href for indicator in contact_indicators) or \
               any(indicator in text for indicator in contact_indicators)

    def _is_privacy_link(self, href: str, text: str) -> bool:
        """Check if link is privacy policy"""
        privacy_indicators = ['privacy', 'policy', 'terms']
        return any(indicator in href for indicator in privacy_indicators) or \
               any(indicator in text for indicator in privacy_indicators)

    def _is_pricing_link(self, href: str, text: str) -> bool:
        """Check if link is pricing"""
        pricing_indicators = ['pricing', 'price', 'cost', 'plan', 'subscription']
        return any(indicator in href for indicator in pricing_indicators) or \
               any(indicator in text for indicator in pricing_indicators)

    def _is_support_link(self, href: str, text: str) -> bool:
        """Check if link is support"""
        support_indicators = ['support', 'help', 'faq', 'customer']
        return any(indicator in href for indicator in support_indicators) or \
               any(indicator in text for indicator in support_indicators)