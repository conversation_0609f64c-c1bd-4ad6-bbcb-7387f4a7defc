#!/usr/bin/env python3
"""
Test script to verify feature_ids are being processed and sent correctly
"""

import logging
import json
from ai_navigator_client import AINavigatorClient
from enhanced_item_processor import EnhancedItemProcessor
from backend.enrichment import DataEnrichmentService
from enhanced_taxonomy_service import EnhancedTaxonomyService

# Configure logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_feature_ids_flow():
    """Test that feature_ids are being processed and sent correctly"""
    
    print("🧪 Testing Feature IDs Flow")
    print("=" * 50)
    
    try:
        # Initialize services
        print("1. Initializing services...")
        ai_client = AINavigatorClient()
        enrichment_service = DataEnrichmentService("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")
        taxonomy_service = EnhancedTaxonomyService(ai_client)
        item_processor = EnhancedItemProcessor(ai_client, enrichment_service, taxonomy_service)
        
        # Test data with explicit features
        test_lead = {
            'tool_name_on_directory': 'Test AI Tool',
            'external_website_url': 'https://example.com',
            'source_directory': 'test',
            'scraped_date': '2024-01-01 12:00:00'
        }
        
        print(f"2. Processing test tool: {test_lead['tool_name_on_directory']}")
        
        # Process the lead item
        entity_dto = item_processor.process_lead_item(test_lead)
        
        if entity_dto:
            print("3. ✅ Entity DTO created successfully!")
            
            # Check if feature_ids are present
            feature_ids = entity_dto.get('feature_ids', [])
            print(f"   📊 Feature IDs found: {len(feature_ids)}")
            
            if feature_ids:
                print(f"   🎯 Feature IDs: {feature_ids}")
                
                # Test the API call (but don't actually submit)
                print("4. Testing API payload structure...")
                
                # Show what would be sent to the API
                api_payload = {
                    "name": entity_dto.get('name'),
                    "website_url": entity_dto.get('website_url'),
                    "feature_ids": feature_ids,
                    "category_ids": entity_dto.get('category_ids', []),
                    "tag_ids": entity_dto.get('tag_ids', []),
                    "status": "ACTIVE"
                }
                
                print("   📤 API Payload structure:")
                print(json.dumps(api_payload, indent=2))
                
                print("\n✅ SUCCESS: feature_ids are being processed and included in the payload!")
                print(f"   - {len(feature_ids)} feature IDs would be sent to POST /entities")
                print(f"   - Features are being created/found via taxonomy manager")
                print(f"   - Entity DTO includes feature_ids field")
                
            else:
                print("   ⚠️  No feature IDs found in entity DTO")
                print("   This could be normal if no features were extracted/created")
                
                # Show what features were suggested
                print("\n   🔍 Debugging feature extraction...")
                # We'd need to add more debugging here to see what features were suggested
                
        else:
            print("❌ Entity DTO creation failed")
            
    except Exception as e:
        print(f"❌ Error in test: {str(e)}")
        import traceback
        traceback.print_exc()

def test_taxonomy_manager_features():
    """Test taxonomy manager feature creation directly"""
    
    print("\n🧪 Testing Taxonomy Manager Feature Creation")
    print("=" * 50)
    
    try:
        # Initialize AI client and taxonomy manager
        ai_client = AINavigatorClient()
        
        # Test feature processing
        test_features = ["Text Generation", "API Integration", "Real-time Processing"]
        
        print(f"Testing feature processing for: {test_features}")
        
        # Process taxonomy suggestions
        taxonomy_result = ai_client.process_taxonomy_suggestions(
            suggested_categories=["AI Tools"],
            suggested_features=test_features,
            suggested_tags=["Machine Learning"]
        )
        
        print(f"✅ Taxonomy processing complete:")
        print(f"   - Categories: {len(taxonomy_result.categories)}")
        print(f"   - Features: {len(taxonomy_result.features)}")
        print(f"   - Tags: {len(taxonomy_result.tags)}")
        
        if taxonomy_result.features:
            print(f"   🎯 Feature UUIDs:")
            for feature in taxonomy_result.features:
                print(f"      - {feature.name}: {feature.uuid}")
        
        if taxonomy_result.errors:
            print(f"   ⚠️  Errors: {taxonomy_result.errors}")
            
    except Exception as e:
        print(f"❌ Error testing taxonomy manager: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_feature_ids_flow()
    test_taxonomy_manager_features()