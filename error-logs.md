prisma:error 
Invalid `prisma.entity.create()` invocation:
Unique constraint failed on the fields: (`name`)
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"PrismaClientKnownRequestError"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"\nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"code\": \"P2002\",\n  \"meta\": {\n    \"modelName\": \"Entity\",\n    \"target\": [\n      \"name\"\n    ]\n  },\n  \"clientVersion\": \"6.10.1\",\n  \"name\": \"PrismaClientKnownRequestError\"\n}"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Unexpected exception caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"CorrelationId: 54d2d068-f89b-4d36-8025-2750bfdb1e9f, ExceptionType: PrismaClientKnownRequestError, Message: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"Stack: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"ERROR","message":"OriginalException: {\"code\":\"P2002\",\"meta\":{\"modelName\":\"Entity\",\"target\":[\"name\"]},\"clientVersion\":\"6.10.1\",\"name\":\"PrismaClientKnownRequestError\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:07.399Z","level":"INFO","message":"Request completed: POST /entities","correlationId":"54d2d068-f89b-4d36-8025-2750bfdb1e9f","ip":"::ffff:***********","userAgent":"python-requests/2.32.3","method":"POST","url":"/entities","statusCode":500,"responseTime":125,"type":"response"}
prisma:query ROLLBACK
{"timestamp":"2025-07-07T07:55:09.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"664f78ea-4ca2-48d7-9452-fed8339d63df","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:09.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"664f78ea-4ca2-48d7-9452-fed8339d63df","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:09.693Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"e84979f9-74a1-454b-b9ba-dfaf32280129","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:09.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"e84979f9-74a1-454b-b9ba-dfaf32280129","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":0,"type":"response"}
--- INCOMING POST /entities REQUEST ---
Content-Type: application/json
Body type: undefined
Body: undefined
{"timestamp":"2025-07-07T07:55:13.021Z","level":"INFO","message":"Incoming request: POST /entities","correlationId":"91b10f47-53cc-4fa8-9449-5398b3a251f8","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"POST","url":"/entities","type":"request"}
--- SUPABASE AUTH GUARD ACTIVATED ---
Authorization header: Present
JWT Token extracted from header
[SupabaseAuthGuard] Supabase JWT validation successful for user: a7ed2b50-6c3d-4568-a35a-466504c037b2
[SupabaseAuthGuard] Attempting to find user in database...
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id", "public"."users"."auth_user_id", "public"."users"."username", "public"."users"."display_name", "public"."users"."email", "public"."users"."role"::text, "public"."users"."status"::text, "public"."users"."technical_level"::text, "public"."users"."profile_picture_url", "public"."users"."bio", "public"."users"."social_links", "public"."users"."created_at", "public"."users"."updated_at", "public"."users"."last_login", "public"."users"."bookmarks_count", "public"."users"."reputation_score", "public"."users"."requests_fulfilled", "public"."users"."requests_made", "public"."users"."reviews_count", "public"."users"."tools_approved", "public"."users"."tools_submitted" FROM "public"."users" WHERE ("public"."users"."auth_user_id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query COMMIT
[SupabaseAuthGuard] User profile found in DB for authUserId: a7ed2b50-6c3d-4568-a35a-466504c037b2, public.users ID: 758268a4-a99c-4413-a83f-34e5f70c848c
--- SUPABASE AUTH GUARD SUCCESS ---
{"timestamp":"2025-07-07T07:55:13.083Z","level":"INFO","message":"--- ENTITIES_CONTROLLER.CREATE METHOD ENTERED ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.083Z","level":"INFO","message":"User ID: 758268a4-a99c-4413-a83f-34e5f70c848c","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.083Z","level":"INFO","message":"DTO Type: object","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.083Z","level":"INFO","message":"DTO Constructor: CreateEntityDto","correlationId":"unknown"}
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 1: Entered create method for entity: "Gadget"
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 1a: Submitter user ID: 758268a4-a99c-4413-a83f-34e5f70c848c
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 1b: Entity type ID: fd181400-c9e6-431c-a8bd-c068d0491aba
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 2: Validating entity submission rate limits...
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id", "public"."users"."role"::text FROM "public"."users" WHERE ("public"."users"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 2: Rate limit validation passed.
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 3: Received entity_type_id: fd181400-c9e6-431c-a8bd-c068d0491aba
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 3a: Current entityTypeMap (size 19): [["5662e324-c5ba-4fb0-9738-d8ea39b63e1e","api"],["203628d4-e8e0-4e69-a22a-f4f1e00e76c5","model"],["8f7f4e42-0c10-4f11-963d-f4ce24e48e51","library"],["7bda8a8d-34d2-49c4-b514-8f0fedebe917","service"],["cdbea80d-d825-4844-bea2-5c7569f9ea12","test-rel-type-prisma"],["279a4f9b-9f10-4439-9bc9-a9aca497d2a0","agency"],["30a3464d-92dd-4bd2-afa3-925ddce3b986","software"],["4786b380-6381-419f-aff3-ebc47848ac7e","job"],["562bfe61-3cbb-4c1f-80f9-aadfd908009d","podcast"],["cca19303-7108-4a0e-a52d-3c259304e711","grant"],["fd181400-c9e6-431c-a8bd-c068d0491aba","ai-tool"],["a4bdf0a2-91ca-4e36-9734-41823de89323","course"],["0a117088-50cf-46d8-ac79-3e15ac7acfaf","dataset"],["90f0c9d5-bf89-4401-a5a7-c7b6005d396a","research-paper"],["7697eb38-36a9-46a9-9d17-c8a2f2da8dcd","platform"],["a5a0fbe1-38ea-4dc6-b02b-53260084ea55","hardware"],["de02661f-999e-4bc1-b5d9-d9005c6b5cfa","newsletter"],["d9815a04-0f04-4c67-9b7a-f629210fc48e","community"],["015f5cea-5b0f-495e-b4f2-07c70428daa4","event"]]
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 3b: Resolved entityTypeSlug: ai-tool
prisma:query COMMIT
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."entities"."id", "public"."entities"."entity_type_id", "public"."entities"."name", "public"."entities"."short_description", "public"."entities"."description", "public"."entities"."logo_url", "public"."entities"."website_url", "public"."entities"."documentation_url", "public"."entities"."contact_url", "public"."entities"."privacy_policy_url", "public"."entities"."founded_year", "public"."entities"."social_links", "public"."entities"."status"::text, "public"."entities"."avg_rating", "public"."entities"."review_count", "public"."entities"."upvote_count", "public"."entities"."created_at", "public"."entities"."updated_at", "public"."entities"."legacy_id", "public"."entities"."submitter_id", "public"."entities"."affiliateStatus"::text, "public"."entities"."location_summary", "public"."entities"."meta_description", "public"."entities"."meta_title", "public"."entities"."ref_link", "public"."entities"."scraped_review_count", "public"."entities"."scraped_review_sentiment_label", "public"."entities"."scraped_review_sentiment_score", "public"."entities"."employee_count_range"::text, "public"."entities"."funding_stage"::text, "public"."entities"."slug" FROM "public"."entities" WHERE ("public"."entities"."slug" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query COMMIT
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."entities"."id", "public"."entities"."entity_type_id", "public"."entities"."name", "public"."entities"."short_description", "public"."entities"."description", "public"."entities"."logo_url", "public"."entities"."website_url", "public"."entities"."documentation_url", "public"."entities"."contact_url", "public"."entities"."privacy_policy_url", "public"."entities"."founded_year", "public"."entities"."social_links", "public"."entities"."status"::text, "public"."entities"."avg_rating", "public"."entities"."review_count", "public"."entities"."upvote_count", "public"."entities"."created_at", "public"."entities"."updated_at", "public"."entities"."legacy_id", "public"."entities"."submitter_id", "public"."entities"."affiliateStatus"::text, "public"."entities"."location_summary", "public"."entities"."meta_description", "public"."entities"."meta_title", "public"."entities"."ref_link", "public"."entities"."scraped_review_count", "public"."entities"."scraped_review_sentiment_label", "public"."entities"."scraped_review_sentiment_score", "public"."entities"."employee_count_range"::text, "public"."entities"."funding_stage"::text, "public"."entities"."slug" FROM "public"."entities" WHERE ("public"."entities"."slug" = $1 AND 1=1) LIMIT $2 OFFSET $3
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 4: Building createData object...
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 5: Starting Prisma transaction...
prisma:query COMMIT
prisma:query BEGIN
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 5a: Entered Prisma transaction successfully.
[Nest] 139  - 07/07/2025, 7:55:13 AM   DEBUG [EntitiesService] [Create] Step 5b: createData object: {
  "name": "Gadget",
  "slug": "gadget-1",
  "websiteUrl": "https://gadget.dev/",
  "shortDescription": "Gadget is an AI-powered full-stack development platform that automates backend infrastructure, API generation, and deployment, enabling developers to build complex web and AI apps rapidly with buil...",
  "description": "Gadget is a comprehensive developer platform designed to accelerate web and AI application development by automating traditionally time-consuming backend tasks such as CRUD API generation, database management, and hosting. It features a built-in Postgres database with vector support, serverless Node.js API actions, and seamless integrations with services like Shopify. Its AI assistant understands the entire app context—schema, APIs, frontend, and backend—providing smart code generation, workflow automation, and deployment assistance. Developers benefit from auto-scaled hosting, security pat...",
  "logoUrl": "https://cdn.prod.website-files.com/60673aeb6d3163e7fda75a76/66b4ff2f63744956e5dcbaec_logo-dark.svg",
  "foundedYear": 2020,
  "socialLinks": {
    "linkedin": "gadget-dev",
    "discord": "fpcmttyw2z"
  },
  "metaTitle": "Gadget | AI Navigator - AI-Powered Development Platform",
  "metaDescription": "Discover Gadget, the leading ai-powered development platform solution featuring Auto-generated, typesafe CRUD APIs for each data model, Built-in Postgres database with native vector support and spreadsheet-like record management. Transform your workflow with AI-powered automation.",
  "employeeCountRange": "C51_200",
  "fundingStage": "SERIES_A",
  "locationSummary": "Remote",
  "refLink": "https://gadget.dev/",
  "affiliateStatus": "NONE",
  "scrapedReviewSentimentLabel": "Positive",
  "scrapedReviewSentimentScore": 0.85,
  "scrapedReviewCount": 150,
  "entityType": {
    "connect": {
      "id": "fd181400-c9e6-431c-a8bd-c068d0491aba"
    }
  },
  "submitter": {
    "connect": {
      "id": "758268a4-a99c-4413-a83f-34e5f70c848c"
    }
  },
  "status": "PENDING",
  "entityDetailsTool": {
    "create": {
      "learningCurve": "MEDIUM",
      "keyFeatures": [
        "Auto-generated, typesafe CRUD APIs for each data model",
        "Built-in Postgres database with native vector support and spreadsheet-like record management",
        "AI assistant aware of app schema and logic for context-sensitive code generation and workflow automation",
        "Serverless API actions in Node.js with scheduling and background job support",
        "Seamless integrations with Shopify and third-party APIs, plus real-time frontend previews with React components",
        "Auto-scaled hosting with built-in security patching",
        "Real-time collaboration for developer teams",
        "Automated infrastructure setup on Google Cloud",
        "One-click OAuth and Stripe billing integration",
        "React frontend powered by Vite with real-time previews"
      ],
      "programmingLanguages": [
        "JavaScript",
        "Node.js"
      ],
      "frameworks": [
        "React"
      ],
      "targetAudience": [
        "Full-stack developers building AI and web applications",
        "Startups and SaaS companies needing rapid app deployment",
        "Shopify app developers leveraging AI features"
      ],
      "deploymentOptions": [
        "Cloud"
      ],
      "supportedOs": [
        "Windows",
        "macOS",
        "Linux",
        "Web"
      ],
      "mobileSupport": false,
      "apiAccess": true,
      "customizationLevel": "HIGH",
      "trialAvailable": true,
      "demoAvailable": true,
      "openSource": false,
      "supportChannels": [
        "Email",
        "Documentation"
      ],
      "technicalLevel": "BEGINNER",
      "hasApi": true,
      "hasFreeTier": true,
      "useCases": [
        "Rapid development and deployment of AI-powered chatbots using native vector search and OpenAI integration",
        "Building AI-enhanced Shopify apps with automated API versioning and infrastructure setup",
        "Creating scalable SaaS platforms with secure authentication, permissioning, and real-time collaboration features",
        "Developing custom backend workflows with scheduled serverless functions and webhook handlers for third-party service automation"
      ],
      "integrations": [
        "Shopify",
        "OpenAI",
        "Third-party APIs via webhooks"
      ],
      "pricingModel": "SUBSCRIPTION",
      "priceRange": "MEDIUM",
      "pricingDetails": "Offers a free trial with $50 of free OpenAI credits included; detailed subscription pricing not publicly disclosed yet.",
      "pricingUrl": "https://gadget.dev/pricing",
      "hasLiveChat": false,
      "communityUrl": "https://discord.com/invite/fpCMTtyW2Z"
    }
  },
  "entityCategories": {
    "create": [
      {
        "categoryId": "e52307e3-46d3-4516-a8d0-5ad7fde336b6",
        "assignedBy": "758268a4-a99c-4413-a83f-34e5f70c848c"
      }
    ]
  },
  "entityTags": {
    "create": [
      {
        "tagId": "954d898e-0860-41b0-89bf-073db78d4c19",
        "assignedBy": "758268a4-a99c-4413-a83f-34e5f70c848c"
      }
    ]
  },
  "entityFeatures": {
    "create": []
  }
}
[Nest] 139  - 07/07/2025, 7:55:13 AM     LOG [EntitiesService] [Create] Step 5c: Attempting to create core entity record...
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id" FROM "public"."users" WHERE ("public"."users"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query SELECT "public"."entity_types"."id" FROM "public"."entity_types" WHERE ("public"."entity_types"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query INSERT INTO "public"."entities" ("id","entity_type_id","name","short_description","description","logo_url","website_url","founded_year","social_links","status","avg_rating","review_count","upvote_count","created_at","updated_at","submitter_id","affiliateStatus","location_summary","meta_description","meta_title","ref_link","scraped_review_count","scraped_review_sentiment_label","scraped_review_sentiment_score","employee_count_range","funding_stage","slug") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,CAST($10::text AS "public"."EntityStatus"),$11,$12,$13,$14,$15,$16,CAST($17::text AS "public"."AffiliateStatus"),$18,$19,$20,$21,$22,$23,$24,CAST($25::text AS "public"."EmployeeCountRange"),CAST($26::text AS "public"."FundingStage"),$27) RETURNING "public"."entities"."id"
prisma:error 
Invalid `prisma.entity.create()` invocation:
Unique constraint failed on the fields: (`name`)
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"PrismaClientKnownRequestError"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"\nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"code\": \"P2002\",\n  \"meta\": {\n    \"modelName\": \"Entity\",\n    \"target\": [\n      \"name\"\n    ]\n  },\n  \"clientVersion\": \"6.10.1\",\n  \"name\": \"PrismaClientKnownRequestError\"\n}"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Unexpected exception caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"CorrelationId: 91b10f47-53cc-4fa8-9449-5398b3a251f8, ExceptionType: PrismaClientKnownRequestError, Message: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"Stack: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.149Z","level":"ERROR","message":"OriginalException: {\"code\":\"P2002\",\"meta\":{\"modelName\":\"Entity\",\"target\":[\"name\"]},\"clientVersion\":\"6.10.1\",\"name\":\"PrismaClientKnownRequestError\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:13.150Z","level":"INFO","message":"Request completed: POST /entities","correlationId":"91b10f47-53cc-4fa8-9449-5398b3a251f8","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"POST","url":"/entities","statusCode":500,"responseTime":129,"type":"response"}
prisma:query ROLLBACK
{"timestamp":"2025-07-07T07:55:14.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"bbc66026-560f-418f-9262-04a38c01eb85","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:14.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"bbc66026-560f-418f-9262-04a38c01eb85","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":1,"type":"response"}
--- INCOMING POST /entities REQUEST ---
Content-Type: application/json
Body type: undefined
Body: undefined
{"timestamp":"2025-07-07T07:55:18.337Z","level":"INFO","message":"Incoming request: POST /entities","correlationId":"c5ed2c84-147f-402b-9f34-ea3527da0175","ip":"::ffff:************","userAgent":"python-requests/2.32.3","method":"POST","url":"/entities","type":"request"}
--- SUPABASE AUTH GUARD ACTIVATED ---
Authorization header: Present
JWT Token extracted from header
[SupabaseAuthGuard] Supabase JWT validation successful for user: a7ed2b50-6c3d-4568-a35a-466504c037b2
[SupabaseAuthGuard] Attempting to find user in database...
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id", "public"."users"."auth_user_id", "public"."users"."username", "public"."users"."display_name", "public"."users"."email", "public"."users"."role"::text, "public"."users"."status"::text, "public"."users"."technical_level"::text, "public"."users"."profile_picture_url", "public"."users"."bio", "public"."users"."social_links", "public"."users"."created_at", "public"."users"."updated_at", "public"."users"."last_login", "public"."users"."bookmarks_count", "public"."users"."reputation_score", "public"."users"."requests_fulfilled", "public"."users"."requests_made", "public"."users"."reviews_count", "public"."users"."tools_approved", "public"."users"."tools_submitted" FROM "public"."users" WHERE ("public"."users"."auth_user_id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query COMMIT
[SupabaseAuthGuard] User profile found in DB for authUserId: a7ed2b50-6c3d-4568-a35a-466504c037b2, public.users ID: 758268a4-a99c-4413-a83f-34e5f70c848c
--- SUPABASE AUTH GUARD SUCCESS ---
{"timestamp":"2025-07-07T07:55:18.390Z","level":"INFO","message":"--- ENTITIES_CONTROLLER.CREATE METHOD ENTERED ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.390Z","level":"INFO","message":"User ID: 758268a4-a99c-4413-a83f-34e5f70c848c","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.390Z","level":"INFO","message":"DTO Type: object","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.390Z","level":"INFO","message":"DTO Constructor: CreateEntityDto","correlationId":"unknown"}
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 1: Entered create method for entity: "Gadget"
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 1a: Submitter user ID: 758268a4-a99c-4413-a83f-34e5f70c848c
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 1b: Entity type ID: fd181400-c9e6-431c-a8bd-c068d0491aba
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 2: Validating entity submission rate limits...
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id", "public"."users"."role"::text FROM "public"."users" WHERE ("public"."users"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 2: Rate limit validation passed.
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 3: Received entity_type_id: fd181400-c9e6-431c-a8bd-c068d0491aba
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 3a: Current entityTypeMap (size 19): [["5662e324-c5ba-4fb0-9738-d8ea39b63e1e","api"],["203628d4-e8e0-4e69-a22a-f4f1e00e76c5","model"],["8f7f4e42-0c10-4f11-963d-f4ce24e48e51","library"],["7bda8a8d-34d2-49c4-b514-8f0fedebe917","service"],["cdbea80d-d825-4844-bea2-5c7569f9ea12","test-rel-type-prisma"],["279a4f9b-9f10-4439-9bc9-a9aca497d2a0","agency"],["30a3464d-92dd-4bd2-afa3-925ddce3b986","software"],["4786b380-6381-419f-aff3-ebc47848ac7e","job"],["562bfe61-3cbb-4c1f-80f9-aadfd908009d","podcast"],["cca19303-7108-4a0e-a52d-3c259304e711","grant"],["fd181400-c9e6-431c-a8bd-c068d0491aba","ai-tool"],["a4bdf0a2-91ca-4e36-9734-41823de89323","course"],["0a117088-50cf-46d8-ac79-3e15ac7acfaf","dataset"],["90f0c9d5-bf89-4401-a5a7-c7b6005d396a","research-paper"],["7697eb38-36a9-46a9-9d17-c8a2f2da8dcd","platform"],["a5a0fbe1-38ea-4dc6-b02b-53260084ea55","hardware"],["de02661f-999e-4bc1-b5d9-d9005c6b5cfa","newsletter"],["d9815a04-0f04-4c67-9b7a-f629210fc48e","community"],["015f5cea-5b0f-495e-b4f2-07c70428daa4","event"]]
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 3b: Resolved entityTypeSlug: ai-tool
prisma:query COMMIT
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."entities"."id", "public"."entities"."entity_type_id", "public"."entities"."name", "public"."entities"."short_description", "public"."entities"."description", "public"."entities"."logo_url", "public"."entities"."website_url", "public"."entities"."documentation_url", "public"."entities"."contact_url", "public"."entities"."privacy_policy_url", "public"."entities"."founded_year", "public"."entities"."social_links", "public"."entities"."status"::text, "public"."entities"."avg_rating", "public"."entities"."review_count", "public"."entities"."upvote_count", "public"."entities"."created_at", "public"."entities"."updated_at", "public"."entities"."legacy_id", "public"."entities"."submitter_id", "public"."entities"."affiliateStatus"::text, "public"."entities"."location_summary", "public"."entities"."meta_description", "public"."entities"."meta_title", "public"."entities"."ref_link", "public"."entities"."scraped_review_count", "public"."entities"."scraped_review_sentiment_label", "public"."entities"."scraped_review_sentiment_score", "public"."entities"."employee_count_range"::text, "public"."entities"."funding_stage"::text, "public"."entities"."slug" FROM "public"."entities" WHERE ("public"."entities"."slug" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query COMMIT
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."entities"."id", "public"."entities"."entity_type_id", "public"."entities"."name", "public"."entities"."short_description", "public"."entities"."description", "public"."entities"."logo_url", "public"."entities"."website_url", "public"."entities"."documentation_url", "public"."entities"."contact_url", "public"."entities"."privacy_policy_url", "public"."entities"."founded_year", "public"."entities"."social_links", "public"."entities"."status"::text, "public"."entities"."avg_rating", "public"."entities"."review_count", "public"."entities"."upvote_count", "public"."entities"."created_at", "public"."entities"."updated_at", "public"."entities"."legacy_id", "public"."entities"."submitter_id", "public"."entities"."affiliateStatus"::text, "public"."entities"."location_summary", "public"."entities"."meta_description", "public"."entities"."meta_title", "public"."entities"."ref_link", "public"."entities"."scraped_review_count", "public"."entities"."scraped_review_sentiment_label", "public"."entities"."scraped_review_sentiment_score", "public"."entities"."employee_count_range"::text, "public"."entities"."funding_stage"::text, "public"."entities"."slug" FROM "public"."entities" WHERE ("public"."entities"."slug" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query COMMIT
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 4: Building createData object...
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 5: Starting Prisma transaction...
prisma:query BEGIN
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 5a: Entered Prisma transaction successfully.
[Nest] 139  - 07/07/2025, 7:55:18 AM   DEBUG [EntitiesService] [Create] Step 5b: createData object: {
  "name": "Gadget",
  "slug": "gadget-1",
  "websiteUrl": "https://gadget.dev/",
  "shortDescription": "Gadget is an AI-powered full-stack development platform that automates backend infrastructure, API generation, and deployment, enabling developers to build complex web and AI apps rapidly with buil...",
  "description": "Gadget is a comprehensive developer platform designed to accelerate web and AI application development by automating traditionally time-consuming backend tasks such as CRUD API generation, database management, and hosting. It features a built-in Postgres database with vector support, serverless Node.js API actions, and seamless integrations with services like Shopify. Its AI assistant understands the entire app context—schema, APIs, frontend, and backend—providing smart code generation, workflow automation, and deployment assistance. Developers benefit from auto-scaled hosting, security pat...",
  "logoUrl": "https://cdn.prod.website-files.com/60673aeb6d3163e7fda75a76/66b4ff2f63744956e5dcbaec_logo-dark.svg",
  "foundedYear": 2020,
  "socialLinks": {
    "linkedin": "gadget-dev",
    "discord": "fpcmttyw2z"
  },
  "metaTitle": "Gadget | AI Navigator - AI-Powered Development Platform",
  "metaDescription": "Discover Gadget, the leading ai-powered development platform solution featuring Auto-generated, typesafe CRUD APIs for each data model, Built-in Postgres database with native vector support and spreadsheet-like record management. Transform your workflow with AI-powered automation.",
  "employeeCountRange": "C51_200",
  "fundingStage": "SERIES_A",
  "locationSummary": "Remote",
  "refLink": "https://gadget.dev/",
  "affiliateStatus": "NONE",
  "scrapedReviewSentimentLabel": "Positive",
  "scrapedReviewSentimentScore": 0.85,
  "scrapedReviewCount": 150,
  "entityType": {
    "connect": {
      "id": "fd181400-c9e6-431c-a8bd-c068d0491aba"
    }
  },
  "submitter": {
    "connect": {
      "id": "758268a4-a99c-4413-a83f-34e5f70c848c"
    }
  },
  "status": "PENDING",
  "entityDetailsTool": {
    "create": {
      "learningCurve": "MEDIUM",
      "keyFeatures": [
        "Auto-generated, typesafe CRUD APIs for each data model",
        "Built-in Postgres database with native vector support and spreadsheet-like record management",
        "AI assistant aware of app schema and logic for context-sensitive code generation and workflow automation",
        "Serverless API actions in Node.js with scheduling and background job support",
        "Seamless integrations with Shopify and third-party APIs, plus real-time frontend previews with React components",
        "Auto-scaled hosting with built-in security patching",
        "Real-time collaboration for developer teams",
        "Automated infrastructure setup on Google Cloud",
        "One-click OAuth and Stripe billing integration",
        "React frontend powered by Vite with real-time previews"
      ],
      "programmingLanguages": [
        "JavaScript",
        "Node.js"
      ],
      "frameworks": [
        "React"
      ],
      "targetAudience": [
        "Full-stack developers building AI and web applications",
        "Startups and SaaS companies needing rapid app deployment",
        "Shopify app developers leveraging AI features"
      ],
      "deploymentOptions": [
        "Cloud"
      ],
      "supportedOs": [
        "Windows",
        "macOS",
        "Linux",
        "Web"
      ],
      "mobileSupport": false,
      "apiAccess": true,
      "customizationLevel": "HIGH",
      "trialAvailable": true,
      "demoAvailable": true,
      "openSource": false,
      "supportChannels": [
        "Email",
        "Documentation"
      ],
      "technicalLevel": "BEGINNER",
      "hasApi": true,
      "hasFreeTier": true,
      "useCases": [
        "Rapid development and deployment of AI-powered chatbots using native vector search and OpenAI integration",
        "Building AI-enhanced Shopify apps with automated API versioning and infrastructure setup",
        "Creating scalable SaaS platforms with secure authentication, permissioning, and real-time collaboration features",
        "Developing custom backend workflows with scheduled serverless functions and webhook handlers for third-party service automation"
      ],
      "integrations": [
        "Shopify",
        "OpenAI",
        "Third-party APIs via webhooks"
      ],
      "pricingModel": "SUBSCRIPTION",
      "priceRange": "MEDIUM",
      "pricingDetails": "Offers a free trial with $50 of free OpenAI credits included; detailed subscription pricing not publicly disclosed yet.",
      "pricingUrl": "https://gadget.dev/pricing",
      "hasLiveChat": false,
      "communityUrl": "https://discord.com/invite/fpCMTtyW2Z"
    }
  },
  "entityCategories": {
    "create": [
      {
        "categoryId": "e52307e3-46d3-4516-a8d0-5ad7fde336b6",
        "assignedBy": "758268a4-a99c-4413-a83f-34e5f70c848c"
      }
    ]
  },
  "entityTags": {
    "create": [
      {
        "tagId": "954d898e-0860-41b0-89bf-073db78d4c19",
        "assignedBy": "758268a4-a99c-4413-a83f-34e5f70c848c"
      }
    ]
  },
  "entityFeatures": {
    "create": []
  }
}
[Nest] 139  - 07/07/2025, 7:55:18 AM     LOG [EntitiesService] [Create] Step 5c: Attempting to create core entity record...
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."users"."id" FROM "public"."users" WHERE ("public"."users"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query SELECT "public"."entity_types"."id" FROM "public"."entity_types" WHERE ("public"."entity_types"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query INSERT INTO "public"."entities" ("id","entity_type_id","name","short_description","description","logo_url","website_url","founded_year","social_links","status","avg_rating","review_count","upvote_count","created_at","updated_at","submitter_id","affiliateStatus","location_summary","meta_description","meta_title","ref_link","scraped_review_count","scraped_review_sentiment_label","scraped_review_sentiment_score","employee_count_range","funding_stage","slug") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,CAST($10::text AS "public"."EntityStatus"),$11,$12,$13,$14,$15,$16,CAST($17::text AS "public"."AffiliateStatus"),$18,$19,$20,$21,$22,$23,$24,CAST($25::text AS "public"."EmployeeCountRange"),CAST($26::text AS "public"."FundingStage"),$27) RETURNING "public"."entities"."id"
prisma:error 
Invalid `prisma.entity.create()` invocation:
Unique constraint failed on the fields: (`name`)
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"PrismaClientKnownRequestError"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"\nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"code\": \"P2002\",\n  \"meta\": {\n    \"modelName\": \"Entity\",\n    \"target\": [\n      \"name\"\n    ]\n  },\n  \"clientVersion\": \"6.10.1\",\n  \"name\": \"PrismaClientKnownRequestError\"\n}"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Unexpected exception caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"CorrelationId: c5ed2c84-147f-402b-9f34-ea3527da0175, ExceptionType: PrismaClientKnownRequestError, Message: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"Stack: PrismaClientKnownRequestError: \nInvalid `prisma.entity.create()` invocation:\n\n\nUnique constraint failed on the fields: (`name`)\n    at Zn.handleRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:7459)\n    at Zn.handleAndLogRequestError (/opt/render/project/src/generated/prisma/runtime/library.js:124:6784)\n    at Zn.request (/opt/render/project/src/generated/prisma/runtime/library.js:124:6491)\n    at async l (/opt/render/project/src/generated/prisma/runtime/library.js:133:9778)\n    at async newEntity.prisma.$transaction.maxWait (/opt/render/project/src/dist/entities/entities.service.js:651:33)\n    at async Proxy._transactionWithCallback (/opt/render/project/src/generated/prisma/runtime/library.js:133:8139)\n    at async EntitiesService.create (/opt/render/project/src/dist/entities/entities.service.js:610:27)\n    at async EntitiesController.create (/opt/render/project/src/dist/entities/entities.controller.js:665:24)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"ERROR","message":"OriginalException: {\"code\":\"P2002\",\"meta\":{\"modelName\":\"Entity\",\"target\":[\"name\"]},\"clientVersion\":\"6.10.1\",\"name\":\"PrismaClientKnownRequestError\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.452Z","level":"INFO","message":"Request completed: POST /entities","correlationId":"c5ed2c84-147f-402b-9f34-ea3527da0175","ip":"::ffff:************","userAgent":"python-requests/2.32.3","method":"POST","url":"/entities","statusCode":500,"responseTime":115,"type":"response"}
prisma:query ROLLBACK
{"timestamp":"2025-07-07T07:55:18.648Z","level":"INFO","message":"Incoming request: GET /entities?search=Chronicle&limit=20","correlationId":"aa7a7afe-c3a8-45b1-9a35-240dea1b09af","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=20","type":"request"}
--- VALIDATION PIPE EXCEPTION FACTORY TRIGGERED ---
Number of validation errors: 1
Detailed Validation Errors: [
  {
    "target": {
      "page": 1,
      "limit": 20,
      "sortBy": "createdAt",
      "sortOrder": "desc",
      "search": "Chronicle"
    },
    "value": "Chronicle",
    "property": "search",
    "constraints": {
      "whitelistValidation": "property search should not exist"
    }
  }
]
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"BadRequestException"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"Bad Request Exception"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"response\": {\n    \"message\": [\n      {\n        \"target\": {\n          \"page\": 1,\n          \"limit\": 20,\n          \"sortBy\": \"createdAt\",\n          \"sortOrder\": \"desc\",\n          \"search\": \"Chronicle\"\n        },\n        \"value\": \"Chronicle\",\n        \"property\": \"search\",\n        \"constraints\": {\n          \"whitelistValidation\": \"property search should not exist\"\n        }\n      }\n    ],\n    \"error\": \"Bad Request\",\n    \"statusCode\": 400\n  },\n  \"status\": 400,\n  \"options\": {},\n  \"message\": \"Bad Request Exception\",\n  \"name\": \"BadRequestException\"\n}"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"BadRequestException: Bad Request Exception\n    at ValidationPipe.exceptionFactory (/opt/render/project/src/dist/main.js:46:20)\n    at ValidationPipe.transform (/opt/render/project/src/node_modules/@nestjs/common/pipes/validation.pipe.js:74:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async resolveParamValue (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:148:23)\n    at async Promise.all (index 0)\n    at async pipesFn (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:151:13)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:37:30\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: BadRequestException: Bad Request Exception","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"HttpException caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"CorrelationId: aa7a7afe-c3a8-45b1-9a35-240dea1b09af, StatusCode: 400, Message: An instance of ListEntitiesDto has failed the validation:\n - property search has failed the following constraints: whitelistValidation \n, ErrorType: BadRequestException","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"ErrorDetails: {\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"ERROR","message":"OriginalException: {\"response\":{\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400},\"status\":400,\"options\":{},\"message\":\"Bad Request Exception\",\"name\":\"BadRequestException\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.649Z","level":"INFO","message":"Request completed: GET /entities?search=Chronicle&limit=20","correlationId":"aa7a7afe-c3a8-45b1-9a35-240dea1b09af","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=20","statusCode":400,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:18.792Z","level":"INFO","message":"Incoming request: GET /entities?search=chronicle&limit=20","correlationId":"ce0b72e4-cadf-4c77-81c3-887f6b319338","ip":"::ffff:************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=chronicle&limit=20","type":"request"}
--- VALIDATION PIPE EXCEPTION FACTORY TRIGGERED ---
Number of validation errors: 1
Detailed Validation Errors: [
  {
    "target": {
      "page": 1,
      "limit": 20,
      "sortBy": "createdAt",
      "sortOrder": "desc",
      "search": "chronicle"
    },
    "value": "chronicle",
    "property": "search",
    "constraints": {
      "whitelistValidation": "property search should not exist"
    }
  }
]
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"BadRequestException"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"Bad Request Exception"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"response\": {\n    \"message\": [\n      {\n        \"target\": {\n          \"page\": 1,\n          \"limit\": 20,\n          \"sortBy\": \"createdAt\",\n          \"sortOrder\": \"desc\",\n          \"search\": \"chronicle\"\n        },\n        \"value\": \"chronicle\",\n        \"property\": \"search\",\n        \"constraints\": {\n          \"whitelistValidation\": \"property search should not exist\"\n        }\n      }\n    ],\n    \"error\": \"Bad Request\",\n    \"statusCode\": 400\n  },\n  \"status\": 400,\n  \"options\": {},\n  \"message\": \"Bad Request Exception\",\n  \"name\": \"BadRequestException\"\n}"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"BadRequestException: Bad Request Exception\n    at ValidationPipe.exceptionFactory (/opt/render/project/src/dist/main.js:46:20)\n    at ValidationPipe.transform (/opt/render/project/src/node_modules/@nestjs/common/pipes/validation.pipe.js:74:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async resolveParamValue (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:148:23)\n    at async Promise.all (index 0)\n    at async pipesFn (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:151:13)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:37:30\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: BadRequestException: Bad Request Exception","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"HttpException caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"CorrelationId: ce0b72e4-cadf-4c77-81c3-887f6b319338, StatusCode: 400, Message: An instance of ListEntitiesDto has failed the validation:\n - property search has failed the following constraints: whitelistValidation \n, ErrorType: BadRequestException","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"ErrorDetails: {\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"chronicle\"},\"value\":\"chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"ERROR","message":"OriginalException: {\"response\":{\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"chronicle\"},\"value\":\"chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400},\"status\":400,\"options\":{},\"message\":\"Bad Request Exception\",\"name\":\"BadRequestException\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.793Z","level":"INFO","message":"Request completed: GET /entities?search=chronicle&limit=20","correlationId":"ce0b72e4-cadf-4c77-81c3-887f6b319338","ip":"::ffff:************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=chronicle&limit=20","statusCode":400,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:18.929Z","level":"INFO","message":"Incoming request: GET /entities?search=Chronicle&limit=20","correlationId":"cfbcf008-b8bd-4120-a19a-efe7e86fb7b8","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=20","type":"request"}
--- VALIDATION PIPE EXCEPTION FACTORY TRIGGERED ---
Number of validation errors: 1
Detailed Validation Errors: [
  {
    "target": {
      "page": 1,
      "limit": 20,
      "sortBy": "createdAt",
      "sortOrder": "desc",
      "search": "Chronicle"
    },
    "value": "Chronicle",
    "property": "search",
    "constraints": {
      "whitelistValidation": "property search should not exist"
    }
  }
]
{"timestamp":"2025-07-07T07:55:18.929Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.929Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"BadRequestException"}
{"timestamp":"2025-07-07T07:55:18.929Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"Bad Request Exception"}
{"timestamp":"2025-07-07T07:55:18.929Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"response\": {\n    \"message\": [\n      {\n        \"target\": {\n          \"page\": 1,\n          \"limit\": 20,\n          \"sortBy\": \"createdAt\",\n          \"sortOrder\": \"desc\",\n          \"search\": \"Chronicle\"\n        },\n        \"value\": \"Chronicle\",\n        \"property\": \"search\",\n        \"constraints\": {\n          \"whitelistValidation\": \"property search should not exist\"\n        }\n      }\n    ],\n    \"error\": \"Bad Request\",\n    \"statusCode\": 400\n  },\n  \"status\": 400,\n  \"options\": {},\n  \"message\": \"Bad Request Exception\",\n  \"name\": \"BadRequestException\"\n}"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"BadRequestException: Bad Request Exception\n    at ValidationPipe.exceptionFactory (/opt/render/project/src/dist/main.js:46:20)\n    at ValidationPipe.transform (/opt/render/project/src/node_modules/@nestjs/common/pipes/validation.pipe.js:74:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async resolveParamValue (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:148:23)\n    at async Promise.all (index 0)\n    at async pipesFn (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:151:13)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:37:30\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: BadRequestException: Bad Request Exception","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"HttpException caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"CorrelationId: cfbcf008-b8bd-4120-a19a-efe7e86fb7b8, StatusCode: 400, Message: An instance of ListEntitiesDto has failed the validation:\n - property search has failed the following constraints: whitelistValidation \n, ErrorType: BadRequestException","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"ErrorDetails: {\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"ERROR","message":"OriginalException: {\"response\":{\"message\":[{\"target\":{\"page\":1,\"limit\":20,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400},\"status\":400,\"options\":{},\"message\":\"Bad Request Exception\",\"name\":\"BadRequestException\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:18.930Z","level":"INFO","message":"Request completed: GET /entities?search=Chronicle&limit=20","correlationId":"cfbcf008-b8bd-4120-a19a-efe7e86fb7b8","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=20","statusCode":400,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:19.077Z","level":"INFO","message":"Incoming request: GET /entities?search=Chronicle&limit=5","correlationId":"0757f251-7362-4b6d-ad16-5269bffee69d","ip":"::ffff:***********","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=5","type":"request"}
--- VALIDATION PIPE EXCEPTION FACTORY TRIGGERED ---
Number of validation errors: 1
Detailed Validation Errors: [
  {
    "target": {
      "page": 1,
      "limit": 5,
      "sortBy": "createdAt",
      "sortOrder": "desc",
      "search": "Chronicle"
    },
    "value": "Chronicle",
    "property": "search",
    "constraints": {
      "whitelistValidation": "property search should not exist"
    }
  }
]
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"BadRequestException"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"Bad Request Exception"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"response\": {\n    \"message\": [\n      {\n        \"target\": {\n          \"page\": 1,\n          \"limit\": 5,\n          \"sortBy\": \"createdAt\",\n          \"sortOrder\": \"desc\",\n          \"search\": \"Chronicle\"\n        },\n        \"value\": \"Chronicle\",\n        \"property\": \"search\",\n        \"constraints\": {\n          \"whitelistValidation\": \"property search should not exist\"\n        }\n      }\n    ],\n    \"error\": \"Bad Request\",\n    \"statusCode\": 400\n  },\n  \"status\": 400,\n  \"options\": {},\n  \"message\": \"Bad Request Exception\",\n  \"name\": \"BadRequestException\"\n}"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"BadRequestException: Bad Request Exception\n    at ValidationPipe.exceptionFactory (/opt/render/project/src/dist/main.js:46:20)\n    at ValidationPipe.transform (/opt/render/project/src/node_modules/@nestjs/common/pipes/validation.pipe.js:74:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async resolveParamValue (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:148:23)\n    at async Promise.all (index 0)\n    at async pipesFn (/opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:151:13)\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:37:30\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /opt/render/project/src/node_modules/@nestjs/core/router/router-proxy.js:9:17"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: BadRequestException: Bad Request Exception","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"HttpException caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"CorrelationId: 0757f251-7362-4b6d-ad16-5269bffee69d, StatusCode: 400, Message: An instance of ListEntitiesDto has failed the validation:\n - property search has failed the following constraints: whitelistValidation \n, ErrorType: BadRequestException","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"ErrorDetails: {\"message\":[{\"target\":{\"page\":1,\"limit\":5,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"ERROR","message":"OriginalException: {\"response\":{\"message\":[{\"target\":{\"page\":1,\"limit\":5,\"sortBy\":\"createdAt\",\"sortOrder\":\"desc\",\"search\":\"Chronicle\"},\"value\":\"Chronicle\",\"property\":\"search\",\"constraints\":{\"whitelistValidation\":\"property search should not exist\"}}],\"error\":\"Bad Request\",\"statusCode\":400},\"status\":400,\"options\":{},\"message\":\"Bad Request Exception\",\"name\":\"BadRequestException\"}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:19.078Z","level":"INFO","message":"Request completed: GET /entities?search=Chronicle&limit=5","correlationId":"0757f251-7362-4b6d-ad16-5269bffee69d","ip":"::ffff:***********","userAgent":"python-requests/2.32.3","method":"GET","url":"/entities?search=Chronicle&limit=5","statusCode":400,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:19.177Z","level":"INFO","message":"Incoming request: GET /entity-types","correlationId":"be4e2e51-aa26-4b90-90bd-4eaf4f210043","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entity-types","type":"request"}
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."entity_types"."id", "public"."entity_types"."name", "public"."entity_types"."description", "public"."entity_types"."slug", "public"."entity_types"."icon_url", "public"."entity_types"."created_at", "public"."entity_types"."updated_at" FROM "public"."entity_types" WHERE 1=1 ORDER BY "public"."entity_types"."name" ASC OFFSET $1
prisma:query COMMIT
{"timestamp":"2025-07-07T07:55:19.190Z","level":"INFO","message":"Request completed: GET /entity-types","correlationId":"be4e2e51-aa26-4b90-90bd-4eaf4f210043","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/entity-types","statusCode":200,"responseTime":13,"type":"response"}
{"timestamp":"2025-07-07T07:55:19.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"f0e2e4fd-8129-4fd6-8df7-deda4d816130","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:19.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"f0e2e4fd-8129-4fd6-8df7-deda4d816130","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:24.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"995b6f8f-50a4-418d-86ad-aa84ee677fdd","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:24.692Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"995b6f8f-50a4-418d-86ad-aa84ee677fdd","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":0,"type":"response"}
{"timestamp":"2025-07-07T07:55:29.693Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"0faea1e9-6ecc-4e5e-9f04-7add7417a625","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:29.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"0faea1e9-6ecc-4e5e-9f04-7add7417a625","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":0,"type":"response"}
{"timestamp":"2025-07-07T07:55:34.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"5203d048-c5b8-402d-8ea6-e56ae05529db","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:34.693Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"5203d048-c5b8-402d-8ea6-e56ae05529db","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:39.692Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"778708c4-58bb-4e40-8530-01161353d800","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:39.692Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"778708c4-58bb-4e40-8530-01161353d800","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":0,"type":"response"}
{"timestamp":"2025-07-07T07:55:39.693Z","level":"INFO","message":"Incoming request: GET /healthz","correlationId":"b40e3046-4631-4642-ba10-0857806e4557","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","type":"request"}
{"timestamp":"2025-07-07T07:55:39.694Z","level":"INFO","message":"Request completed: GET /healthz","correlationId":"b40e3046-4631-4642-ba10-0857806e4557","ip":"::ffff:*************","userAgent":"Render/1.0","method":"GET","url":"/healthz","statusCode":200,"responseTime":1,"type":"response"}
{"timestamp":"2025-07-07T07:55:39.879Z","level":"INFO","message":"Incoming request: GET /categories","correlationId":"c899e9d5-f68b-4da0-aacf-4bb108b53b96","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/categories","type":"request"}
prisma:query SELECT 1
prisma:query BEGIN
prisma:query DEALLOCATE ALL
prisma:query SELECT "public"."categories"."id", "public"."categories"."name", "public"."categories"."description", "public"."categories"."slug", "public"."categories"."icon_url", "public"."categories"."created_at", "public"."categories"."updated_at", "public"."categories"."parent_id" FROM "public"."categories" WHERE 1=1 ORDER BY "public"."categories"."name" ASC OFFSET $1
prisma:query COMMIT
{"timestamp":"2025-07-07T07:55:39.896Z","level":"INFO","message":"Request completed: GET /categories","correlationId":"c899e9d5-f68b-4da0-aacf-4bb108b53b96","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"GET","url":"/categories","statusCode":200,"responseTime":17,"type":"response"}
{"timestamp":"2025-07-07T07:55:39.953Z","level":"INFO","message":"Incoming request: POST /admin/categories","correlationId":"5518cac8-aaac-4ebf-aec1-0217c49dd133","ip":"::ffff:*************","userAgent":"python-requests/2.32.3","method":"POST","url":"/admin/categories","type":"request"}
--- JWT AUTH GUARD ACTIVATED ---
Authorization header: Present
--- JWT AUTH GUARD HANDLE REQUEST ---
Error: null
User: Missing
Info: JsonWebTokenError: invalid signature
    at /opt/render/project/src/node_modules/jsonwebtoken/verify.js:171:19
    at getSecret (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:97:14)
    at module.exports [as verify] (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:101:10)
    at module.exports [as JwtVerifier] (/opt/render/project/src/node_modules/passport-jwt/lib/verify_jwt.js:4:16)
    at /opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:104:25
    at JwtStrategy._secretOrKeyProvider (/opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:40:13)
    at JwtStrategy.authenticate (/opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:99:10)
    at attempt (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:378:16)
    at authenticate (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:379:7)
    at /opt/render/project/src/node_modules/@nestjs/passport/dist/auth.guard.js:88:3
JwtAuthGuard Error: null Info: JsonWebTokenError: invalid signature
    at /opt/render/project/src/node_modules/jsonwebtoken/verify.js:171:19
    at getSecret (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:97:14)
    at module.exports [as verify] (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:101:10)
    at module.exports [as JwtVerifier] (/opt/render/project/src/node_modules/passport-jwt/lib/verify_jwt.js:4:16)
    at /opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:104:25
    at JwtStrategy._secretOrKeyProvider (/opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:40:13)
    at JwtStrategy.authenticate (/opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:99:10)
    at attempt (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:378:16)
    at authenticate (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:379:7)
    at /opt/render/project/src/node_modules/@nestjs/passport/dist/auth.guard.js:88:3
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"--- GLOBAL EXCEPTION FILTER CAUGHT AN ERROR ---","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"Exception Type:","correlationId":"unknown","trace":"UnauthorizedException"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"Exception Message:","correlationId":"unknown","trace":"invalid signature"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"Exception Details:","correlationId":"unknown","trace":"{\n  \"response\": {\n    \"message\": \"invalid signature\",\n    \"error\": \"Unauthorized\",\n    \"statusCode\": 401\n  },\n  \"status\": 401,\n  \"options\": {},\n  \"message\": \"invalid signature\",\n  \"name\": \"UnauthorizedException\"\n}"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"Exception Stack:","correlationId":"unknown","trace":"UnauthorizedException: invalid signature\n    at AdminGuard.handleRequest (/opt/render/project/src/dist/auth/guards/jwt-auth.guard.js:27:26)\n    at /opt/render/project/src/node_modules/@nestjs/passport/dist/auth.guard.js:44:124\n    at /opt/render/project/src/node_modules/@nestjs/passport/dist/auth.guard.js:83:24\n    at allFailed (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:110:18)\n    at attempt (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:183:28)\n    at strategy.fail (/opt/render/project/src/node_modules/passport/lib/middleware/authenticate.js:314:9)\n    at /opt/render/project/src/node_modules/passport-jwt/lib/strategy.js:106:33\n    at /opt/render/project/src/node_modules/jsonwebtoken/verify.js:171:14\n    at getSecret (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:97:14)\n    at module.exports [as verify] (/opt/render/project/src/node_modules/jsonwebtoken/verify.js:101:10)"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"[GlobalExceptionFilter] Full exception object: UnauthorizedException: invalid signature","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"HttpException caught:","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"CorrelationId: 5518cac8-aaac-4ebf-aec1-0217c49dd133, StatusCode: 401, Message: invalid signature, ErrorType: UnauthorizedException","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"ErrorDetails: {\"message\":\"invalid signature\",\"error\":\"Unauthorized\",\"statusCode\":401}","correlationId":"unknown"}
{"timestamp":"2025-07-07T07:55:39.954Z","level":"ERROR","message":"OriginalException: {\"response\":{\"message\":\"invalid signature\",\"error\":\"Unauthorized\",\"statusCode\":401},\"status\":401,\"options\":{},\"message\":\"invalid signature\",\"name\":\"UnauthorizedException\"}","correlationId":"unknown"}