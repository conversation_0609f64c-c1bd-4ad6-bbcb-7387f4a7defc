#!/bin/bash

echo "🔄 Restarting Frontend with Local Backend Configuration"
echo "======================================================"

# Kill any existing frontend processes
echo "🛑 Stopping existing frontend processes..."
pkill -f "react-scripts"
sleep 2

# Check if backend is running
echo "🔍 Checking backend status..."
if curl -s http://localhost:8001/api/health > /dev/null 2>&1; then
    echo "✅ Backend is running on http://localhost:8001"
else
    echo "❌ Backend is not running! Please start it first:"
    echo "   cd backend && python3 working_server.py"
    exit 1
fi

# Start frontend
echo "🌐 Starting frontend..."
cd frontend

# Export the environment variable to make sure it's set
export REACT_APP_BACKEND_URL=http://localhost:8001

# Start the frontend
if command -v npm > /dev/null 2>&1; then
    npm start
elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
    /Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm start
else
    echo "❌ npm not found. Please start frontend manually:"
    echo "   cd frontend"
    echo "   export REACT_APP_BACKEND_URL=http://localhost:8001"
    echo "   npm start"
    exit 1
fi
