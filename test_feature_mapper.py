"""
Test script for Enhanced Feature Mapper
Tests feature mapping accuracy with sample tools
"""

import sys
sys.path.append('/app')

from enhanced_feature_mapper import EnhancedFeatureMapper
from ai_navigator_client import AINavigatorClient
import json

def test_feature_mapper():
    """Test the enhanced feature mapper with sample tools"""
    
    print("🎯 TESTING ENHANCED FEATURE MAPPER")
    print("=" * 50)
    
    # Initialize services
    client = AINavigatorClient()
    mapper = EnhancedFeatureMapper(client, '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')
    
    # Test cases with expected features
    test_cases = [
        {
            "tool_name": "Canva",
            "description": "Drag-and-drop design tool for creating graphics, presentations, and social media posts",
            "capabilities": ["drag-and-drop interface", "templates", "design tools", "collaboration"],
            "use_cases": ["social media graphics", "presentations", "marketing materials"],
            "expected_features": ["drag and drop", "template library", "real-time collaboration"]
        },
        {
            "tool_name": "Zapier",
            "description": "Automation platform that connects apps and services through workflows",
            "capabilities": ["API integrations", "workflow automation", "webhook support", "app connections"],
            "use_cases": ["business automation", "data synchronization", "workflow optimization"],
            "expected_features": ["api integration", "workflow automation", "notifications"]
        },
        {
            "tool_name": "ChatGPT",
            "description": "AI-powered conversational assistant for text generation and analysis",
            "capabilities": ["natural language processing", "text generation", "conversation", "AI responses"],
            "use_cases": ["content creation", "question answering", "writing assistance"],
            "expected_features": ["natural language processing", "text generation"]
        },
        {
            "tool_name": "Tableau",
            "description": "Data visualization and business intelligence platform",
            "capabilities": ["data visualization", "dashboard creation", "analytics", "reporting"],
            "use_cases": ["business intelligence", "data analysis", "reporting"],
            "expected_features": ["data visualization", "analytics", "dashboard"]
        }
    ]
    
    total_expected = 0
    total_correct = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['tool_name']}")
        print(f"   Expected features: {test_case['expected_features']}")
        
        # Map features
        mapped_features = mapper.map_features_with_ai(
            test_case['capabilities'],
            test_case['use_cases'],
            test_case['description'],
            test_case['tool_name']
        )
        
        print(f"   Mapped {len(mapped_features)} features:")
        predicted_features = []
        for feature in mapped_features:
            print(f"     - {feature['feature_name']} (confidence: {feature['confidence']:.2f})")
            predicted_features.append(feature['feature_name'])
        
        # Calculate accuracy for this test case
        expected_set = set(test_case['expected_features'])
        predicted_set = set(predicted_features)
        
        intersection = expected_set.intersection(predicted_set)
        
        if expected_set:
            case_accuracy = len(intersection) / len(expected_set) * 100
            print(f"   Accuracy: {case_accuracy:.1f}% ({len(intersection)}/{len(expected_set)} correct)")
            
            total_expected += len(expected_set)
            total_correct += len(intersection)
        else:
            print("   No expected features to compare")
    
    # Calculate overall accuracy
    overall_accuracy = (total_correct / total_expected * 100) if total_expected > 0 else 0
    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Total correct: {total_correct}/{total_expected}")
    print(f"   Overall accuracy: {overall_accuracy:.1f}%")
    
    if overall_accuracy >= 90:
        print("   🎉 TARGET ACCURACY ACHIEVED (90%+)")
    else:
        print("   ⚠️  Below target accuracy - needs improvement")
    
    return overall_accuracy >= 90

def test_rule_based_mapping():
    """Test rule-based feature mapping specifically"""
    
    print("\n🔧 TESTING RULE-BASED MAPPING")
    print("=" * 50)
    
    # Initialize services
    client = AINavigatorClient()
    mapper = EnhancedFeatureMapper(client, '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')
    
    # Test rule-based mapping
    capabilities = ["drag-and-drop interface", "API integration", "machine learning", "data visualization"]
    use_cases = ["workflow automation", "business intelligence"]
    description = "AI-powered tool with drag-and-drop interface for creating automated workflows"
    
    rule_based_results = mapper._rule_based_feature_mapping(capabilities, use_cases, description)
    
    print(f"Rule-based mapping found {len(rule_based_results)} features:")
    for feature in rule_based_results:
        print(f"  - {feature['feature_name']} (confidence: {feature['confidence']:.2f})")
    
    return len(rule_based_results) > 0

if __name__ == "__main__":
    # Test rule-based mapping first
    rule_based_success = test_rule_based_mapping()
    
    # Test full feature mapping
    full_mapping_success = test_feature_mapper()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Rule-based mapping: {'✅ PASS' if rule_based_success else '❌ FAIL'}")
    print(f"   Full feature mapping: {'✅ PASS' if full_mapping_success else '❌ FAIL'}")
    
    if rule_based_success and full_mapping_success:
        print("   🎉 ALL TESTS PASSED!")
    else:
        print("   ⚠️  Some tests failed - needs improvement")
