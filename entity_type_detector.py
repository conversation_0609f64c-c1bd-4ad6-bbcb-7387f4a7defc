"""
Entity Type Detector - Intelligent classification of entities by type
"""

import re
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse

class EntityTypeDetector:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Define detection patterns for each entity type
        self.detection_patterns = {
            'research-paper': {
                'url_patterns': [
                    r'arxiv\.org',
                    r'ieee\.org', 
                    r'acm\.org',
                    r'springer\.com',
                    r'nature\.com',
                    r'science\.org',
                    r'papers\.nips\.cc',
                    r'openreview\.net',
                    r'proceedings\.mlr\.press',
                    r'aclanthology\.org'
                ],
                'name_patterns': [
                    r'\b(paper|research|study|analysis)\b',
                    r'\b(arxiv|doi|conference|journal)\b',
                    r'\b(proceedings|publication)\b'
                ],
                'description_patterns': [
                    r'\b(abstract|methodology|findings|results)\b',
                    r'\b(authors?|researchers?|scientists?)\b',
                    r'\b(published|peer.?reviewed|citation)\b'
                ]
            },
            'job': {
                'url_patterns': [
                    r'jobs\.',
                    r'careers\.',
                    r'hiring\.',
                    r'greenhouse\.io',
                    r'lever\.co',
                    r'workday\.com',
                    r'indeed\.com',
                    r'linkedin\.com/jobs',
                    r'glassdoor\.com',
                    r'angel\.co/jobs'
                ],
                'name_patterns': [
                    r'\b(job|position|role|hiring)\b',
                    r'\b(engineer|developer|scientist|analyst)\b',
                    r'\b(remote|full.?time|part.?time)\b'
                ],
                'description_patterns': [
                    r'\b(salary|compensation|benefits)\b',
                    r'\b(experience|years|skills|requirements)\b',
                    r'\b(apply|application|resume|cv)\b'
                ]
            },
            'event': {
                'url_patterns': [
                    r'eventbrite\.com',
                    r'meetup\.com',
                    r'conference\.org',
                    r'summit\.com',
                    r'events?\.',
                    r'workshop\.',
                    r'webinar\.'
                ],
                'name_patterns': [
                    r'\b(conference|summit|meetup|workshop)\b',
                    r'\b(event|seminar|webinar|symposium)\b',
                    r'\b(expo|convention|gathering)\b',
                    r'\b20(2[4-9]|3[0-9])\b'  # Years 2024-2039
                ],
                'description_patterns': [
                    r'\b(speakers?|agenda|schedule|registration)\b',
                    r'\b(venue|location|date|time)\b',
                    r'\b(attendees?|networking|tickets?)\b'
                ]
            },
            'hardware': {
                'url_patterns': [
                    r'nvidia\.com',
                    r'amd\.com',
                    r'intel\.com',
                    r'apple\.com',
                    r'qualcomm\.com',
                    r'broadcom\.com'
                ],
                'name_patterns': [
                    r'\b(gpu|cpu|chip|processor)\b',
                    r'\b(rtx|gtx|radeon|geforce)\b',
                    r'\b(hardware|device|component)\b',
                    r'\b(server|workstation|laptop)\b'
                ],
                'description_patterns': [
                    r'\b(memory|ram|storage|cores?)\b',
                    r'\b(performance|benchmark|speed)\b',
                    r'\b(watts?|power|consumption|tgp)\b',
                    r'\b(manufacturing|silicon|architecture)\b'
                ]
            },
            'agency': {
                'url_patterns': [
                    r'agency\.',
                    r'consulting\.',
                    r'services\.',
                    r'studio\.',
                    r'partners\.'
                ],
                'name_patterns': [
                    r'\b(agency|consulting|services)\b',
                    r'\b(studio|partners|group)\b',
                    r'\b(solutions|creative|digital)\b'
                ],
                'description_patterns': [
                    r'\b(clients?|projects?|portfolio)\b',
                    r'\b(strategy|design|development)\b',
                    r'\b(team|experts?|specialists?)\b'
                ]
            },
            'course': {
                'url_patterns': [
                    r'coursera\.org',
                    r'udemy\.com',
                    r'edx\.org',
                    r'khan.?academy\.org',
                    r'pluralsight\.com',
                    r'udacity\.com',
                    r'course\.',
                    r'learn\.',
                    r'education\.'
                ],
                'name_patterns': [
                    r'\b(course|class|training|tutorial)\b',
                    r'\b(learning|education|certification)\b',
                    r'\b(bootcamp|academy|school)\b'
                ],
                'description_patterns': [
                    r'\b(students?|lessons?|modules?)\b',
                    r'\b(instructor|teacher|professor)\b',
                    r'\b(certificate|diploma|credential)\b'
                ]
            },
            'api': {
                'url_patterns': [
                    r'api\.',
                    r'/api/',
                    r'developers?\.',
                    r'docs\.',
                    r'swagger\.',
                    r'postman\.'
                ],
                'name_patterns': [
                    r'\bapi\b',
                    r'\b(rest|restful|graphql)\b',
                    r'\b(endpoint|integration|sdk)\b'
                ],
                'description_patterns': [
                    r'\b(endpoint|request|response)\b',
                    r'\b(authentication|oauth|token)\b',
                    r'\b(json|xml|http|https)\b'
                ]
            },
            'dataset': {
                'url_patterns': [
                    r'kaggle\.com',
                    r'huggingface\.co/datasets',
                    r'data\.',
                    r'dataset\.',
                    r'github\.com.*dataset'
                ],
                'name_patterns': [
                    r'\b(dataset|data|corpus)\b',
                    r'\b(collection|repository|database)\b',
                    r'\b(samples?|records?|entries?)\b'
                ],
                'description_patterns': [
                    r'\b(rows?|columns?|features?)\b',
                    r'\b(csv|json|parquet|sql)\b',
                    r'\b(training|testing|validation)\b'
                ]
            }
        }
        
        # Entity type mapping to schema entity types
        self.entity_type_mapping = {
            'research-paper': 'research-paper',
            'job': 'job', 
            'event': 'event',
            'hardware': 'hardware',
            'agency': 'agency',
            'course': 'course',
            'api': 'api',
            'dataset': 'dataset',
            'ai-tool': 'ai-tool'  # Default fallback
        }

    def detect_entity_type(self, tool_name: str, website_url: str, description: str = "") -> str:
        """
        Intelligently detect entity type based on multiple signals
        
        Args:
            tool_name: Name of the tool/entity
            website_url: URL of the entity
            description: Description or other text content
            
        Returns:
            Entity type slug (e.g., 'research-paper', 'ai-tool', etc.)
        """
        
        # Normalize inputs
        name_lower = tool_name.lower() if tool_name else ""
        url_lower = website_url.lower() if website_url else ""
        desc_lower = description.lower() if description else ""
        
        # Score each entity type
        type_scores = {}
        
        for entity_type, patterns in self.detection_patterns.items():
            score = 0
            
            # URL pattern matching (highest weight)
            for pattern in patterns['url_patterns']:
                if re.search(pattern, url_lower, re.IGNORECASE):
                    score += 3
                    
            # Name pattern matching (medium weight)  
            for pattern in patterns['name_patterns']:
                if re.search(pattern, name_lower, re.IGNORECASE):
                    score += 2
                    
            # Description pattern matching (lower weight)
            for pattern in patterns['description_patterns']:
                if re.search(pattern, desc_lower, re.IGNORECASE):
                    score += 1
                    
            if score > 0:
                type_scores[entity_type] = score
        
        # Determine best match
        if type_scores:
            detected_type = max(type_scores, key=type_scores.get)
            confidence = type_scores[detected_type]
            
            self.logger.info(f"Detected entity type '{detected_type}' for '{tool_name}' with confidence {confidence}")
            
            # Require minimum confidence for non-default types
            if confidence >= 2 or detected_type == 'ai-tool':
                return self.entity_type_mapping[detected_type]
        
        # Default to AI tool
        self.logger.info(f"Defaulting to 'ai-tool' for '{tool_name}'")
        return 'ai-tool'

    def get_entity_type_id(self, entity_type_slug: str, client) -> Optional[str]:
        """Get entity type ID from the API based on slug"""
        try:
            entity_types = client.get_entity_types()
            for entity_type in entity_types:
                if entity_type.get('slug') == entity_type_slug:
                    return entity_type['id']
                    
            self.logger.error(f"Entity type '{entity_type_slug}' not found in API")
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting entity type ID for '{entity_type_slug}': {e}")
            return None

    def validate_entity_type_detection(self, tool_name: str, website_url: str, detected_type: str) -> Dict[str, Any]:
        """Validate and provide details about entity type detection"""
        
        validation_result = {
            'detected_type': detected_type,
            'confidence': 'medium',
            'reasoning': [],
            'alternative_types': []
        }
        
        # Add reasoning based on detection
        name_lower = tool_name.lower() if tool_name else ""
        url_lower = website_url.lower() if website_url else ""
        
        if detected_type in self.detection_patterns:
            patterns = self.detection_patterns[detected_type]
            
            # Check URL matches
            for pattern in patterns['url_patterns']:
                if re.search(pattern, url_lower, re.IGNORECASE):
                    validation_result['reasoning'].append(f"URL matches {detected_type} pattern: {pattern}")
                    validation_result['confidence'] = 'high'
                    
            # Check name matches  
            for pattern in patterns['name_patterns']:
                if re.search(pattern, name_lower, re.IGNORECASE):
                    validation_result['reasoning'].append(f"Name matches {detected_type} pattern: {pattern}")
        
        return validation_result