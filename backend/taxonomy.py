"""
Enhanced Taxonomy Service with Smart Mapping
Implements synonym-aware fuzzy matching to reduce duplicate taxonomy items
"""

import logging
import re
from difflib import SequenceMatcher
from typing import Dict, List, Optional, Set
import json
from datetime import datetime

class EnhancedTaxonomyService:
    def __init__(self, ai_client):
        self.ai_client = ai_client
        self.logger = logging.getLogger(__name__)
        
        # Load current taxonomy
        self.categories_map = {}
        self.tags_map = {}
        self.features_map = {}
        
        # Synonym dictionaries for smart matching
        self.category_synonyms = {
            'ai': ['artificial intelligence', 'ai tools', 'ai-powered', 'machine learning', 'deep learning'],
            'analytics': ['data analytics', 'analytics tools', 'business analytics', 'data analysis'],
            'automation': ['workflow automation', 'process automation', 'automated', 'auto'],
            'content creation': ['content generation', 'content tools', 'creative tools', 'media creation'],
            'development': ['software development', 'dev tools', 'developer tools', 'coding'],
            'productivity': ['business productivity', 'workflow', 'efficiency', 'productive'],
            'marketing': ['digital marketing', 'marketing automation', 'growth', 'advertising'],
            'communication': ['messaging', 'chat', 'collaboration', 'team communication'],
            'design': ['graphic design', 'ui design', 'creative design', 'visual design'],
            'education': ['learning', 'training', 'educational', 'e-learning'],
            'finance': ['financial', 'fintech', 'accounting', 'payments'],
            'healthcare': ['medical', 'health tech', 'clinical', 'wellness'],
        }
        
        self.tag_synonyms = {
            'api': ['api access', 'api available', 'rest api', 'api integration'],
            'cloud': ['cloud-based', 'saas', 'web-based', 'online'],
            'free': ['free tier', 'freemium', 'open source', 'no cost'],
            'ai': ['artificial intelligence', 'machine learning', 'smart', 'intelligent'],
            'beginner': ['beginner-friendly', 'easy to use', 'user-friendly', 'simple'],
            'advanced': ['professional', 'enterprise', 'complex', 'sophisticated'],
            'mobile': ['mobile app', 'ios', 'android', 'smartphone'],
            'integration': ['integrations', 'connects with', 'compatible with'],
            'real-time': ['live', 'instant', 'real time', 'immediate'],
            'customizable': ['custom', 'configurable', 'flexible', 'personalized'],
        }
        
        self.feature_synonyms = {
            'api access': ['api integration', 'rest api', 'api endpoint', 'developer api'],
            'data analysis': ['analytics', 'data analytics', 'reporting', 'insights'],
            'automation': ['automated workflows', 'auto-pilot', 'smart automation'],
            'collaboration': ['team collaboration', 'collaborative editing', 'shared workspace'],
            'integration': ['third-party integration', 'app integration', 'system integration'],
            'customization': ['custom workflows', 'personalization', 'configuration'],
            'security': ['data security', 'encryption', 'secure', 'privacy'],
            'scalability': ['scalable', 'enterprise-grade', 'high-performance'],
            'monitoring': ['tracking', 'analytics dashboard', 'performance monitoring'],
            'support': ['customer support', 'help desk', 'documentation', 'community'],
        }
        
        self.load_taxonomy()
        
    def load_taxonomy(self):
        """Load current taxonomy from API"""
        try:
            categories = self.ai_client.get_categories()
            for category in categories:
                name = category.get('name', '').lower()
                self.categories_map[name] = category
            self.logger.info(f"Loaded {len(self.categories_map)} categories")
            
            tags = self.ai_client.get_tags()
            for tag in tags:
                name = tag.get('name', '').lower()
                self.tags_map[name] = tag
            self.logger.info(f"Loaded {len(self.tags_map)} tags")
            
            features = self.ai_client.get_features()
            for feature in features:
                name = feature.get('name', '').lower()
                self.features_map[name] = feature
            self.logger.info(f"Loaded {len(self.features_map)} features")
            
        except Exception as e:
            self.logger.error(f"Error loading taxonomy: {str(e)}")
    
    def normalize_text(self, text: str) -> str:
        """Normalize text for comparison"""
        if not text:
            return ""
        # Convert to lowercase, remove special chars, normalize spaces
        text = re.sub(r'[^\w\s-]', '', text.lower())
        text = re.sub(r'\s+', ' ', text).strip()
        text = re.sub(r'-+', '-', text)
        return text
    
    def similarity_score(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        text1_norm = self.normalize_text(text1)
        text2_norm = self.normalize_text(text2)
        
        # Exact match
        if text1_norm == text2_norm:
            return 1.0
        
        # Check if one contains the other
        if text1_norm in text2_norm or text2_norm in text1_norm:
            return 0.9
        
        # Sequence matcher
        return SequenceMatcher(None, text1_norm, text2_norm).ratio()
    
    def find_synonym_match(self, text: str, synonym_dict: Dict[str, List[str]]) -> Optional[str]:
        """Find matching key in synonym dictionary"""
        text_norm = self.normalize_text(text)
        
        for key, synonyms in synonym_dict.items():
            # Check exact match with key
            if self.similarity_score(text, key) >= 0.85:
                return key
            
            # Check synonyms
            for synonym in synonyms:
                if self.similarity_score(text, synonym) >= 0.85:
                    return key
        
        return None
    
    def map_categories(self, categories: List[str]) -> List[str]:
        """Map categories to existing ones using enhanced fuzzy matching"""
        mapped_ids = []
        
        for category in categories:
            if not category:
                continue
                
            # Try exact match first
            category_lower = category.lower()
            if category_lower in self.categories_map:
                mapped_ids.append(self.categories_map[category_lower]['id'])
                continue
            
            # Try synonym matching
            synonym_match = self.find_synonym_match(category, self.category_synonyms)
            if synonym_match and synonym_match in self.categories_map:
                mapped_ids.append(self.categories_map[synonym_match]['id'])
                self.logger.info(f"Mapped category '{category}' -> '{synonym_match}' via synonym")
                continue
            
            # Try fuzzy matching with higher threshold
            best_match = None
            best_score = 0.0
            
            for existing_category, cat_data in self.categories_map.items():
                score = self.similarity_score(category, existing_category)
                if score > best_score and score >= 0.75:  # Increased threshold
                    best_score = score
                    best_match = cat_data
            
            if best_match:
                mapped_ids.append(best_match['id'])
                self.logger.info(f"Mapped category '{category}' -> '{best_match['name']}' (score: {best_score:.2f})")
            else:
                self.logger.warning(f"Could not map category: '{category}' - Consider adding to taxonomy")
                self._log_missing_taxonomy('category', category)
        
        return mapped_ids
    
    def map_tags(self, tags: List[str]) -> List[str]:
        """Map tags to existing ones using enhanced fuzzy matching"""
        mapped_ids = []
        
        for tag in tags:
            if not tag:
                continue
                
            # Try exact match first
            tag_lower = tag.lower()
            if tag_lower in self.tags_map:
                mapped_ids.append(self.tags_map[tag_lower]['id'])
                continue
            
            # Try synonym matching
            synonym_match = self.find_synonym_match(tag, self.tag_synonyms)
            if synonym_match and synonym_match in self.tags_map:
                mapped_ids.append(self.tags_map[synonym_match]['id'])
                self.logger.info(f"Mapped tag '{tag}' -> '{synonym_match}' via synonym")
                continue
            
            # Try fuzzy matching
            best_match = None
            best_score = 0.0
            
            for existing_tag, tag_data in self.tags_map.items():
                score = self.similarity_score(tag, existing_tag)
                if score > best_score and score >= 0.75:
                    best_score = score
                    best_match = tag_data
            
            if best_match:
                mapped_ids.append(best_match['id'])
                self.logger.info(f"Mapped tag '{tag}' -> '{best_match['name']}' (score: {best_score:.2f})")
            else:
                self.logger.warning(f"Could not map tag: '{tag}' - Consider adding to taxonomy")
                self._log_missing_taxonomy('tag', tag)
        
        return mapped_ids
    
    def map_features(self, features: List[str]) -> List[str]:
        """Map features to existing ones using enhanced fuzzy matching"""
        mapped_ids = []
        
        for feature in features:
            if not feature:
                continue
                
            # Try exact match first
            feature_lower = feature.lower()
            if feature_lower in self.features_map:
                mapped_ids.append(self.features_map[feature_lower]['id'])
                continue
            
            # Try synonym matching
            synonym_match = self.find_synonym_match(feature, self.feature_synonyms)
            if synonym_match and synonym_match in self.features_map:
                mapped_ids.append(self.features_map[synonym_match]['id'])
                self.logger.info(f"Mapped feature '{feature}' -> '{synonym_match}' via synonym")
                continue
            
            # Try fuzzy matching
            best_match = None
            best_score = 0.0
            
            for existing_feature, feat_data in self.features_map.items():
                score = self.similarity_score(feature, existing_feature)
                if score > best_score and score >= 0.75:
                    best_score = score
                    best_match = feat_data
            
            if best_match:
                mapped_ids.append(best_match['id'])
                self.logger.info(f"Mapped feature '{feature}' -> '{best_match['name']}' (score: {best_score:.2f})")
            else:
                self.logger.warning(f"Could not map feature: '{feature}' - Consider adding to taxonomy")
                self._log_missing_taxonomy('feature', feature)
        
        return mapped_ids

    def map_features_with_ai(self, tool_capabilities: List[str], use_cases: List[str],
                           description: str, tool_name: str = "") -> List[str]:
        """
        Enhanced feature mapping using AI-powered analysis
        For now, falls back to traditional mapping since AI feature mapper is not integrated

        Args:
            tool_capabilities: List of tool capabilities/features
            use_cases: List of use cases
            description: Tool description
            tool_name: Name of the tool

        Returns:
            List of feature UUIDs
        """
        self.logger.info(f"AI-powered feature mapping for: {tool_name} (fallback to traditional mapping)")

        # For now, use traditional feature mapping
        # TODO: Integrate with enhanced_feature_mapper.py for true AI-powered mapping
        all_features = tool_capabilities + use_cases
        return self.map_features(all_features)

    def _log_missing_taxonomy(self, taxonomy_type: str, item_name: str):
        """Log missing taxonomy items for review"""
        missing_item = {
            'type': taxonomy_type,
            'name': item_name,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'suggestion': self._get_suggestion(taxonomy_type, item_name)
        }
        
        try:
            # Use local path instead of /app/ for development
            import os
            log_file = os.path.join(os.getcwd(), 'missing_taxonomy.log')

            with open(log_file, 'a') as f:
                f.write(json.dumps(missing_item) + '\n')
        except Exception as e:
            self.logger.error(f"Error logging missing taxonomy: {str(e)}")
    
    def _get_suggestion(self, taxonomy_type: str, item_name: str) -> str:
        """Get suggestion for unmapped taxonomy item"""
        synonym_dict = {
            'category': self.category_synonyms,
            'tag': self.tag_synonyms,
            'feature': self.feature_synonyms
        }.get(taxonomy_type, {})
        
        # Find potential matches
        suggestions = []
        for key, synonyms in synonym_dict.items():
            if self.similarity_score(item_name, key) >= 0.5:
                suggestions.append(key)
        
        if suggestions:
            return f"Consider mapping to: {', '.join(suggestions[:3])}"
        else:
            return "No close matches found - may need new taxonomy item"
    
    def get_missing_items(self) -> List[Dict]:
        """Get all missing taxonomy items from log"""
        missing_items = []
        try:
            # Use local path instead of /app/ for development
            import os
            log_file = os.path.join(os.getcwd(), 'missing_taxonomy.log')

            with open(log_file, 'r') as f:
                for line in f:
                    try:
                        item = json.loads(line.strip())
                        missing_items.append(item)
                    except json.JSONDecodeError:
                        continue
        except FileNotFoundError:
            pass
        except Exception as e:
            self.logger.error(f"Error reading missing taxonomy log: {str(e)}")

        return missing_items
    
    def get_taxonomy_stats(self) -> Dict:
        """Get taxonomy statistics"""
        return {
            'categories': len(self.categories_map),
            'tags': len(self.tags_map),
            'features': len(self.features_map),
            'missing_items': len(self.get_missing_items())
        }