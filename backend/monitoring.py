"""
Performance Monitoring and Optimization System
Comprehensive performance metrics, alerting, and optimization recommendations
for the AI Navigator enhancement pipeline.
"""

import time
import threading
import logging
import statistics
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import json
import psutil
import gc

logger = logging.getLogger(__name__)

class MetricType(Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    name: str
    value: float
    timestamp: float
    metric_type: MetricType
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceAlert:
    """Performance alert data structure"""
    alert_id: str
    level: AlertLevel
    message: str
    metric_name: str
    threshold: float
    current_value: float
    timestamp: float
    resolved: bool = False

class PerformanceMonitor:
    """
    Comprehensive performance monitoring system
    """
    
    def __init__(self, max_history_size: int = 10000):
        """
        Initialize performance monitor
        
        Args:
            max_history_size: Maximum number of metrics to keep in history
        """
        self.logger = logging.getLogger(__name__)
        self.max_history_size = max_history_size
        
        # Metric storage
        self.metrics_history = defaultdict(lambda: deque(maxlen=max_history_size))
        self.current_metrics = {}
        self.metrics_lock = threading.Lock()
        
        # Alert system
        self.alerts = []
        self.alert_thresholds = {}
        self.alert_callbacks = []
        self.alerts_lock = threading.Lock()
        
        # Performance timers
        self.active_timers = {}
        self.timer_lock = threading.Lock()
        
        # System monitoring
        self.system_metrics_enabled = True
        self.system_monitor_thread = None
        self.monitoring_active = False
        
        # Optimization recommendations
        self.optimization_rules = []
        self.recommendations_cache = {}
        
        self._setup_default_thresholds()
        self._setup_optimization_rules()
        
        self.logger.info("Performance monitoring system initialized")

    def _setup_default_thresholds(self):
        """Setup default alert thresholds"""
        self.alert_thresholds = {
            'processing_time': {'warning': 10.0, 'error': 30.0, 'critical': 60.0},
            'memory_usage_percent': {'warning': 70.0, 'error': 85.0, 'critical': 95.0},
            'cpu_usage_percent': {'warning': 80.0, 'error': 90.0, 'critical': 95.0},
            'error_rate': {'warning': 5.0, 'error': 10.0, 'critical': 25.0},
            'api_response_time': {'warning': 5.0, 'error': 15.0, 'critical': 30.0},
            'cache_hit_rate': {'warning': 70.0, 'error': 50.0, 'critical': 30.0},
            'queue_size': {'warning': 100, 'error': 500, 'critical': 1000}
        }

    def _setup_optimization_rules(self):
        """Setup optimization recommendation rules"""
        self.optimization_rules = [
            {
                'name': 'high_memory_usage',
                'condition': lambda metrics: metrics.get('memory_usage_percent', 0) > 80,
                'recommendation': 'Consider implementing memory optimization: garbage collection, object pooling, or increasing available memory'
            },
            {
                'name': 'slow_processing',
                'condition': lambda metrics: metrics.get('avg_processing_time', 0) > 15,
                'recommendation': 'Processing time is high. Consider: parallel processing, caching, or algorithm optimization'
            },
            {
                'name': 'low_cache_hit_rate',
                'condition': lambda metrics: metrics.get('cache_hit_rate', 100) < 60,
                'recommendation': 'Low cache hit rate. Review cache TTL settings and cache key strategies'
            },
            {
                'name': 'high_error_rate',
                'condition': lambda metrics: metrics.get('error_rate', 0) > 5,
                'recommendation': 'High error rate detected. Review error logs and implement better error handling'
            },
            {
                'name': 'api_bottleneck',
                'condition': lambda metrics: metrics.get('avg_api_response_time', 0) > 10,
                'recommendation': 'API response times are high. Consider: rate limiting, request batching, or API optimization'
            }
        ]

    def record_metric(self, name: str, value: float, metric_type: MetricType = MetricType.GAUGE,
                     tags: Optional[Dict[str, str]] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Record a performance metric
        
        Args:
            name: Metric name
            value: Metric value
            metric_type: Type of metric
            tags: Optional tags for the metric
            metadata: Optional metadata
        """
        timestamp = time.time()
        
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=timestamp,
            metric_type=metric_type,
            tags=tags or {},
            metadata=metadata or {}
        )
        
        with self.metrics_lock:
            self.metrics_history[name].append(metric)
            self.current_metrics[name] = metric
        
        # Check for alerts
        self._check_alerts(name, value)
        
        self.logger.debug(f"Recorded metric: {name} = {value}")

    def start_timer(self, timer_name: str) -> str:
        """
        Start a performance timer
        
        Args:
            timer_name: Name of the timer
            
        Returns:
            Timer ID for stopping the timer
        """
        timer_id = f"{timer_name}_{int(time.time() * 1000000)}"
        start_time = time.time()
        
        with self.timer_lock:
            self.active_timers[timer_id] = {
                'name': timer_name,
                'start_time': start_time
            }
        
        return timer_id

    def stop_timer(self, timer_id: str) -> float:
        """
        Stop a performance timer and record the duration
        
        Args:
            timer_id: Timer ID returned by start_timer
            
        Returns:
            Duration in seconds
        """
        end_time = time.time()
        
        with self.timer_lock:
            if timer_id in self.active_timers:
                timer_info = self.active_timers.pop(timer_id)
                duration = end_time - timer_info['start_time']
                
                # Record the timing metric
                self.record_metric(
                    f"{timer_info['name']}_duration",
                    duration,
                    MetricType.TIMER
                )
                
                return duration
            else:
                self.logger.warning(f"Timer {timer_id} not found")
                return 0.0

    def time_function(self, func: Callable, *args, **kwargs) -> tuple:
        """
        Time a function execution
        
        Args:
            func: Function to time
            *args, **kwargs: Function arguments
            
        Returns:
            Tuple of (result, duration)
        """
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # Record timing metric
            func_name = getattr(func, '__name__', 'unknown_function')
            self.record_metric(f"{func_name}_execution_time", duration, MetricType.TIMER)
            
            return result, duration
        except Exception as e:
            duration = time.time() - start_time
            self.record_metric(f"{func_name}_execution_time", duration, MetricType.TIMER)
            raise e

    def _check_alerts(self, metric_name: str, value: float):
        """Check if metric value triggers any alerts"""
        if metric_name not in self.alert_thresholds:
            return
        
        thresholds = self.alert_thresholds[metric_name]
        
        # Determine alert level
        alert_level = None
        if value >= thresholds.get('critical', float('inf')):
            alert_level = AlertLevel.CRITICAL
        elif value >= thresholds.get('error', float('inf')):
            alert_level = AlertLevel.ERROR
        elif value >= thresholds.get('warning', float('inf')):
            alert_level = AlertLevel.WARNING
        
        # For metrics where lower is worse (like cache hit rate)
        if metric_name in ['cache_hit_rate'] and value <= thresholds.get('critical', 0):
            alert_level = AlertLevel.CRITICAL
        elif metric_name in ['cache_hit_rate'] and value <= thresholds.get('error', 0):
            alert_level = AlertLevel.ERROR
        elif metric_name in ['cache_hit_rate'] and value <= thresholds.get('warning', 0):
            alert_level = AlertLevel.WARNING
        
        if alert_level:
            self._create_alert(metric_name, alert_level, value, thresholds)

    def _create_alert(self, metric_name: str, level: AlertLevel, value: float, thresholds: Dict[str, float]):
        """Create a performance alert"""
        alert_id = f"{metric_name}_{level.value}_{int(time.time())}"
        threshold = thresholds.get(level.value, 0)
        
        alert = PerformanceAlert(
            alert_id=alert_id,
            level=level,
            message=f"{metric_name} is {value:.2f}, exceeding {level.value} threshold of {threshold}",
            metric_name=metric_name,
            threshold=threshold,
            current_value=value,
            timestamp=time.time()
        )
        
        with self.alerts_lock:
            self.alerts.append(alert)
            
            # Keep only recent alerts (last 1000)
            if len(self.alerts) > 1000:
                self.alerts = self.alerts[-1000:]
        
        # Trigger alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {str(e)}")
        
        self.logger.warning(f"Performance alert: {alert.message}")

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Add callback function for alerts"""
        self.alert_callbacks.append(callback)

    def start_system_monitoring(self, interval: float = 30.0):
        """
        Start system resource monitoring
        
        Args:
            interval: Monitoring interval in seconds
        """
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitor_system():
            while self.monitoring_active:
                try:
                    # CPU usage
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.record_metric('cpu_usage_percent', cpu_percent)
                    
                    # Memory usage
                    memory = psutil.virtual_memory()
                    self.record_metric('memory_usage_percent', memory.percent)
                    self.record_metric('memory_used_gb', memory.used / (1024**3))
                    self.record_metric('memory_available_gb', memory.available / (1024**3))
                    
                    # Disk usage
                    disk = psutil.disk_usage('/')
                    self.record_metric('disk_usage_percent', (disk.used / disk.total) * 100)
                    
                    # Process-specific metrics
                    process = psutil.Process()
                    self.record_metric('process_memory_mb', process.memory_info().rss / (1024**2))
                    self.record_metric('process_cpu_percent', process.cpu_percent())
                    
                    # Python garbage collection stats
                    gc_stats = gc.get_stats()
                    if gc_stats:
                        self.record_metric('gc_collections', sum(stat['collections'] for stat in gc_stats))
                        self.record_metric('gc_collected', sum(stat['collected'] for stat in gc_stats))
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {str(e)}")
                    time.sleep(interval)
        
        self.system_monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        self.system_monitor_thread.start()
        
        self.logger.info(f"Started system monitoring with {interval}s interval")

    def stop_system_monitoring(self):
        """Stop system resource monitoring"""
        self.monitoring_active = False
        if self.system_monitor_thread:
            self.system_monitor_thread.join(timeout=5)
        self.logger.info("Stopped system monitoring")

    def get_metric_summary(self, metric_name: str, time_window: float = 3600) -> Dict[str, Any]:
        """
        Get summary statistics for a metric
        
        Args:
            metric_name: Name of the metric
            time_window: Time window in seconds (default: 1 hour)
            
        Returns:
            Dictionary with summary statistics
        """
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        with self.metrics_lock:
            if metric_name not in self.metrics_history:
                return {'error': f'Metric {metric_name} not found'}
            
            # Filter metrics within time window
            recent_metrics = [
                m for m in self.metrics_history[metric_name]
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return {'error': f'No recent data for {metric_name}'}
            
            values = [m.value for m in recent_metrics]
            
            summary = {
                'metric_name': metric_name,
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                'latest_value': values[-1],
                'latest_timestamp': recent_metrics[-1].timestamp,
                'time_window_hours': time_window / 3600
            }
            
            # Add percentiles if enough data
            if len(values) >= 10:
                sorted_values = sorted(values)
                summary['p95'] = sorted_values[int(0.95 * len(sorted_values))]
                summary['p99'] = sorted_values[int(0.99 * len(sorted_values))]
            
            return summary

    def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data"""
        dashboard = {
            'timestamp': time.time(),
            'system_metrics': {},
            'processing_metrics': {},
            'alerts': {
                'active_alerts': len([a for a in self.alerts if not a.resolved]),
                'recent_alerts': self.alerts[-10:] if self.alerts else []
            },
            'recommendations': self.get_optimization_recommendations()
        }
        
        # System metrics
        system_metric_names = [
            'cpu_usage_percent', 'memory_usage_percent', 'disk_usage_percent',
            'process_memory_mb', 'process_cpu_percent'
        ]
        
        for metric_name in system_metric_names:
            if metric_name in self.current_metrics:
                dashboard['system_metrics'][metric_name] = self.current_metrics[metric_name].value
        
        # Processing metrics
        processing_metric_names = [
            'processing_time', 'error_rate', 'cache_hit_rate', 'api_response_time'
        ]
        
        for metric_name in processing_metric_names:
            summary = self.get_metric_summary(metric_name, 3600)  # Last hour
            if 'error' not in summary:
                dashboard['processing_metrics'][metric_name] = summary
        
        return dashboard

    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get performance optimization recommendations"""
        recommendations = []
        current_metrics = {name: metric.value for name, metric in self.current_metrics.items()}
        
        for rule in self.optimization_rules:
            try:
                if rule['condition'](current_metrics):
                    recommendations.append({
                        'rule_name': rule['name'],
                        'recommendation': rule['recommendation'],
                        'priority': self._calculate_recommendation_priority(rule['name'], current_metrics),
                        'timestamp': time.time()
                    })
            except Exception as e:
                self.logger.error(f"Error evaluating optimization rule {rule['name']}: {str(e)}")
        
        # Sort by priority (higher is more urgent)
        recommendations.sort(key=lambda x: x['priority'], reverse=True)
        
        return recommendations

    def _calculate_recommendation_priority(self, rule_name: str, metrics: Dict[str, float]) -> int:
        """Calculate priority for optimization recommendation"""
        priority_map = {
            'high_memory_usage': metrics.get('memory_usage_percent', 0),
            'slow_processing': min(100, metrics.get('avg_processing_time', 0) * 5),
            'low_cache_hit_rate': 100 - metrics.get('cache_hit_rate', 100),
            'high_error_rate': metrics.get('error_rate', 0) * 10,
            'api_bottleneck': min(100, metrics.get('avg_api_response_time', 0) * 3)
        }
        
        return int(priority_map.get(rule_name, 50))

    def export_metrics(self, format_type: str = 'json', time_window: float = 3600) -> str:
        """
        Export metrics in specified format
        
        Args:
            format_type: Export format ('json', 'csv')
            time_window: Time window in seconds
            
        Returns:
            Exported metrics as string
        """
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        exported_data = []
        
        with self.metrics_lock:
            for metric_name, metrics in self.metrics_history.items():
                recent_metrics = [
                    m for m in metrics
                    if m.timestamp >= cutoff_time
                ]
                
                for metric in recent_metrics:
                    exported_data.append({
                        'name': metric.name,
                        'value': metric.value,
                        'timestamp': metric.timestamp,
                        'type': metric.metric_type.value,
                        'tags': metric.tags,
                        'metadata': metric.metadata
                    })
        
        if format_type == 'json':
            return json.dumps(exported_data, indent=2)
        elif format_type == 'csv':
            # Simple CSV export
            csv_lines = ['name,value,timestamp,type']
            for data in exported_data:
                csv_lines.append(f"{data['name']},{data['value']},{data['timestamp']},{data['type']}")
            return '\n'.join(csv_lines)
        else:
            raise ValueError(f"Unsupported export format: {format_type}")

    def clear_old_metrics(self, max_age: float = 86400):
        """
        Clear metrics older than specified age
        
        Args:
            max_age: Maximum age in seconds (default: 24 hours)
        """
        current_time = time.time()
        cutoff_time = current_time - max_age
        
        with self.metrics_lock:
            for metric_name in list(self.metrics_history.keys()):
                # Filter out old metrics
                recent_metrics = deque([
                    m for m in self.metrics_history[metric_name]
                    if m.timestamp >= cutoff_time
                ], maxlen=self.max_history_size)
                
                if recent_metrics:
                    self.metrics_history[metric_name] = recent_metrics
                else:
                    # Remove empty metric history
                    del self.metrics_history[metric_name]
                    if metric_name in self.current_metrics:
                        del self.current_metrics[metric_name]
        
        self.logger.info(f"Cleared metrics older than {max_age} seconds")

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
