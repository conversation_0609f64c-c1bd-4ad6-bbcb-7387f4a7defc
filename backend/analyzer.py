"""
Advanced Content Analysis System
Extracts customer testimonials, reviews, pricing comparisons, key selling points,
and differentiators from website content using AI and pattern matching.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from bs4 import BeautifulSoup, Tag
import requests
import json

logger = logging.getLogger(__name__)

class ContentType(Enum):
    TESTIMONIAL = "testimonial"
    REVIEW = "review"
    PRICING_COMPARISON = "pricing_comparison"
    FEATURE_COMPARISON = "feature_comparison"
    SELLING_POINT = "selling_point"
    DIFFERENTIATOR = "differentiator"
    SOCIAL_PROOF = "social_proof"

@dataclass
class ContentAnalysisResult:
    """Result of content analysis"""
    content_type: ContentType
    content: str
    metadata: Dict[str, Any]
    confidence: float
    source_location: str

class AdvancedContentAnalyzer:
    """
    Advanced content analysis system for extracting business-critical information
    """
    
    def __init__(self, ai_api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.ai_api_key = ai_api_key
        
        # Testimonial indicators
        self.testimonial_indicators = [
            'testimonial', 'review', 'feedback', 'customer says', 'client says',
            'user review', 'customer review', 'success story', 'case study'
        ]
        
        # Review indicators
        self.review_indicators = [
            'rating', 'stars', 'score', 'review', 'feedback', 'opinion',
            'experience', 'recommendation', 'verdict'
        ]
        
        # Selling point indicators
        self.selling_point_indicators = [
            'benefit', 'advantage', 'feature', 'capability', 'strength',
            'why choose', 'what makes us', 'unique', 'special', 'different'
        ]
        
        # Differentiator indicators
        self.differentiator_indicators = [
            'unlike', 'different from', 'compared to', 'vs', 'versus',
            'alternative to', 'better than', 'unique', 'only', 'first'
        ]
        
        # Social proof indicators
        self.social_proof_indicators = [
            'trusted by', 'used by', 'customers', 'companies', 'users',
            'million', 'thousand', 'enterprise', 'fortune', 'startup'
        ]

    def analyze_content(self, url: str, html_content: str) -> List[ContentAnalysisResult]:
        """
        Perform comprehensive content analysis on a webpage
        """
        results = []
        soup = BeautifulSoup(html_content, 'html.parser')
        
        try:
            # Extract testimonials
            testimonial_results = self._extract_testimonials(soup)
            results.extend(testimonial_results)
            
            # Extract reviews
            review_results = self._extract_reviews(soup)
            results.extend(review_results)
            
            # Extract pricing comparisons
            pricing_results = self._extract_pricing_comparisons(soup)
            results.extend(pricing_results)
            
            # Extract feature comparisons
            feature_results = self._extract_feature_comparisons(soup)
            results.extend(feature_results)
            
            # Extract selling points
            selling_point_results = self._extract_selling_points(soup)
            results.extend(selling_point_results)
            
            # Extract differentiators
            differentiator_results = self._extract_differentiators(soup)
            results.extend(differentiator_results)
            
            # Extract social proof
            social_proof_results = self._extract_social_proof(soup)
            results.extend(social_proof_results)
            
            # AI-powered enhancement if API key available
            if self.ai_api_key and results:
                enhanced_results = self._enhance_with_ai(results, html_content)
                results.extend(enhanced_results)
            
            self.logger.info(f"Analyzed content and found {len(results)} elements from {url}")
            
        except Exception as e:
            self.logger.error(f"Error analyzing content from {url}: {str(e)}")
        
        return results

    def _extract_testimonials(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract customer testimonials"""
        results = []
        
        # Look for testimonial-related elements
        testimonial_selectors = [
            '.testimonial', '.review', '.feedback', '.customer-review',
            '[class*="testimonial"]', '[class*="review"]', '[class*="feedback"]',
            'blockquote', '.quote', '[class*="quote"]'
        ]
        
        for selector in testimonial_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    testimonial_data = self._parse_testimonial_element(element)
                    
                    if testimonial_data and self._is_valid_testimonial(testimonial_data):
                        result = ContentAnalysisResult(
                            content_type=ContentType.TESTIMONIAL,
                            content=testimonial_data['text'],
                            metadata=testimonial_data,
                            confidence=self._calculate_testimonial_confidence(testimonial_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing testimonial element: {str(e)}")
                    continue
        
        return results

    def _parse_testimonial_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a testimonial element"""
        testimonial_data = {
            'text': '',
            'author': '',
            'company': '',
            'title': '',
            'rating': None,
            'date': ''
        }
        
        # Extract main text
        testimonial_data['text'] = element.get_text(strip=True)
        
        # Look for author information
        author_selectors = [
            '.author', '.name', '.customer-name', '.reviewer',
            '[class*="author"]', '[class*="name"]'
        ]
        
        for selector in author_selectors:
            author_elem = element.select_one(selector)
            if author_elem:
                testimonial_data['author'] = author_elem.get_text(strip=True)
                break
        
        # Look for company information
        company_selectors = [
            '.company', '.organization', '.business',
            '[class*="company"]', '[class*="org"]'
        ]
        
        for selector in company_selectors:
            company_elem = element.select_one(selector)
            if company_elem:
                testimonial_data['company'] = company_elem.get_text(strip=True)
                break
        
        # Look for title/position
        title_selectors = [
            '.title', '.position', '.job-title',
            '[class*="title"]', '[class*="position"]'
        ]
        
        for selector in title_selectors:
            title_elem = element.select_one(selector)
            if title_elem:
                testimonial_data['title'] = title_elem.get_text(strip=True)
                break
        
        # Look for rating
        rating_elem = element.select_one('[class*="rating"], [class*="stars"], [class*="score"]')
        if rating_elem:
            rating_text = rating_elem.get_text()
            rating_match = re.search(r'(\d+(?:\.\d+)?)', rating_text)
            if rating_match:
                testimonial_data['rating'] = float(rating_match.group(1))
        
        return testimonial_data

    def _is_valid_testimonial(self, testimonial_data: Dict[str, Any]) -> bool:
        """Check if extracted testimonial data is valid"""
        text = testimonial_data.get('text', '')
        return (
            len(text) > 20 and  # Minimum length
            len(text) < 1000 and  # Maximum length
            (testimonial_data.get('author') or 
             any(indicator in text.lower() for indicator in self.testimonial_indicators))
        )

    def _calculate_testimonial_confidence(self, testimonial_data: Dict[str, Any]) -> float:
        """Calculate confidence score for testimonial"""
        confidence = 0.3  # Base confidence
        
        if testimonial_data.get('author'):
            confidence += 0.3
        if testimonial_data.get('company'):
            confidence += 0.2
        if testimonial_data.get('title'):
            confidence += 0.1
        if testimonial_data.get('rating'):
            confidence += 0.1
        
        return min(confidence, 1.0)

    def _extract_reviews(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract customer reviews and ratings"""
        results = []
        
        # Look for review-related elements
        review_selectors = [
            '.review', '.rating', '.feedback', '.user-review',
            '[class*="review"]', '[class*="rating"]', '[class*="feedback"]'
        ]
        
        for selector in review_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    review_data = self._parse_review_element(element)
                    
                    if review_data and self._is_valid_review(review_data):
                        result = ContentAnalysisResult(
                            content_type=ContentType.REVIEW,
                            content=review_data['text'],
                            metadata=review_data,
                            confidence=self._calculate_review_confidence(review_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing review element: {str(e)}")
                    continue
        
        return results

    def _parse_review_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a review element"""
        review_data = {
            'text': element.get_text(strip=True),
            'rating': None,
            'source': '',
            'date': '',
            'helpful_count': None
        }
        
        # Extract rating
        rating_patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:out of|/)\s*(\d+)',  # 4.5 out of 5
            r'(\d+(?:\.\d+)?)\s*stars?',  # 4.5 stars
            r'Rating:\s*(\d+(?:\.\d+)?)',  # Rating: 4.5
        ]
        
        text = review_data['text']
        for pattern in rating_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                review_data['rating'] = float(match.group(1))
                break
        
        # Look for source platform
        source_patterns = [
            r'(G2|Capterra|TrustPilot|ProductHunt|AppStore|Google)',
            r'reviewed on\s+(\w+)',
            r'source:\s*(\w+)'
        ]
        
        for pattern in source_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                review_data['source'] = match.group(1)
                break
        
        return review_data

    def _is_valid_review(self, review_data: Dict[str, Any]) -> bool:
        """Check if extracted review data is valid"""
        text = review_data.get('text', '')
        return (
            len(text) > 10 and
            (review_data.get('rating') is not None or
             any(indicator in text.lower() for indicator in self.review_indicators))
        )

    def _calculate_review_confidence(self, review_data: Dict[str, Any]) -> float:
        """Calculate confidence score for review"""
        confidence = 0.4  # Base confidence
        
        if review_data.get('rating'):
            confidence += 0.3
        if review_data.get('source'):
            confidence += 0.2
        if review_data.get('date'):
            confidence += 0.1
        
        return min(confidence, 1.0)

    def _extract_pricing_comparisons(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract pricing comparison information"""
        results = []
        
        # Look for pricing comparison elements
        comparison_selectors = [
            '.pricing-comparison', '.price-comparison', '.plan-comparison',
            '[class*="pricing"][class*="comparison"]',
            'table[class*="pricing"]', 'table[class*="plan"]'
        ]
        
        for selector in comparison_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    comparison_data = self._parse_pricing_comparison(element)
                    
                    if comparison_data and self._is_valid_pricing_comparison(comparison_data):
                        result = ContentAnalysisResult(
                            content_type=ContentType.PRICING_COMPARISON,
                            content=comparison_data['summary'],
                            metadata=comparison_data,
                            confidence=self._calculate_pricing_comparison_confidence(comparison_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing pricing comparison: {str(e)}")
                    continue
        
        return results

    def _parse_pricing_comparison(self, element: Tag) -> Dict[str, Any]:
        """Parse pricing comparison element"""
        comparison_data = {
            'plans': [],
            'features': [],
            'prices': [],
            'summary': ''
        }
        
        text = element.get_text()
        comparison_data['summary'] = text[:500]  # First 500 chars as summary
        
        # Extract plan names
        plan_headers = element.find_all(['h1', 'h2', 'h3', 'h4', 'th', '.plan-name'])
        for header in plan_headers:
            plan_text = header.get_text(strip=True)
            if plan_text and len(plan_text) < 50:
                comparison_data['plans'].append(plan_text)
        
        # Extract prices
        price_patterns = [
            r'[$€£¥₹]\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[$€£¥₹]'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, text)
            comparison_data['prices'].extend(matches)
        
        # Extract features
        feature_items = element.find_all(['li', 'tr'])
        for item in feature_items:
            feature_text = item.get_text(strip=True)
            if feature_text and len(feature_text) < 200:
                comparison_data['features'].append(feature_text)
        
        return comparison_data

    def _is_valid_pricing_comparison(self, comparison_data: Dict[str, Any]) -> bool:
        """Check if pricing comparison data is valid"""
        return (
            len(comparison_data.get('plans', [])) >= 2 or
            len(comparison_data.get('prices', [])) >= 2
        )

    def _calculate_pricing_comparison_confidence(self, comparison_data: Dict[str, Any]) -> float:
        """Calculate confidence for pricing comparison"""
        confidence = 0.2
        
        plan_count = len(comparison_data.get('plans', []))
        price_count = len(comparison_data.get('prices', []))
        feature_count = len(comparison_data.get('features', []))
        
        if plan_count >= 2:
            confidence += 0.3
        if price_count >= 2:
            confidence += 0.3
        if feature_count >= 3:
            confidence += 0.2
        
        return min(confidence, 1.0)

    def _extract_feature_comparisons(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract feature comparison information"""
        results = []
        
        # Look for feature comparison tables and lists
        feature_selectors = [
            '.feature-comparison', '.features-table', '.comparison-table',
            'table[class*="feature"]', 'table[class*="comparison"]',
            '.feature-list', '[class*="feature"][class*="comparison"]'
        ]
        
        for selector in feature_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    feature_data = self._parse_feature_comparison(element)
                    
                    if feature_data and self._is_valid_feature_comparison(feature_data):
                        result = ContentAnalysisResult(
                            content_type=ContentType.FEATURE_COMPARISON,
                            content=feature_data['summary'],
                            metadata=feature_data,
                            confidence=self._calculate_feature_comparison_confidence(feature_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing feature comparison: {str(e)}")
                    continue
        
        return results

    def _parse_feature_comparison(self, element: Tag) -> Dict[str, Any]:
        """Parse feature comparison element"""
        feature_data = {
            'features': [],
            'categories': [],
            'comparisons': [],
            'summary': ''
        }
        
        text = element.get_text()
        feature_data['summary'] = text[:500]
        
        # Extract features
        feature_items = element.find_all(['li', 'tr', 'td'])
        for item in feature_items:
            feature_text = item.get_text(strip=True)
            if feature_text and 10 < len(feature_text) < 200:
                feature_data['features'].append(feature_text)
        
        # Extract categories
        headers = element.find_all(['th', 'h1', 'h2', 'h3', 'h4'])
        for header in headers:
            category_text = header.get_text(strip=True)
            if category_text and len(category_text) < 100:
                feature_data['categories'].append(category_text)
        
        return feature_data

    def _is_valid_feature_comparison(self, feature_data: Dict[str, Any]) -> bool:
        """Check if feature comparison data is valid"""
        return len(feature_data.get('features', [])) >= 3

    def _calculate_feature_comparison_confidence(self, feature_data: Dict[str, Any]) -> float:
        """Calculate confidence for feature comparison"""
        confidence = 0.3
        
        feature_count = len(feature_data.get('features', []))
        category_count = len(feature_data.get('categories', []))
        
        confidence += min(feature_count * 0.05, 0.4)
        confidence += min(category_count * 0.1, 0.3)
        
        return min(confidence, 1.0)

    def _extract_selling_points(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract key selling points and benefits"""
        results = []
        
        # Look for selling point elements
        selling_point_selectors = [
            '.benefits', '.advantages', '.features', '.selling-points',
            '[class*="benefit"]', '[class*="advantage"]', '[class*="why"]',
            '.value-proposition', '[class*="value"]'
        ]
        
        for selector in selling_point_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    selling_points = self._parse_selling_points(element)
                    
                    for j, point in enumerate(selling_points):
                        if self._is_valid_selling_point(point):
                            result = ContentAnalysisResult(
                                content_type=ContentType.SELLING_POINT,
                                content=point['text'],
                                metadata=point,
                                confidence=self._calculate_selling_point_confidence(point),
                                source_location=f"{selector}[{i}][{j}]"
                            )
                            results.append(result)
                            
                except Exception as e:
                    self.logger.warning(f"Error parsing selling points: {str(e)}")
                    continue
        
        return results

    def _parse_selling_points(self, element: Tag) -> List[Dict[str, Any]]:
        """Parse selling points from an element"""
        selling_points = []
        
        # Extract from list items
        list_items = element.find_all(['li', 'div', 'p'])
        
        for item in list_items:
            text = item.get_text(strip=True)
            if text and 10 < len(text) < 300:
                point = {
                    'text': text,
                    'has_benefit_keywords': any(keyword in text.lower() for keyword in self.selling_point_indicators),
                    'length': len(text)
                }
                selling_points.append(point)
        
        return selling_points

    def _is_valid_selling_point(self, point: Dict[str, Any]) -> bool:
        """Check if selling point is valid"""
        return (
            point.get('has_benefit_keywords', False) or
            (10 < point.get('length', 0) < 300)
        )

    def _calculate_selling_point_confidence(self, point: Dict[str, Any]) -> float:
        """Calculate confidence for selling point"""
        confidence = 0.4
        
        if point.get('has_benefit_keywords'):
            confidence += 0.3
        
        length = point.get('length', 0)
        if 50 < length < 200:  # Optimal length
            confidence += 0.3
        
        return min(confidence, 1.0)

    def _extract_differentiators(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract competitive differentiators"""
        results = []
        
        # Look for differentiator content
        text_content = soup.get_text()
        
        # Find sentences with differentiator keywords
        sentences = re.split(r'[.!?]+', text_content)
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if len(sentence) > 20 and any(indicator in sentence.lower() for indicator in self.differentiator_indicators):
                differentiator_data = {
                    'text': sentence,
                    'keywords': [kw for kw in self.differentiator_indicators if kw in sentence.lower()],
                    'context': 'competitive_analysis'
                }
                
                result = ContentAnalysisResult(
                    content_type=ContentType.DIFFERENTIATOR,
                    content=sentence,
                    metadata=differentiator_data,
                    confidence=0.7,
                    source_location=f"text_analysis[{i}]"
                )
                results.append(result)
        
        return results

    def _extract_social_proof(self, soup: BeautifulSoup) -> List[ContentAnalysisResult]:
        """Extract social proof elements"""
        results = []
        
        # Look for social proof elements
        social_proof_selectors = [
            '.social-proof', '.customers', '.clients', '.users',
            '[class*="social"]', '[class*="customer"]', '[class*="client"]',
            '.logos', '.testimonials', '[class*="logo"]'
        ]
        
        for selector in social_proof_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    social_proof_data = self._parse_social_proof(element)
                    
                    if social_proof_data and self._is_valid_social_proof(social_proof_data):
                        result = ContentAnalysisResult(
                            content_type=ContentType.SOCIAL_PROOF,
                            content=social_proof_data['text'],
                            metadata=social_proof_data,
                            confidence=self._calculate_social_proof_confidence(social_proof_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing social proof: {str(e)}")
                    continue
        
        return results

    def _parse_social_proof(self, element: Tag) -> Dict[str, Any]:
        """Parse social proof element"""
        social_proof_data = {
            'text': element.get_text(strip=True),
            'numbers': [],
            'companies': [],
            'metrics': []
        }
        
        text = social_proof_data['text']
        
        # Extract numbers (users, customers, etc.)
        number_patterns = [
            r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:million|thousand|k|m|users|customers|companies)',
            r'(\d+(?:,\d{3})*)\+?\s*(?:users|customers|companies|clients)'
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            social_proof_data['numbers'].extend(matches)
        
        # Extract company names
        company_patterns = [
            r'(Fortune \d+)',
            r'(Google|Microsoft|Apple|Amazon|Facebook|Netflix|Uber|Airbnb)',
            r'trusted by\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            social_proof_data['companies'].extend(matches)
        
        return social_proof_data

    def _is_valid_social_proof(self, social_proof_data: Dict[str, Any]) -> bool:
        """Check if social proof data is valid"""
        return (
            len(social_proof_data.get('numbers', [])) > 0 or
            len(social_proof_data.get('companies', [])) > 0 or
            any(indicator in social_proof_data.get('text', '').lower() for indicator in self.social_proof_indicators)
        )

    def _calculate_social_proof_confidence(self, social_proof_data: Dict[str, Any]) -> float:
        """Calculate confidence for social proof"""
        confidence = 0.3
        
        if social_proof_data.get('numbers'):
            confidence += 0.3
        if social_proof_data.get('companies'):
            confidence += 0.4
        
        return min(confidence, 1.0)

    def _enhance_with_ai(self, results: List[ContentAnalysisResult], html_content: str) -> List[ContentAnalysisResult]:
        """Enhance content analysis with AI insights"""
        enhanced_results = []
        
        try:
            # Prepare content summary for AI analysis
            content_summary = self._prepare_content_summary(results, html_content)
            
            # AI analysis prompt
            prompt = f"""
            Analyze this website content and identify additional key selling points, differentiators, and value propositions:
            
            {content_summary}
            
            Please identify:
            1. Key value propositions not already captured
            2. Competitive advantages and differentiators
            3. Target audience indicators
            4. Business model insights
            
            Return as JSON with confidence scores.
            """
            
            # Note: This would call an AI API if implemented
            # For now, return empty list
            
        except Exception as e:
            self.logger.warning(f"Error in AI enhancement: {str(e)}")
        
        return enhanced_results

    def _prepare_content_summary(self, results: List[ContentAnalysisResult], html_content: str) -> str:
        """Prepare content summary for AI analysis"""
        summary_parts = []
        
        # Add existing analysis results
        for result in results[:10]:  # Limit to first 10 results
            summary_parts.append(f"{result.content_type.value}: {result.content[:200]}")
        
        # Add raw content sample
        soup = BeautifulSoup(html_content, 'html.parser')
        text_content = soup.get_text()
        summary_parts.append(f"Raw content sample: {text_content[:1000]}")
        
        return "\n\n".join(summary_parts)

    def get_content_analysis_summary(self, results: List[ContentAnalysisResult]) -> Dict[str, Any]:
        """Generate summary of content analysis results"""
        summary = {
            'total_elements': len(results),
            'by_type': {},
            'high_confidence_count': 0,
            'average_confidence': 0.0,
            'key_insights': []
        }
        
        if not results:
            return summary
        
        # Group by type
        for result in results:
            content_type = result.content_type.value
            if content_type not in summary['by_type']:
                summary['by_type'][content_type] = 0
            summary['by_type'][content_type] += 1
            
            if result.confidence >= 0.7:
                summary['high_confidence_count'] += 1
        
        # Calculate average confidence
        summary['average_confidence'] = sum(r.confidence for r in results) / len(results)
        
        # Extract key insights
        high_confidence_results = [r for r in results if r.confidence >= 0.7]
        for result in high_confidence_results[:5]:  # Top 5 insights
            summary['key_insights'].append({
                'type': result.content_type.value,
                'content': result.content[:100],
                'confidence': result.confidence
            })
        
        return summary
