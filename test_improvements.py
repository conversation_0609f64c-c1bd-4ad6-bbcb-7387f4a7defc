#!/usr/bin/env python3
"""
Test script for AI Navigator Scrapers improvements:
1. Entity existence check
2. RefLink using actual website URL
3. Features taxonomy system
4. Improved tags taxonomy classification
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_enhanced_scraping():
    """Test the enhanced scraping with all improvements"""
    
    # Test tools - mix of existing and new tools
    test_tools = [
        {
            "name": "Test Tool Enhanced",
            "url": "https://example.com/test-tool"
        },
        {
            "name": "ChatGPT",  # This should exist and trigger entity existence check
            "url": "https://chat.openai.com"
        },
        {
            "name": "Midjourney AI",
            "url": "https://www.midjourney.com"
        }
    ]
    
    logger.info("🚀 Starting enhanced scraping test...")
    
    # Send request to enhanced scraping endpoint
    response = requests.post(
        f"{BASE_URL}/api/start-enhanced-scraping",
        json={
            "tools": test_tools,
            "use_parallel": False,
            "use_phase3": True
        }
    )
    
    if response.status_code != 200:
        logger.error(f"❌ Failed to start enhanced scraping: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return None
    
    result = response.json()
    job_id = result.get('job_id')
    logger.info(f"✅ Enhanced scraping started with job ID: {job_id}")
    
    return job_id

def monitor_job(job_id):
    """Monitor job progress and results"""
    logger.info(f"📊 Monitoring job {job_id}...")
    
    max_attempts = 60  # 5 minutes max
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            
            if response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {response.status_code}")
                break
            
            status = response.json()
            progress = status.get('progress', 0)
            state = status.get('status', 'unknown')
            
            logger.info(f"📈 Job {job_id}: {state} - {progress:.1f}% complete")
            
            # Log any results
            if 'results' in status and status['results']:
                logger.info(f"📋 Results so far: {len(status['results'])} items processed")
                
                # Check for entity existence check logs
                for result in status['results']:
                    if 'skipped' in str(result).lower() or 'exists' in str(result).lower():
                        logger.info(f"🔍 Entity existence check: {result}")
            
            if state in ['completed', 'failed']:
                logger.info(f"🏁 Job {job_id} finished with state: {state}")
                return status
            
            time.sleep(5)  # Wait 5 seconds between checks
            attempt += 1
            
        except Exception as e:
            logger.error(f"❌ Error monitoring job: {str(e)}")
            break
    
    logger.warning(f"⏰ Job monitoring timed out after {max_attempts * 5} seconds")
    return None

def analyze_results(final_status):
    """Analyze the final results to verify improvements"""
    if not final_status:
        logger.error("❌ No final status to analyze")
        return
    
    logger.info("🔍 Analyzing results for improvements...")
    
    results = final_status.get('results', [])
    errors = final_status.get('errors', [])
    
    logger.info(f"📊 Total results: {len(results)}")
    logger.info(f"❌ Total errors: {len(errors)}")
    
    # Check for entity existence check functionality
    existence_checks = 0
    reflink_fixes = 0
    features_populated = 0
    improved_tags = 0
    
    for result in results:
        result_str = str(result).lower()
        
        # Check for entity existence messages
        if 'already exists' in result_str or 'skipping' in result_str:
            existence_checks += 1
            logger.info(f"✅ Entity existence check worked: {result}")
        
        # Check for refLink improvements (should not contain redirect URLs)
        if isinstance(result, dict) and 'ref_link' in result:
            ref_link = result['ref_link']
            if not any(redirect in ref_link for redirect in ['futuretools.link', 'redirect', 'affiliate']):
                reflink_fixes += 1
                logger.info(f"✅ RefLink uses actual URL: {ref_link}")
        
        # Check for features field population
        if isinstance(result, dict) and 'features' in result:
            features = result['features']
            if features and len(features) > 0:
                features_populated += 1
                logger.info(f"✅ Features populated: {len(features)} features")
        
        # Check for improved tags (not defaulting to Cloud-Based)
        if isinstance(result, dict) and 'tags' in result:
            tags = result.get('tags', [])
            if tags and not all(tag.get('name', '').lower() == 'cloud-based' for tag in tags):
                improved_tags += 1
                logger.info(f"✅ Improved tags: {[tag.get('name') for tag in tags]}")
    
    # Summary
    logger.info("📋 IMPROVEMENT VERIFICATION SUMMARY:")
    logger.info(f"   🔍 Entity existence checks: {existence_checks}")
    logger.info(f"   🔗 RefLink fixes: {reflink_fixes}")
    logger.info(f"   🏷️  Features populated: {features_populated}")
    logger.info(f"   🏷️  Improved tags: {improved_tags}")
    
    # Check errors for any issues
    if errors:
        logger.warning("⚠️  Errors encountered:")
        for error in errors[:5]:  # Show first 5 errors
            logger.warning(f"   ❌ {error}")

def main():
    """Main test function"""
    logger.info("🧪 Starting AI Navigator Scrapers Improvements Test")
    logger.info("=" * 60)
    
    # Test 1: Start enhanced scraping
    job_id = test_enhanced_scraping()
    if not job_id:
        logger.error("❌ Failed to start enhanced scraping test")
        return
    
    # Test 2: Monitor job progress
    final_status = monitor_job(job_id)
    
    # Test 3: Analyze results
    analyze_results(final_status)
    
    logger.info("=" * 60)
    logger.info("🏁 Test completed!")

if __name__ == "__main__":
    main()
