"""
Comprehensive Test Suite for Phase 3: Advanced Features
Tests all Phase 3 components including structured data extraction,
advanced content analysis, performance analysis, caching, parallel processing,
and performance monitoring.
"""

import unittest
import time
import json
import asyncio
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import sys

# Import Phase 3 components
try:
    from structured_data_extractor import StructuredDataExtractor, StructuredDataType
    from advanced_content_analyzer import AdvancedContentAnalyzer, ContentType
    from performance_technical_analyzer import PerformanceTechnicalAnalyzer, PerformanceMetric
    # Skip cache and monitoring tests if dependencies not available
    try:
        from enhancement_cache import EnhancementCache, CacheLevel
        CACHE_AVAILABLE = True
    except ImportError:
        CACHE_AVAILABLE = False
        print("⚠️  Redis not available - skipping cache tests")

    try:
        from parallel_processing_system import ParallelProcessingSystem, ProcessingTask, ProcessingMode
        PARALLEL_AVAILABLE = True
    except ImportError:
        PARALLEL_AVAILABLE = False
        print("⚠️  Parallel processing dependencies not available")

    try:
        from performance_monitoring_system import PerformanceMonitor, MetricType, AlertLevel
        MONITORING_AVAILABLE = True
    except ImportError:
        MONITORING_AVAILABLE = False
        print("⚠️  Performance monitoring dependencies not available")

except ImportError as e:
    print(f"❌ Error importing Phase 3 components: {e}")
    sys.exit(1)

class TestStructuredDataExtractor(unittest.TestCase):
    """Test structured data extraction functionality"""
    
    def setUp(self):
        self.extractor = StructuredDataExtractor()
        
        # Sample HTML with structured data
        self.sample_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "SoftwareApplication",
                "name": "Test AI Tool",
                "description": "A test AI application"
            }
            </script>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <div class="pricing">
                <h3>Basic Plan</h3>
                <p>$9.99 per month</p>
                <h3>Pro Plan</h3>
                <p>$19.99 per month</p>
            </div>
            <div class="features">
                <ul>
                    <li>AI-powered analysis</li>
                    <li>Real-time processing</li>
                    <li>Advanced algorithms</li>
                </ul>
            </div>
            <section itemscope itemtype="https://schema.org/Product">
                <h1 itemprop="name">Test Product</h1>
                <p itemprop="description">Product description</p>
            </section>
        </body>
        </html>
        """

    def test_extract_json_ld(self):
        """Test JSON-LD extraction"""
        results = self.extractor.extract_all_structured_data("https://test.com", self.sample_html)
        
        json_ld_results = [r for r in results if r.data_type == StructuredDataType.JSON_LD]
        self.assertGreater(len(json_ld_results), 0)
        
        json_ld = json_ld_results[0]
        self.assertEqual(json_ld.confidence, 0.95)
        self.assertIn("@type", json_ld.content)
        self.assertEqual(json_ld.content["@type"], "SoftwareApplication")

    def test_extract_pricing_tables(self):
        """Test pricing table extraction"""
        results = self.extractor.extract_all_structured_data("https://test.com", self.sample_html)
        
        pricing_results = [r for r in results if r.data_type == StructuredDataType.PRICING_TABLE]
        self.assertGreater(len(pricing_results), 0)
        
        pricing = pricing_results[0]
        self.assertGreater(len(pricing.content['prices']), 0)
        self.assertIn('9.99', pricing.content['prices'])

    def test_extract_microdata(self):
        """Test microdata extraction"""
        results = self.extractor.extract_all_structured_data("https://test.com", self.sample_html)
        
        microdata_results = [r for r in results if r.data_type == StructuredDataType.MICRODATA]
        self.assertGreater(len(microdata_results), 0)
        
        microdata = microdata_results[0]
        self.assertEqual(microdata.confidence, 0.85)
        self.assertIn("@type", microdata.content)

    def test_get_structured_data_summary(self):
        """Test structured data summary generation"""
        results = self.extractor.extract_all_structured_data("https://test.com", self.sample_html)
        summary = self.extractor.get_structured_data_summary(results)
        
        self.assertGreater(summary['total_elements'], 0)
        self.assertIn('by_type', summary)
        self.assertGreater(summary['average_confidence'], 0)

class TestAdvancedContentAnalyzer(unittest.TestCase):
    """Test advanced content analysis functionality"""
    
    def setUp(self):
        self.analyzer = AdvancedContentAnalyzer()
        
        # Sample HTML with content to analyze
        self.sample_html = """
        <!DOCTYPE html>
        <html>
        <body>
            <div class="testimonial">
                <blockquote>"This tool has revolutionized our workflow!"</blockquote>
                <div class="author">John Smith</div>
                <div class="company">Tech Corp</div>
                <div class="rating">5 stars</div>
            </div>
            <div class="features">
                <h3>Key Benefits</h3>
                <ul>
                    <li>Saves 50% time on data processing</li>
                    <li>Improves accuracy by 90%</li>
                    <li>Easy to integrate with existing systems</li>
                </ul>
            </div>
            <div class="social-proof">
                <p>Trusted by over 10,000 companies worldwide</p>
                <p>Used by Fortune 500 companies</p>
            </div>
            <div class="pricing-comparison">
                <table>
                    <tr><th>Feature</th><th>Basic</th><th>Pro</th></tr>
                    <tr><td>API Calls</td><td>1,000</td><td>10,000</td></tr>
                    <tr><td>Support</td><td>Email</td><td>24/7</td></tr>
                </table>
            </div>
        </body>
        </html>
        """

    def test_extract_testimonials(self):
        """Test testimonial extraction"""
        results = self.analyzer.analyze_content("https://test.com", self.sample_html)
        
        testimonials = [r for r in results if r.content_type == ContentType.TESTIMONIAL]
        self.assertGreater(len(testimonials), 0)
        
        testimonial = testimonials[0]
        self.assertIn("revolutionized", testimonial.content)
        self.assertGreater(testimonial.confidence, 0.5)

    def test_extract_selling_points(self):
        """Test selling points extraction"""
        results = self.analyzer.analyze_content("https://test.com", self.sample_html)
        
        selling_points = [r for r in results if r.content_type == ContentType.SELLING_POINT]
        self.assertGreater(len(selling_points), 0)

    def test_extract_social_proof(self):
        """Test social proof extraction"""
        results = self.analyzer.analyze_content("https://test.com", self.sample_html)
        
        social_proof = [r for r in results if r.content_type == ContentType.SOCIAL_PROOF]
        self.assertGreater(len(social_proof), 0)
        
        proof = social_proof[0]
        self.assertIn("10,000", proof.metadata.get('numbers', []))

    def test_get_content_analysis_summary(self):
        """Test content analysis summary"""
        results = self.analyzer.analyze_content("https://test.com", self.sample_html)
        summary = self.analyzer.get_content_analysis_summary(results)
        
        self.assertGreater(summary['total_elements'], 0)
        self.assertIn('by_type', summary)
        self.assertGreater(summary['average_confidence'], 0)

class TestPerformanceTechnicalAnalyzer(unittest.TestCase):
    """Test performance and technical analysis"""
    
    def setUp(self):
        self.analyzer = PerformanceTechnicalAnalyzer()
        
        # Sample HTML with performance indicators
        self.sample_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="stylesheet" href="styles.css">
            <style>
                @media (max-width: 768px) {
                    .container { width: 100%; }
                }
            </style>
        </head>
        <body>
            <div class="requirements">
                <h3>System Requirements</h3>
                <ul>
                    <li>Windows 10 or later</li>
                    <li>4GB RAM minimum</li>
                    <li>Chrome 90+ or Firefox 88+</li>
                    <li>Node.js 14.0 or higher</li>
                </ul>
            </div>
            <img src="logo.png" loading="lazy" srcset="logo-small.png 480w, logo-large.png 800w">
            <img src="hero.jpg" alt="Hero image">
        </body>
        </html>
        """

    @patch('requests.get')
    def test_analyze_load_time(self, mock_get):
        """Test load time analysis"""
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b"test content"
        mock_get.return_value = mock_response
        
        results = self.analyzer.analyze_performance_and_technical("https://test.com", self.sample_html)
        
        load_time_results = [r for r in results if r.metric_type == PerformanceMetric.LOAD_TIME]
        self.assertGreater(len(load_time_results), 0)
        
        load_time = load_time_results[0]
        self.assertIsInstance(load_time.value, (int, float))
        self.assertGreaterEqual(load_time.score, 0)
        self.assertLessEqual(load_time.score, 100)

    def test_analyze_mobile_friendliness(self):
        """Test mobile-friendliness analysis"""
        results = self.analyzer.analyze_performance_and_technical("https://test.com", self.sample_html)
        
        mobile_results = [r for r in results if r.metric_type == PerformanceMetric.MOBILE_FRIENDLY]
        self.assertGreater(len(mobile_results), 0)
        
        mobile = mobile_results[0]
        self.assertTrue(mobile.value['viewport_meta'])
        self.assertGreater(mobile.score, 50)  # Should score well due to viewport meta

    def test_analyze_responsive_design(self):
        """Test responsive design analysis"""
        results = self.analyzer.analyze_performance_and_technical("https://test.com", self.sample_html)
        
        responsive_results = [r for r in results if r.metric_type == PerformanceMetric.RESPONSIVE_DESIGN]
        self.assertGreater(len(responsive_results), 0)
        
        responsive = responsive_results[0]
        self.assertGreater(responsive.value['media_queries'], 0)

    def test_extract_technical_specs(self):
        """Test technical specifications extraction"""
        results = self.analyzer.analyze_performance_and_technical("https://test.com", self.sample_html)
        
        tech_specs_results = [r for r in results if r.metric_type == PerformanceMetric.TECHNICAL_SPECS]
        self.assertGreater(len(tech_specs_results), 0)
        
        tech_specs = tech_specs_results[0]
        self.assertGreater(len(tech_specs.value['system_requirements']), 0)
        self.assertGreater(len(tech_specs.value['supported_browsers']), 0)

    def test_get_performance_summary(self):
        """Test performance summary generation"""
        results = self.analyzer.analyze_performance_and_technical("https://test.com", self.sample_html)
        summary = self.analyzer.get_performance_summary(results)
        
        self.assertIn('overall_score', summary)
        self.assertIn('performance_grade', summary)
        self.assertIn('top_recommendations', summary)

@unittest.skipUnless(CACHE_AVAILABLE, "Redis dependencies not available")
class TestEnhancementCache(unittest.TestCase):
    """Test caching functionality"""

    def setUp(self):
        # Use in-memory cache for testing
        self.cache = EnhancementCache(redis_host="nonexistent")  # Will fallback to memory
        
    def test_cache_enhancement_result(self):
        """Test caching enhancement results"""
        test_data = {"name": "Test Tool", "description": "Test description"}
        
        success = self.cache.cache_enhancement_result("test_tool_1", test_data)
        self.assertTrue(success)
        
        # Retrieve cached data
        cached_data = self.cache.get_cached_enhancement("test_tool_1")
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data["name"], "Test Tool")

    def test_cache_website_data(self):
        """Test caching website data"""
        test_data = {"title": "Test Website", "content": "Test content"}
        
        success = self.cache.cache_website_data("https://test.com", test_data)
        self.assertTrue(success)
        
        # Retrieve cached data
        cached_data = self.cache.get_cached_website_data("https://test.com")
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data["title"], "Test Website")

    def test_cache_levels(self):
        """Test different cache levels"""
        test_data = {"test": "data"}
        
        # Test different cache levels
        for level in CacheLevel:
            success = self.cache.cache_enhancement_result(f"test_{level.value}", test_data, level)
            self.assertTrue(success)

    def test_batch_cache_enhancement_results(self):
        """Test batch caching"""
        results = [
            {"tool_id": "tool1", "data": {"name": "Tool 1"}},
            {"tool_id": "tool2", "data": {"name": "Tool 2"}},
            {"tool_id": "tool3", "data": {"name": "Tool 3"}}
        ]
        
        cached_count = self.cache.batch_cache_enhancement_results(results)
        self.assertEqual(cached_count, 3)
        
        # Verify all were cached
        for result in results:
            cached_data = self.cache.get_cached_enhancement(result["tool_id"])
            self.assertIsNotNone(cached_data)

    def test_get_cache_stats(self):
        """Test cache statistics"""
        stats = self.cache.get_cache_stats()
        self.assertIn('redis_connected', stats)
        self.assertFalse(stats['redis_connected'])  # Should be false for memory cache

@unittest.skipUnless(PARALLEL_AVAILABLE, "Parallel processing dependencies not available")
class TestParallelProcessingSystem(unittest.TestCase):
    """Test parallel processing functionality"""

    def setUp(self):
        self.processor = ParallelProcessingSystem(max_workers=4)
        
    def test_sequential_processing(self):
        """Test sequential processing mode"""
        def dummy_processor(task):
            time.sleep(0.1)  # Simulate work
            return {"processed": task.tool_name, "task_id": task.task_id}
        
        tasks = [
            ProcessingTask(f"task_{i}", f"tool_{i}", f"https://test{i}.com", {})
            for i in range(3)
        ]
        
        results = self.processor.process_batch(tasks, dummy_processor, ProcessingMode.SEQUENTIAL)
        
        self.assertEqual(len(results), 3)
        self.assertTrue(all(r.success for r in results))

    def test_threaded_processing(self):
        """Test threaded processing mode"""
        def dummy_processor(task):
            time.sleep(0.1)
            return {"processed": task.tool_name}
        
        tasks = [
            ProcessingTask(f"task_{i}", f"tool_{i}", f"https://test{i}.com", {})
            for i in range(5)
        ]
        
        start_time = time.time()
        results = self.processor.process_batch(tasks, dummy_processor, ProcessingMode.THREADED)
        end_time = time.time()
        
        self.assertEqual(len(results), 5)
        self.assertTrue(all(r.success for r in results))
        # Should be faster than sequential (5 * 0.1 = 0.5s)
        self.assertLess(end_time - start_time, 0.4)

    def test_error_handling_and_retries(self):
        """Test error handling and retry mechanism"""
        call_count = 0
        
        def failing_processor(task):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:  # Fail first 2 attempts
                raise Exception("Simulated failure")
            return {"success": True}
        
        task = ProcessingTask("retry_task", "retry_tool", "https://test.com", {}, max_retries=3)
        results = self.processor.process_batch([task], failing_processor, ProcessingMode.SEQUENTIAL)
        
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].success)
        self.assertEqual(results[0].retry_count, 2)

    def test_get_processing_stats(self):
        """Test processing statistics"""
        def dummy_processor(task):
            return {"processed": True}
        
        tasks = [ProcessingTask(f"task_{i}", f"tool_{i}", "https://test.com", {}) for i in range(3)]
        self.processor.process_batch(tasks, dummy_processor, ProcessingMode.SEQUENTIAL)
        
        stats = self.processor.get_processing_stats()
        self.assertEqual(stats['total_processed'], 3)
        self.assertEqual(stats['successful_processed'], 3)
        self.assertEqual(stats['success_rate'], 100.0)

    def test_optimize_batch_size(self):
        """Test batch size optimization"""
        # Simulate some processing history
        self.processor.stats['average_processing_time'] = 2.0
        
        optimal_size = self.processor.optimize_batch_size(100, target_time=300.0)
        self.assertGreater(optimal_size, 0)
        self.assertLessEqual(optimal_size, 100)

@unittest.skipUnless(MONITORING_AVAILABLE, "Performance monitoring dependencies not available")
class TestPerformanceMonitor(unittest.TestCase):
    """Test performance monitoring functionality"""

    def setUp(self):
        self.monitor = PerformanceMonitor()
        
    def test_record_metric(self):
        """Test metric recording"""
        self.monitor.record_metric("test_metric", 42.5, MetricType.GAUGE)
        
        self.assertIn("test_metric", self.monitor.current_metrics)
        self.assertEqual(self.monitor.current_metrics["test_metric"].value, 42.5)

    def test_timer_functionality(self):
        """Test timer functionality"""
        timer_id = self.monitor.start_timer("test_operation")
        time.sleep(0.1)
        duration = self.monitor.stop_timer(timer_id)
        
        self.assertGreater(duration, 0.05)
        self.assertLess(duration, 0.2)

    def test_time_function(self):
        """Test function timing"""
        def test_function(x, y):
            time.sleep(0.05)
            return x + y
        
        result, duration = self.monitor.time_function(test_function, 2, 3)
        
        self.assertEqual(result, 5)
        self.assertGreater(duration, 0.04)

    def test_alert_system(self):
        """Test alert system"""
        # Record a metric that should trigger an alert
        self.monitor.record_metric("cpu_usage_percent", 95.0)
        
        # Check if alert was created
        self.assertGreater(len(self.monitor.alerts), 0)
        alert = self.monitor.alerts[-1]
        self.assertEqual(alert.level, AlertLevel.CRITICAL)

    def test_get_metric_summary(self):
        """Test metric summary generation"""
        # Record multiple values
        for i in range(10):
            self.monitor.record_metric("test_summary", i * 10)
        
        summary = self.monitor.get_metric_summary("test_summary")
        
        self.assertEqual(summary['count'], 10)
        self.assertEqual(summary['min'], 0)
        self.assertEqual(summary['max'], 90)
        self.assertEqual(summary['mean'], 45)

    def test_optimization_recommendations(self):
        """Test optimization recommendations"""
        # Set up metrics that should trigger recommendations
        self.monitor.record_metric("memory_usage_percent", 85.0)
        self.monitor.record_metric("avg_processing_time", 20.0)
        
        recommendations = self.monitor.get_optimization_recommendations()
        
        self.assertGreater(len(recommendations), 0)
        self.assertTrue(any("memory" in rec['recommendation'].lower() for rec in recommendations))

    def test_performance_dashboard(self):
        """Test performance dashboard generation"""
        # Record some metrics
        self.monitor.record_metric("cpu_usage_percent", 45.0)
        self.monitor.record_metric("memory_usage_percent", 60.0)
        
        dashboard = self.monitor.get_performance_dashboard()
        
        self.assertIn('timestamp', dashboard)
        self.assertIn('system_metrics', dashboard)
        self.assertIn('alerts', dashboard)
        self.assertIn('recommendations', dashboard)

def run_comprehensive_phase3_tests():
    """Run all Phase 3 tests"""
    print("🧪 RUNNING COMPREHENSIVE PHASE 3 TESTS")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes (conditionally based on availability)
    test_classes = [
        TestStructuredDataExtractor,
        TestAdvancedContentAnalyzer,
        TestPerformanceTechnicalAnalyzer,
    ]

    if CACHE_AVAILABLE:
        test_classes.append(TestEnhancementCache)
    if PARALLEL_AVAILABLE:
        test_classes.append(TestParallelProcessingSystem)
    if MONITORING_AVAILABLE:
        test_classes.append(TestPerformanceMonitor)
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 PHASE 3 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ ALL TESTS PASSED!")
        print("🎉 Phase 3 implementation is working correctly!")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_phase3_tests()
    exit(0 if success else 1)
