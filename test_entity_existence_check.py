#!/usr/bin/env python3
"""
Test entity existence check to verify:
1. The API search works without using unsupported 'search' parameter
2. Existing entities are properly detected
3. No more unique constraint violations
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8001"

def test_entity_existence_check():
    """Test entity existence check with known entities"""
    
    # Test with entities that likely exist in the database
    test_tools = [
        {
            "name": "ChatGPT",  # This likely exists
            "url": "https://chat.openai.com"
        },
        {
            "name": "Gadget",  # This definitely exists based on error logs
            "url": "https://gadget.dev"
        },
        {
            "name": "Unique Test Tool " + str(int(time.time())),  # This should be unique
            "url": "https://example.com/unique-test-" + str(int(time.time()))
        }
    ]
    
    logger.info("🧪 Testing entity existence check...")
    
    # Send request to enhanced scraping endpoint
    response = requests.post(
        f"{BASE_URL}/api/start-enhanced-scraping",
        json={
            "tools": test_tools,
            "use_parallel": False,
            "use_phase3": True
        }
    )
    
    if response.status_code != 200:
        logger.error(f"❌ Failed to start enhanced scraping: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return None
    
    result = response.json()
    job_id = result.get('job_id')
    logger.info(f"✅ Enhanced scraping started with job ID: {job_id}")
    
    # Monitor job
    max_attempts = 40  # 3+ minutes max
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/api/job-status/{job_id}")
            
            if response.status_code != 200:
                logger.error(f"❌ Failed to get job status: {response.status_code}")
                break
            
            status = response.json()
            progress = status.get('progress', 0)
            state = status.get('status', 'unknown')
            
            logger.info(f"📈 Job {job_id}: {state} - {progress:.1f}% complete")
            
            if state in ['completed', 'failed']:
                logger.info(f"🏁 Job {job_id} finished with state: {state}")
                
                # Analyze results for entity existence check
                results = status.get('results', [])
                errors = status.get('errors', [])
                
                logger.info(f"📊 Results: {len(results)} items, {len(errors)} errors")
                
                # Check for entity existence detection
                entities_skipped = 0
                entities_created = 0
                constraint_errors = 0
                
                for result in results:
                    if isinstance(result, dict):
                        # Check if entity was saved successfully
                        if 'id' in result:
                            entities_created += 1
                            logger.info(f"✅ Successfully created: {result.get('name')} (ID: {result.get('id')})")
                        elif result.get('skipped') or 'already exists' in str(result).lower():
                            entities_skipped += 1
                            logger.info(f"⏭️  Skipped existing entity: {result.get('name', 'Unknown')}")
                
                # Check errors for constraint violations
                for error in errors:
                    error_str = str(error).lower()
                    if 'unique constraint' in error_str or 'duplicate' in error_str:
                        constraint_errors += 1
                        logger.error(f"❌ Constraint violation: {error}")
                
                # Summary
                logger.info("📋 ENTITY EXISTENCE CHECK SUMMARY:")
                logger.info(f"   ✅ Entities created: {entities_created}")
                logger.info(f"   ⏭️  Entities skipped (already exist): {entities_skipped}")
                logger.info(f"   ❌ Constraint violations: {constraint_errors}")
                
                if constraint_errors == 0:
                    logger.info("🎉 SUCCESS: No unique constraint violations!")
                    if entities_skipped > 0:
                        logger.info("🎯 EXCELLENT: Entity existence check is working!")
                    else:
                        logger.info("ℹ️  INFO: No existing entities detected (might be expected)")
                else:
                    logger.error("❌ FAILURE: Still getting constraint violations")
                
                return status
            
            time.sleep(5)  # Wait 5 seconds between checks
            attempt += 1
            
        except Exception as e:
            logger.error(f"❌ Error monitoring job: {str(e)}")
            break
    
    logger.warning(f"⏰ Job monitoring timed out")
    return None

def main():
    """Main test function"""
    logger.info("🧪 Starting Entity Existence Check Test")
    logger.info("=" * 60)
    
    final_status = test_entity_existence_check()
    
    if final_status:
        logger.info("✅ Test completed!")
    else:
        logger.error("❌ Test failed!")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
