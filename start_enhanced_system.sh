#!/bin/bash

# Start Enhanced AI Navigator Scrapers System
# This script starts both the enhanced backend and frontend

echo "🚀 Starting Enhanced AI Navigator Scrapers System"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "backend/server.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: backend/server.py"
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Check if backend port is already in use
if check_port 8001; then
    echo "⚠️  Port 8001 is already in use. Stopping existing backend..."
    pkill -f "server.py" 2>/dev/null || true
    sleep 2
fi

# Check if frontend port is already in use
if check_port 3000; then
    echo "⚠️  Port 3000 is already in use. Frontend may already be running."
fi

echo ""
echo "🔧 Starting Enhanced Backend Server..."
echo "   URL: http://localhost:8001"
echo "   Features: Phase 3 Enhanced Pipeline"

# Activate virtual environment for backend
echo "   📦 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "   📦 Installing/updating dependencies..."
pip install -r requirements.txt > /dev/null 2>&1

# Start backend in background
echo "   🚀 Launching backend server..."
python backend/server.py > backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to start with better detection
echo "   ⏳ Waiting for backend to start..."
for i in {1..15}; do
    sleep 1
    if check_port 8001; then
        echo "   ✅ Enhanced backend server started successfully (PID: $BACKEND_PID)"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "   ❌ Backend server failed to start after 15 seconds"
        echo "   🔍 Checking if process is still running..."
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo "   ⚠️  Backend process is running but port 8001 not responding"
            echo "   💡 This might be normal - continuing anyway..."
        else
            echo "   ❌ Backend process died - check for errors above"
            exit 1
        fi
    fi
done

echo ""
echo "🌐 Starting Frontend Development Server..."
echo "   URL: http://localhost:3000"
echo "   Features: Enhanced UI with Phase 3 Integration"

# Change to frontend directory and start
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "   📦 Installing frontend dependencies..."
    npm install
fi

# Start frontend
echo "   🚀 Starting React development server..."
npm start &
FRONTEND_PID=$!

# Wait for frontend to start
echo "   ⏳ Waiting for frontend to start..."
sleep 8  # React takes longer to start

echo ""
echo "🎉 Enhanced AI Navigator Scrapers System Started!"
echo "=================================================="
echo ""
echo "📊 System Status:"
echo "   ✅ Enhanced Backend: http://localhost:8001"
echo "   ✅ Frontend UI: http://localhost:3000"
echo ""
echo "🚀 Phase 3 Features Available:"
echo "   ✅ Structured Data Extraction"
echo "   ✅ Advanced Content Analysis"
echo "   ✅ Performance & Technical Analysis"
echo "   ✅ Parallel Processing (1.6x faster)"
echo "   ✅ Real-time Monitoring"
echo "   ✅ Performance Dashboard"
echo ""
echo "🎯 Next Steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Click on the 'Enhanced' tab"
echo "   3. Configure tools and start enhanced processing"
echo "   4. Monitor real-time progress and results"
echo ""
echo "⚠️  To stop the system:"
echo "   Press Ctrl+C or run: pkill -f 'server.py' && pkill -f 'react-scripts'"
echo ""

# Keep script running to show logs
echo "📋 System Logs (Press Ctrl+C to stop):"
echo "======================================="

# Wait for user to stop
wait
