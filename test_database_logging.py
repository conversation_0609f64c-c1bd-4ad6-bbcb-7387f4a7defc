#!/usr/bin/env python3
"""
Test script to debug database save failures with enhanced logging
"""

import logging
import sys
import os

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('database_debug.log')
    ]
)

logger = logging.getLogger(__name__)

def test_ai_navigator_client():
    """Test AI Navigator client with enhanced logging"""
    try:
        from ai_navigator_client import AINavigatorClient
        from config import config
        
        logger.info("🔧 Testing AI Navigator Client with enhanced logging...")
        
        # Initialize client
        client = AINavigatorClient()
        logger.info(f"Client initialized with base URL: {client.base_url}")
        
        # Test authentication
        logger.info("Testing authentication...")
        entity_types = client.get_entity_types()
        logger.info(f"Retrieved {len(entity_types)} entity types")
        
        # Get AI tool entity type ID
        ai_tool_entity_type_id = client.get_ai_tool_entity_type_id()
        logger.info(f"AI Tool entity type ID: {ai_tool_entity_type_id}")
        
        if not ai_tool_entity_type_id:
            logger.error("❌ Could not get AI tool entity type ID!")
            return False
        
        # Get actual categories and tags to use
        categories = client.get_categories()
        tags = client.get_tags()

        # Use first available category and tag
        category_id = categories[0]['id'] if categories else None
        tag_id = tags[0]['id'] if tags else None

        if not category_id or not tag_id:
            logger.error("❌ No categories or tags available in the system!")
            return False

        logger.info(f"Using category: {categories[0]['name']} ({category_id})")
        logger.info(f"Using tag: {tags[0]['name']} ({tag_id})")

        # Test entity creation with minimal valid data (use timestamp to avoid duplicates)
        import time
        timestamp = int(time.time())
        test_entity = {
            'name': f'Test Tool for Debugging {timestamp}',
            'entity_type_id': ai_tool_entity_type_id,
            'short_description': 'A test tool to debug database save issues',
            'description': 'This is a comprehensive test tool created to debug database save failures in the AI Navigator system.',
            'website_url': 'https://example.com/test-tool',
            'meta_title': 'Test Tool for Debugging | AI Navigator',
            'meta_description': 'A test tool to debug database save issues',
            'ref_link': 'https://example.com/test-tool',
            'affiliate_status': 'NONE',
            'status': 'PENDING',
            'category_ids': [category_id],  # FIXED: Include at least one category
            'tag_ids': [tag_id],           # FIXED: Include at least one tag
            'feature_ids': []
        }
        
        logger.info("🧪 Testing entity creation with minimal valid data...")
        logger.info(f"Test entity data: {test_entity}")
        
        result = client.create_entity(test_entity)
        
        if result:
            logger.info(f"✅ SUCCESS! Entity created with ID: {result.get('id')}")
            return True
        else:
            logger.error("❌ FAILED! Entity creation returned None")
            return False
            
    except Exception as e:
        logger.error(f"❌ Exception during testing: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_enhanced_pipeline():
    """Test the enhanced pipeline with a single tool"""
    try:
        from enhanced_scraper_pipeline_phase3 import EnhancedScraperPipelinePhase3
        from config import config
        
        logger.info("🔧 Testing Enhanced Pipeline with single tool...")
        
        # Initialize pipeline
        pipeline = EnhancedScraperPipelinePhase3(config.api.xai_api_key)
        
        # Test with a simple tool
        test_tools = [{'name': 'ChatGPT Test', 'url': 'https://chat.openai.com'}]
        
        logger.info("Processing single test tool...")
        results = pipeline.process_tools_enhanced(test_tools)
        
        logger.info(f"Pipeline results: {results}")
        
        if results and len(results) > 0:
            result = results[0]
            if result.get('success') and result.get('database_result', {}).get('success'):
                logger.info("✅ SUCCESS! Pipeline processed and saved tool successfully")
                return True
            else:
                logger.error(f"❌ FAILED! Pipeline result: {result}")
                return False
        else:
            logger.error("❌ FAILED! No results from pipeline")
            return False
            
    except Exception as e:
        logger.error(f"❌ Exception during pipeline testing: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Database Save Debugging Session...")
    logger.info("=" * 80)
    
    # Test 1: Direct AI Navigator Client
    logger.info("TEST 1: Direct AI Navigator Client")
    logger.info("-" * 40)
    client_success = test_ai_navigator_client()
    
    logger.info("\n" + "=" * 80)
    
    # Test 2: Enhanced Pipeline (only if client test passes)
    if client_success:
        logger.info("TEST 2: Enhanced Pipeline")
        logger.info("-" * 40)
        pipeline_success = test_enhanced_pipeline()
    else:
        logger.warning("⚠️ Skipping pipeline test due to client failure")
        pipeline_success = False
    
    logger.info("\n" + "=" * 80)
    logger.info("🏁 DEBUGGING SESSION COMPLETE")
    logger.info(f"Client Test: {'✅ PASS' if client_success else '❌ FAIL'}")
    logger.info(f"Pipeline Test: {'✅ PASS' if pipeline_success else '❌ FAIL'}")
    
    if not client_success:
        logger.error("🔍 Check the detailed logs above for specific API error messages")
        logger.error("🔍 Common issues: missing entity_type_id, invalid credentials, network connectivity")
    
    logger.info("📋 Full debug log saved to: database_debug.log")
