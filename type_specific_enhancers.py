"""
Type-Specific Data Enhancers - Specialized enhancement for each entity type
"""

import json
import requests
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

class BaseEntityEnhancer(ABC):
    """Base class for entity-specific enhancers"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.logger = logging.getLogger(__name__)
        
    @abstractmethod
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        """Get entity-type-specific enhancement prompt"""
        pass
        
    @abstractmethod 
    def get_expected_schema(self) -> Dict[str, Any]:
        """Get expected data schema for this entity type"""
        pass
        
    def enhance_entity(self, name: str, website_url: str, initial_data: str = "") -> Dict[str, Any]:
        """Enhance entity data using type-specific prompts"""
        
        prompt = self.get_enhancement_prompt(name, website_url, initial_data)
        
        try:
            response = requests.post(
                self.base_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "sonar",
                    "messages": [
                        {
                            "role": "system",
                            "content": f"You are an expert researcher specializing in {self.__class__.__name__.replace('Enhancer', '').lower()} data extraction. Always respond with valid JSON format. Provide comprehensive, accurate, and specific information."
                        },
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 4000
                },
                timeout=60
            )
            
            if response.status_code == 200:
                response_text = response.json()['choices'][0]['message']['content']
                
                # Extract JSON from response
                try:
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}') + 1
                    
                    if json_start != -1 and json_end > json_start:
                        json_str = response_text[json_start:json_end]
                        data = json.loads(json_str)
                        
                        self.logger.info(f"✅ Successfully enhanced {name} as {self.__class__.__name__}")
                        return data
                    else:
                        self.logger.warning(f"Could not find JSON in response for {name}")
                        return self._get_fallback_data(name, website_url)
                        
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Could not parse JSON for {name}: {e}")
                    return self._get_fallback_data(name, website_url)
            else:
                self.logger.error(f"API error for {name}: {response.status_code}")
                return self._get_fallback_data(name, website_url)
                
        except Exception as e:
            self.logger.error(f"Error enhancing {name}: {e}")
            return self._get_fallback_data(name, website_url)
    
    def _get_fallback_data(self, name: str, website_url: str) -> Dict[str, Any]:
        """Get basic fallback data if enhancement fails"""
        # Provide meaningful fallback data with features so feature extraction still works
        fallback_data = {
            "name": name,
            "website_url": website_url,
            "enhanced": False,
            "short_description": f"{name} - AI-powered automation platform",
            "description": f"{name} is an innovative AI-powered platform designed to enhance productivity and streamline workflows through intelligent automation capabilities.",
            "key_features": [
                "AI-Powered Automation",
                "User-Friendly Interface",
                "Real-time Processing",
                "API Integration",
                "Cloud-Based Platform"
            ],
            "use_cases": [
                "Business Process Automation",
                "Workflow Optimization",
                "Data Processing",
                "Task Management"
            ],
            "categories": ["Business & Productivity"],
            "tags": ["AI Automation", "Productivity", "Cloud-Based"],
            "pricing_model": "FREEMIUM",
            "has_free_tier": True,
            "price_range": "MEDIUM"
        }
        
        self.logger.info(f"Using fallback data with {len(fallback_data.get('key_features', []))} features for {name}")
        return fallback_data

class AIToolEnhancer(BaseEntityEnhancer):
    """Enhanced for AI Tools with comprehensive feature extraction"""
    
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        return f"""
        Research and provide COMPREHENSIVE information about the AI tool "{name}" (website: {website_url}).
        
        CRITICAL REQUIREMENTS:
        - Provide SPECIFIC, detailed use cases (NOT generic "General Use" or "Business Applications")
        - Create compelling descriptions (NOT just "{name} - AI Tool")
        - Extract maximum technical and business details
        
        Required JSON structure:
        {{
            "name": "Exact tool name",
            "short_description": "Compelling 150-200 char description - NO GENERIC descriptions!",
            "description": "Comprehensive 400-600 character detailed description with specific capabilities",
            "key_features": ["Specific Feature 1", "Specific Feature 2", "Specific Feature 3", "Specific Feature 4", "Specific Feature 5"],
            "use_cases": ["Specific Use Case 1", "Specific Use Case 2", "Specific Use Case 3", "Specific Use Case 4"],
            "categories": ["Primary Category", "Secondary Category"],
            "tags": ["Tag1", "Tag2", "Tag3", "Tag4", "Tag5"],
            "target_audience": ["Specific Audience 1", "Specific Audience 2", "Specific Audience 3"],
            "pricing_model": "FREE/FREEMIUM/SUBSCRIPTION/PAY_PER_USE/ONE_TIME_PURCHASE/CONTACT_SALES",
            "has_free_tier": true/false,
            "price_range": "FREE/LOW/MEDIUM/HIGH/ENTERPRISE",
            "pricing_details": "Specific pricing information with actual numbers if available",
            "pricing_url": "Direct pricing page URL if available",
            "learning_curve": "LOW/MEDIUM/HIGH",
            "trial_available": true/false,
            "demo_available": true/false,
            "api_access": true/false,
            "mobile_support": true/false,
            "open_source": true/false,
            "integrations": ["Integration 1", "Integration 2", "Integration 3"],
            "programming_languages": ["Language 1", "Language 2"],
            "frameworks": ["Framework 1", "Framework 2"],
            "deployment_options": ["Cloud", "On-premise", "Hybrid"],
            "supported_os": ["Windows", "macOS", "Linux", "Web"],
            "customization_level": "Low/Medium/High",
            "has_live_chat": true/false,
            "support_channels": ["Email", "Chat", "Phone", "Documentation"],
            "founded_year": 2020,
            "employee_count_range": "1-10/11-50/51-200/201-500/501-1000/1000+",
            "funding_stage": "Seed/Series A/Series B/Series C/Public",
            "location_summary": "City, Country or Remote",
            "social_links": {{
                "twitter": "actual_handle_only",
                "linkedin": "company_name_only", 
                "github": "username_only"
            }},
            "documentation_url": "Documentation URL if available",
            "contact_url": "Contact/Support URL if available",
            "privacy_policy_url": "Privacy policy URL if available",
            "review_sentiment_label": "Positive/Negative/Neutral",
            "review_sentiment_score": 0.85,
            "review_count": 150
        }}
        
        GUIDELINES:
        1. Use cases MUST be specific (e.g., "Automated Email Marketing Campaigns", "Real-time Customer Support Chat", "AI-Powered Content Optimization")
        2. NO generic use cases like "General Use", "Business Applications", "Productivity"
        3. Short description MUST be compelling and specific
        4. Social links should be CLEAN handles only, not full URLs or HTML
        5. Include pricing details with actual numbers when possible
        6. Focus on technical specifications and business intelligence
        """
    
    def get_expected_schema(self) -> Dict[str, Any]:
        return {
            "required_fields": ["name", "short_description", "key_features", "use_cases"],
            "optional_fields": ["pricing_model", "integrations", "target_audience"],
            "validation_rules": {
                "use_cases": "Must not contain generic terms",
                "short_description": "Must not contain 'AI Tool' generic phrase"
            }
        }

class ResearchPaperEnhancer(BaseEntityEnhancer):
    """Enhanced for Research Papers with academic-specific data"""
    
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        return f"""
        Research and provide COMPREHENSIVE academic information about the research paper "{name}" (URL: {website_url}).
        
        Required JSON structure:
        {{
            "title": "Complete research paper title",
            "authors": ["Author Name 1", "Author Name 2", "Author Name 3"],
            "abstract": "Complete abstract text (200-400 characters)",
            "description": "Comprehensive summary of the research including methodology and findings (400-600 characters)",
            "doi": "10.1000/xyz123",
            "arxiv_id": "2301.12345",
            "publication_date": "2023-03-15",
            "journal_or_conference": "Conference/Journal Name",
            "research_areas": ["Machine Learning", "Natural Language Processing", "Computer Vision"],
            "keywords": ["transformer", "attention mechanism", "neural network"],
            "citation_count": 150,
            "pdf_url": "Direct PDF URL if available",
            "publication_venues": ["NeurIPS", "ICML", "ICLR"],
            "methodology": "Brief description of research methodology",
            "key_findings": ["Finding 1", "Finding 2", "Finding 3"],
            "datasets_used": ["Dataset 1", "Dataset 2"],
            "code_repository": "GitHub repository URL if available",
            "impact_factor": 4.5,
            "institutional_affiliations": ["University 1", "Company 1"],
            "funding_sources": ["Grant Agency 1", "Foundation 1"],
            "related_papers": ["Related Paper 1", "Related Paper 2"]
        }}
        
        GUIDELINES:
        1. Extract complete academic information
        2. Focus on research methodology and findings
        3. Include all author information and institutional affiliations
        4. Provide comprehensive abstract and summary
        5. Include technical keywords and research areas
        """
    
    def get_expected_schema(self) -> Dict[str, Any]:
        return {
            "required_fields": ["title", "authors", "abstract", "research_areas"],
            "optional_fields": ["doi", "arxiv_id", "citation_count", "pdf_url"],
            "validation_rules": {
                "publication_date": "Must be valid date format",
                "citation_count": "Must be positive integer"
            }
        }

class HardwareEnhancer(BaseEntityEnhancer):
    """Enhanced for Hardware with technical specifications"""
    
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        return f"""
        Research and provide COMPREHENSIVE hardware information about "{name}" (website: {website_url}).
        
        Required JSON structure:
        {{
            "name": "Complete hardware name and model",
            "hardware_type": "GPU/CPU/Motherboard/Memory/Storage/Server/Workstation",
            "manufacturer": "Company name",
            "description": "Comprehensive description of hardware capabilities and use cases (400-600 characters)",
            "specifications": {{
                "memory": "24GB GDDR6X",
                "cores": 10496,
                "base_clock": "2230 MHz",
                "boost_clock": "2520 MHz", 
                "memory_bus": "384-bit",
                "power_consumption": "220W TGP",
                "architecture": "Ada Lovelace",
                "process_node": "4nm TSMC"
            }},
            "release_date": "2023-09-20",
            "msrp": "$799",
            "current_price": "$750-850",
            "availability": "In Stock/Limited/Out of Stock",
            "use_cases": ["Gaming at 4K", "AI Model Training", "3D Rendering", "Cryptocurrency Mining"],
            "supported_apis": ["DirectX 12", "Vulkan", "OpenGL"],
            "compatible_systems": ["Windows 11", "Linux", "macOS"],
            "power_requirements": "750W PSU recommended",
            "physical_dimensions": "Length x Width x Height in mm",
            "cooling_solution": "Triple-fan air cooling",
            "warranty": "3 years manufacturer warranty",
            "benchmark_scores": {{
                "3dmark_time_spy": 18500,
                "gaming_performance": "Excellent at 1440p",
                "ai_performance": "350 TOPS"
            }},
            "review_summary": "Overall reception and key strengths/weaknesses",
            "competitive_products": ["Competitor Product 1", "Competitor Product 2"]
        }}
        
        GUIDELINES:
        1. Focus on detailed technical specifications
        2. Include performance benchmarks and metrics
        3. Provide specific use case scenarios
        4. Include pricing and availability information
        5. Compare with competitive products
        """
    
    def get_expected_schema(self) -> Dict[str, Any]:
        return {
            "required_fields": ["name", "hardware_type", "manufacturer", "specifications"],
            "optional_fields": ["release_date", "msrp", "benchmark_scores"],
            "validation_rules": {
                "release_date": "Must be valid date",
                "msrp": "Must include currency symbol"
            }
        }

class JobEnhancer(BaseEntityEnhancer):
    """Enhanced for Job Listings with employment-specific data"""
    
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        return f"""
        Research and provide COMPREHENSIVE job listing information about "{name}" (website: {website_url}).
        
        Required JSON structure:
        {{
            "job_title": "Complete job title",
            "company_name": "Hiring company name",
            "description": "Comprehensive job description including role, responsibilities, and company info (400-600 characters)",
            "employment_types": ["FULL_TIME", "PART_TIME", "CONTRACT", "INTERN"],
            "experience_level": "ENTRY/MID/SENIOR/EXECUTIVE",
            "location": "City, State/Country",
            "location_types": ["Remote", "Hybrid", "On-site"],
            "salary_min": 80000,
            "salary_max": 120000,
            "salary_currency": "USD",
            "application_url": "Direct application URL",
            "is_remote": true/false,
            "job_type": "Software Engineering/Data Science/Product Management/Marketing/Sales",
            "key_responsibilities": [
                "Responsibility 1",
                "Responsibility 2", 
                "Responsibility 3"
            ],
            "required_skills": [
                "Python",
                "Machine Learning",
                "TensorFlow",
                "Docker"
            ],
            "preferred_qualifications": [
                "Master's degree in Computer Science",
                "5+ years experience",
                "Previous startup experience"
            ],
            "benefits": [
                "Health Insurance",
                "Stock Options", 
                "Remote Work",
                "Flexible Hours"
            ],
            "company_size": "Startup/Small/Medium/Large/Enterprise",
            "industry": "Technology/Healthcare/Finance/Education",
            "remote_policy": "Fully Remote/Hybrid/Office-based",
            "visa_sponsorship": true/false,
            "application_deadline": "2024-12-31",
            "posted_date": "2024-01-15",
            "contact_email": "<EMAIL>"
        }}
        
        GUIDELINES:
        1. Extract specific job requirements and qualifications
        2. Include detailed compensation and benefits information
        3. Provide company context and culture information
        4. Include application process details
        5. Specify remote work policies and location requirements
        """
    
    def get_expected_schema(self) -> Dict[str, Any]:
        return {
            "required_fields": ["job_title", "company_name", "employment_types", "location"],
            "optional_fields": ["salary_min", "salary_max", "benefits", "remote_policy"],
            "validation_rules": {
                "salary_min": "Must be positive integer",
                "salary_max": "Must be greater than salary_min"
            }
        }

class EventEnhancer(BaseEntityEnhancer):
    """Enhanced for Events with event-specific data"""
    
    def get_enhancement_prompt(self, name: str, website_url: str, initial_data: str = "") -> str:
        return f"""
        Research and provide COMPREHENSIVE event information about "{name}" (website: {website_url}).
        
        Required JSON structure:
        {{
            "event_name": "Complete event name",
            "event_type": "Conference/Workshop/Seminar/Webinar/Meetup/Summit",
            "description": "Comprehensive event description including topics, format, and value proposition (400-600 characters)",
            "start_date": "2024-09-15T09:00:00Z",
            "end_date": "2024-09-17T17:00:00Z",
            "location": "City, State/Country or 'Virtual'",
            "venue": "Specific venue name or platform",
            "is_online": true/false,
            "event_format": "In-person/Virtual/Hybrid",
            "registration_required": true/false,
            "registration_url": "Direct registration URL",
            "capacity": 500,
            "current_attendees": 350,
            "organizer": "Organization or person organizing",
            "key_speakers": [
                "Dr. AI Expert - Title/Company",
                "Jane Innovations - Title/Company",
                "Prof. Machine Learning - Title/Company"
            ],
            "agenda_topics": [
                "Keynote: Future of AI",
                "Workshop: Hands-on ML",
                "Panel: AI Ethics"
            ],
            "target_audience": [
                "AI Researchers",
                "Data Scientists", 
                "Software Engineers",
                "Business Leaders"
            ],
            "topics": [
                "Machine Learning",
                "Natural Language Processing",
                "Computer Vision"
            ],
            "ticket_price": "$99-299",
            "early_bird_price": "$79",
            "student_discount": "$49",
            "sponsors": ["Company 1", "Company 2"],
            "networking_opportunities": true/false,
            "recording_available": true/false,
            "certificate_provided": true/false,
            "contact_email": "<EMAIL>",
            "social_hashtag": "#EventHashtag"
        }}
        
        GUIDELINES:
        1. Include complete event logistics and scheduling
        2. Provide detailed speaker and agenda information
        3. Include pricing and registration details
        4. Specify target audience and learning outcomes
        5. Include networking and additional value propositions
        """
    
    def get_expected_schema(self) -> Dict[str, Any]:
        return {
            "required_fields": ["event_name", "event_type", "start_date", "location"],
            "optional_fields": ["speakers", "topics", "ticket_price", "capacity"],
            "validation_rules": {
                "start_date": "Must be valid ISO date format",
                "end_date": "Must be after start_date"
            }
        }

class TypeSpecificEnhancerFactory:
    """Factory class to create appropriate enhancer based on entity type"""
    
    @staticmethod
    def create_enhancer(entity_type: str, api_key: str) -> BaseEntityEnhancer:
        """Create appropriate enhancer based on entity type"""
        
        enhancer_map = {
            'ai-tool': AIToolEnhancer,
            'research-paper': ResearchPaperEnhancer,
            'hardware': HardwareEnhancer,
            'job': JobEnhancer,
            'event': EventEnhancer
        }
        
        enhancer_class = enhancer_map.get(entity_type, AIToolEnhancer)  # Default to AI Tool
        return enhancer_class(api_key)

    @staticmethod
    def get_supported_types() -> list:
        """Get list of supported entity types"""
        return ['ai-tool', 'research-paper', 'hardware', 'job', 'event']