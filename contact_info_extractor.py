"""
Contact Information Extraction Service
Comprehensive extraction of support emails, live chat detection, community URLs, and contact information
"""

import logging
import re
import requests
from typing import Dict, Optional, List, Set
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time

class ContactInfoExtractor:
    """Service for extracting comprehensive contact information from websites"""
    
    def __init__(self, timeout: int = 15):
        self.logger = logging.getLogger(__name__)
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Contact page patterns
        self.contact_page_patterns = [
            '/contact', '/contact-us', '/support', '/help', '/about/contact',
            '/get-in-touch', '/reach-out', '/connect', '/feedback', '/sales',
            '/customer-support', '/technical-support', '/contact-support'
        ]
        
        # Link text patterns for contact pages
        self.contact_link_texts = [
            'contact', 'contact us', 'get in touch', 'support', 'help',
            'reach out', 'feedback', 'sales', 'talk to us', 'customer support',
            'technical support', 'get help', 'need help'
        ]
        
        # Email patterns
        self.email_patterns = [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            r'mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})'
        ]
        
        # Live chat indicators
        self.live_chat_indicators = [
            # Common chat widget scripts
            'intercom', 'zendesk', 'drift', 'crisp', 'tawk.to', 'livechat',
            'freshchat', 'helpscout', 'olark', 'smartsupp', 'tidio',
            'chatra', 'userlike', 'liveperson', 'comm100', 'pure.chat',
            
            # Chat-related CSS classes and IDs
            'chat-widget', 'live-chat', 'chat-button', 'support-chat',
            'help-chat', 'customer-chat', 'chat-launcher', 'chat-bubble',
            
            # Chat-related text
            'live chat', 'chat with us', 'start chat', 'chat now',
            'online chat', 'chat support', 'live support'
        ]
        
        # Community platform patterns
        self.community_patterns = {
            'discord': [
                'discord.gg', 'discord.com/invite', 'discordapp.com'
            ],
            'slack': [
                'slack.com', 'join.slack.com', '.slack.com'
            ],
            'reddit': [
                'reddit.com/r/', 'www.reddit.com/r/'
            ],
            'github': [
                'github.com', 'github.io'
            ],
            'telegram': [
                't.me', 'telegram.me'
            ],
            'facebook': [
                'facebook.com', 'fb.com'
            ],
            'twitter': [
                'twitter.com', 'x.com'
            ],
            'linkedin': [
                'linkedin.com/company', 'linkedin.com/in'
            ]
        }
    
    def extract_contact_info(self, website_url: str, content: str = None) -> Dict[str, any]:
        """
        Extract comprehensive contact information
        
        Args:
            website_url: The website URL to extract contact info from
            content: Optional HTML content (if not provided, will fetch)
            
        Returns:
            Dict with contact information
        """
        
        self.logger.info(f"Extracting contact info for: {website_url}")
        
        # Get content if not provided
        if not content:
            content = self._fetch_website_content(website_url)
            if not content:
                return self._empty_result()
        
        contact_info = {}
        
        # Extract from main page first
        main_page_info = self._extract_from_page(website_url, content)
        contact_info.update(main_page_info)
        
        # Try to find and extract from dedicated contact page
        contact_page_url = self._find_contact_page(website_url, content)
        if contact_page_url and contact_page_url != website_url:
            contact_page_content = self._fetch_website_content(contact_page_url)
            if contact_page_content:
                contact_page_info = self._extract_from_page(contact_page_url, contact_page_content)
                # Merge results, prioritizing contact page data
                for key, value in contact_page_info.items():
                    if value and (not contact_info.get(key) or key == 'support_email'):
                        contact_info[key] = value
        
        self.logger.info(f"Contact info extracted: {contact_info}")
        return contact_info
    
    def _fetch_website_content(self, url: str) -> Optional[str]:
        """Fetch website content with error handling"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.text
        except Exception as e:
            self.logger.warning(f"Failed to fetch content from {url}: {str(e)}")
            return None
    
    def _extract_from_page(self, page_url: str, content: str) -> Dict[str, any]:
        """Extract contact information from a single page"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            info = {}
            
            # Extract support email
            support_email = self._extract_support_email(content, soup)
            if support_email:
                info['support_email'] = support_email
            
            # Detect live chat
            has_live_chat = self._detect_live_chat(content, soup)
            info['has_live_chat'] = has_live_chat
            
            # Extract community URLs
            community_url = self._extract_community_url(page_url, soup)
            if community_url:
                info['community_url'] = community_url
            
            # Extract social links
            social_links = self._extract_social_links(page_url, soup)
            if social_links:
                info['social_links'] = social_links
            
            return info
            
        except Exception as e:
            self.logger.error(f"Error extracting from page {page_url}: {str(e)}")
            return {}
    
    def _extract_support_email(self, content: str, soup: BeautifulSoup) -> Optional[str]:
        """Extract support email address"""
        try:
            # Look for emails in common support contexts
            support_emails = set()
            
            # Strategy 1: Look for emails in mailto links
            mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.I))
            for link in mailto_links:
                href = link.get('href', '')
                email_match = re.search(r'mailto:([^?&\s]+)', href, re.I)
                if email_match:
                    email = email_match.group(1).lower()
                    if self._is_support_email(email):
                        support_emails.add(email)
            
            # Strategy 2: Look for emails in text content
            for pattern in self.email_patterns:
                matches = re.findall(pattern, content, re.I)
                for match in matches:
                    email = match.lower() if isinstance(match, str) else match[0].lower()
                    if self._is_support_email(email):
                        support_emails.add(email)
            
            # Strategy 3: Look in contact sections specifically
            contact_sections = soup.find_all(['div', 'section'], 
                                           class_=re.compile(r'contact|support|help', re.I))
            for section in contact_sections:
                section_text = section.get_text()
                for pattern in self.email_patterns:
                    matches = re.findall(pattern, section_text, re.I)
                    for match in matches:
                        email = match.lower() if isinstance(match, str) else match[0].lower()
                        if self._is_support_email(email):
                            support_emails.add(email)
            
            # Return the most likely support email
            if support_emails:
                # Prioritize support-specific emails
                priority_emails = [email for email in support_emails 
                                 if any(keyword in email for keyword in ['support', 'help', 'contact'])]
                if priority_emails:
                    return priority_emails[0]
                return list(support_emails)[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting support email: {str(e)}")
            return None
    
    def _is_support_email(self, email: str) -> bool:
        """Check if email looks like a support email"""
        email_lower = email.lower()
        
        # Skip obviously non-support emails
        skip_patterns = [
            'noreply', 'no-reply', 'donotreply', 'do-not-reply',
            'marketing', 'newsletter', 'promo', 'sales@example',
            'test@', 'admin@localhost', 'user@example'
        ]
        
        if any(pattern in email_lower for pattern in skip_patterns):
            return False
        
        # Prefer support-related emails
        support_patterns = [
            'support', 'help', 'contact', 'info', 'hello', 'team',
            'customer', 'service', 'assistance', 'care'
        ]
        
        return any(pattern in email_lower for pattern in support_patterns)
    
    def _detect_live_chat(self, content: str, soup: BeautifulSoup) -> bool:
        """Detect if the website has live chat functionality"""
        try:
            content_lower = content.lower()
            
            # Check for live chat indicators in content
            for indicator in self.live_chat_indicators:
                if indicator in content_lower:
                    return True
            
            # Check for chat-related elements
            chat_elements = soup.find_all(['div', 'button', 'a'], 
                                        class_=re.compile(r'chat|support', re.I))
            for element in chat_elements:
                element_text = element.get_text().lower()
                if any(phrase in element_text for phrase in ['live chat', 'chat with us', 'start chat']):
                    return True
            
            # Check for common chat widget scripts
            scripts = soup.find_all('script')
            for script in scripts:
                script_content = script.get_text() if script.string else ''
                script_src = script.get('src', '')
                
                for indicator in self.live_chat_indicators[:10]:  # Check main chat services
                    if indicator in script_content.lower() or indicator in script_src.lower():
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting live chat: {str(e)}")
            return False
    
    def _extract_community_url(self, website_url: str, soup: BeautifulSoup) -> Optional[str]:
        """Extract community URL (Discord, Slack, Reddit, etc.)"""
        try:
            # Look for community links
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link.get('href', '')
                full_url = urljoin(website_url, href)
                
                # Check against community patterns
                for platform, patterns in self.community_patterns.items():
                    for pattern in patterns:
                        if pattern in full_url.lower():
                            # Prioritize Discord and Slack as they're most common for support
                            if platform in ['discord', 'slack']:
                                return full_url
                            # Store other community links but keep looking for Discord/Slack
                            elif not hasattr(self, '_temp_community_url'):
                                self._temp_community_url = full_url
            
            # Return stored community URL if no Discord/Slack found
            if hasattr(self, '_temp_community_url'):
                url = self._temp_community_url
                delattr(self, '_temp_community_url')
                return url
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting community URL: {str(e)}")
            return None
    
    def _extract_social_links(self, website_url: str, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract social media links"""
        try:
            social_links = {}
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link.get('href', '')
                full_url = urljoin(website_url, href)
                
                # Check against social patterns
                for platform, patterns in self.community_patterns.items():
                    if platform not in social_links:  # Only take first occurrence
                        for pattern in patterns:
                            if pattern in full_url.lower():
                                social_links[platform] = full_url
                                break
            
            return social_links
            
        except Exception as e:
            self.logger.error(f"Error extracting social links: {str(e)}")
            return {}
    
    def _find_contact_page(self, website_url: str, content: str) -> Optional[str]:
        """Find dedicated contact page URL"""
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # Strategy 1: Look for contact page patterns in URLs
            links = soup.find_all('a', href=True)
            for link in links:
                href = link.get('href', '').lower()
                for pattern in self.contact_page_patterns:
                    if pattern in href:
                        return urljoin(website_url, link['href'])
            
            # Strategy 2: Look for contact link text
            for link in links:
                link_text = link.get_text().lower().strip()
                for text_pattern in self.contact_link_texts:
                    if text_pattern in link_text:
                        return urljoin(website_url, link['href'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding contact page: {str(e)}")
            return None
    
    def _empty_result(self) -> Dict[str, any]:
        """Return empty contact info result"""
        return {
            'support_email': None,
            'has_live_chat': False,
            'community_url': None,
            'social_links': {}
        }
