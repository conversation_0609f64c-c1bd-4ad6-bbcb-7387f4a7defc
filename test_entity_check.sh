#!/bin/bash

echo "🧪 Testing Entity Existence Check"
echo "================================="

# Test with a tool that likely exists (Gadget)
echo "📝 Testing with 'Gadget' (should exist based on error logs)..."

curl -X POST http://localhost:8001/api/start-enhanced-scraping \
  -H "Content-Type: application/json" \
  -d '{
    "tools": [
      {
        "name": "Gadget",
        "url": "https://gadget.dev"
      }
    ],
    "use_parallel": false,
    "use_phase3": true
  }' \
  -s | python3 -m json.tool

echo ""
echo "✅ Test request sent. Check server logs for entity existence check behavior."
echo "Expected: Should detect existing 'Gadget' entity and skip processing."
