import requests
import json
import time

API_BASE_URL = "http://127.0.0.1:8002"

def run_test():
    """Run a targeted test for the enhanced item processor fix."""
    
    test_tool = {
        "name": "Test Tool for Fix",
        "url": "https://www.google.com" 
    }
    
    print("--- Starting Test: Enhanced Item Processor Fix ---")
    
    # 1. Start the enhanced scraping job
    print(f"Submitting one tool for processing: {test_tool['name']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/start-enhanced-scraping",
            json={
                "tools": [test_tool],
                "use_parallel": False,
                "use_phase3": True
            }
        )
        response.raise_for_status()
        
        job_data = response.json()
        if not job_data.get("success"):
            print(f"❌ ERROR: Failed to start job. Response: {job_data}")
            return

        job_id = job_data.get("job_id")
        print(f"✅ Job started successfully. Job ID: {job_id}")

        # 2. Poll for job completion
        print("Polling for job status...")
        timeout = 120  # 2-minute timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                status_response = requests.get(f"{API_BASE_URL}/api/job-status/{job_id}")
                status_data = status_response.json()
                
                status = status_data.get("status")
                progress = status_data.get("progress", 0)
                
                print(f"   Status: {status} | Progress: {progress:.1f}%")

                if status == "completed":
                    print("\n--- TEST RESULT ---")
                    print("✅ Job completed successfully!")
                    
                    results = status_data.get("results", {})
                    print("\nJob Results:")
                    print(json.dumps(results, indent=2))
                    
                    if results.get("database_save_rate", 0) > 0:
                        print("\n✅ SUCCESS: Tool was processed and likely saved to the database.")
                    else:
                        print("\n❌ FAILURE: Job completed, but tool was not saved to the database.")
                    return
                    
                elif status == "failed":
                    print("\n--- TEST RESULT ---")
                    print(f"❌ FAILURE: Job failed. Error: {status_data.get('error')}")
                    return
            
            except requests.RequestException as e:
                print(f"   Polling error: {e}")

            time.sleep(5)
            
        print("\n--- TEST RESULT ---")
        print("❌ FAILURE: Test timed out. Job did not complete in 120 seconds.")

    except requests.RequestException as e:
        print(f"❌ ERROR: Could not connect to the API: {e}")
        print("   Please ensure the backend server is running on port 8002.")

if __name__ == "__main__":
    run_test() 