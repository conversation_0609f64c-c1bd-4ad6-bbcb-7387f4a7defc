#!/usr/bin/env python3
"""
End-to-End API Testing Script
Tests the complete workflow through the backend API to verify all fixes work
"""

import requests
import json
import time
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('e2e_test.log')
    ]
)

logger = logging.getLogger(__name__)

# API Configuration
BACKEND_URL = "http://localhost:8001"

def test_backend_status():
    """Test if backend is running and responsive"""
    try:
        response = requests.get(f"{BACKEND_URL}/api/performance-dashboard", timeout=10)
        if response.status_code == 200:
            logger.info("✅ Backend is running and responsive")
            return True
        else:
            logger.error(f"❌ Backend returned status {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Backend connection failed: {e}")
        return False

def test_enhanced_processing():
    """Test the enhanced processing pipeline with real tools"""
    logger.info("🧪 Testing Enhanced Processing Pipeline...")
    
    # Test tools with different characteristics
    test_tools = [
        {"name": "Notion AI Test", "url": "https://notion.so"},
        {"name": "Midjourney Test", "url": "https://midjourney.com"},
        {"name": "Claude Test", "url": "https://claude.ai"}
    ]
    
    try:
        # Start enhanced processing job
        response = requests.post(
            f"{BACKEND_URL}/api/start-enhanced-scraping",
            json={
                "tools": test_tools,
                "use_parallel": False,  # Use sequential for easier debugging
                "use_phase3": True
            },
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start enhanced processing: {response.status_code} - {response.text}")
            return False
        
        job_data = response.json()
        job_id = job_data.get('job_id')
        logger.info(f"✅ Enhanced processing job started: {job_id}")
        
        # Monitor job progress
        max_wait_time = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                status_response = requests.get(f"{BACKEND_URL}/api/job-status/{job_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    logger.info(f"📊 Job Status: {status_data.get('status', 'Unknown')}")
                    logger.info(f"📈 Progress: {status_data.get('progress', {}).get('completed', 0)}/{status_data.get('progress', {}).get('total', 0)}")
                    
                    if status_data.get('status') == 'completed':
                        logger.info("🎉 Enhanced processing completed!")
                        
                        # Get detailed results
                        results = status_data.get('results', {})
                        successful_results = results.get('successful_results', [])
                        failed_results = results.get('failed_results', [])
                        
                        logger.info(f"✅ Successful: {len(successful_results)}")
                        logger.info(f"❌ Failed: {len(failed_results)}")
                        
                        # Analyze results quality
                        analyze_results_quality(successful_results)
                        
                        return len(successful_results) > 0
                    
                    elif status_data.get('status') == 'failed':
                        logger.error(f"❌ Job failed: {status_data.get('error', 'Unknown error')}")
                        return False
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Error checking job status: {e}")
                time.sleep(5)
        
        logger.error("❌ Job timed out")
        return False
        
    except Exception as e:
        logger.error(f"❌ Enhanced processing test failed: {e}")
        return False

def analyze_results_quality(successful_results):
    """Analyze the quality of enhanced results"""
    logger.info("\n" + "="*60)
    logger.info("📊 RESULTS QUALITY ANALYSIS")
    logger.info("="*60)
    
    for i, result in enumerate(successful_results, 1):
        data = result.get('data', {})
        enhancement_data = data.get('enhancement_data', {})
        database_result = data.get('database_result', {})
        
        tool_name = enhancement_data.get('name', f'Tool {i}')
        logger.info(f"\n🔍 TOOL {i}: {tool_name}")
        logger.info("-" * 40)
        
        # Check database save status
        db_success = database_result.get('success', False)
        logger.info(f"💾 Database Save: {'✅ SUCCESS' if db_success else '❌ FAILED'}")
        
        if not db_success:
            db_error = database_result.get('error', 'Unknown error')
            logger.info(f"   Error: {db_error}")
        
        # Analyze content quality
        description = enhancement_data.get('description', '')
        short_desc = enhancement_data.get('short_description', '')
        features = enhancement_data.get('key_features', [])
        use_cases = enhancement_data.get('use_cases', [])
        categories = enhancement_data.get('categories', [])
        tags = enhancement_data.get('tags', [])
        
        logger.info(f"📝 Description Length: {len(description)} chars")
        logger.info(f"🔧 Features Count: {len(features)}")
        logger.info(f"💼 Use Cases Count: {len(use_cases)}")
        logger.info(f"🏷️  Categories: {categories}")
        logger.info(f"🏷️  Tags: {tags}")
        
        # Quality scoring
        quality_score = 0
        max_score = 100
        
        # Description quality (30 points)
        if len(description) > 300:
            quality_score += 15
        if len(description) > 500:
            quality_score += 10
        if not any(generic in description.lower() for generic in ['ai-powered tool', 'enhance productivity']):
            quality_score += 5
        
        # Features quality (25 points)
        if len(features) >= 5:
            quality_score += 15
        if any(len(f) > 30 for f in features):
            quality_score += 10
        
        # Use cases quality (25 points)
        if len(use_cases) >= 3:
            quality_score += 15
        if any(len(uc) > 40 for uc in use_cases):
            quality_score += 10
        
        # Taxonomy quality (20 points)
        if len(categories) >= 1:
            quality_score += 10
        if len(tags) >= 3:
            quality_score += 10
        
        quality_percentage = (quality_score / max_score) * 100
        logger.info(f"📈 Quality Score: {quality_percentage:.1f}%")
        
        if quality_percentage >= 80:
            logger.info("🎉 EXCELLENT quality!")
        elif quality_percentage >= 60:
            logger.info("✅ GOOD quality")
        else:
            logger.info("⚠️  NEEDS IMPROVEMENT")

def test_traditional_scraping():
    """Test traditional scraping for comparison"""
    logger.info("🧪 Testing Traditional Scraping...")
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/start-scraping",
            json={
                "spider_name": "ai_tools_spider",
                "max_items": 5
            },
            timeout=30
        )
        
        if response.status_code == 200:
            job_data = response.json()
            job_id = job_data.get('job_id')
            logger.info(f"✅ Traditional scraping job started: {job_id}")
            return True
        else:
            logger.error(f"❌ Traditional scraping failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Traditional scraping test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive end-to-end testing"""
    logger.info("🚀 Starting Comprehensive End-to-End Testing")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Test 1: Backend Status
    logger.info("\n📡 TEST 1: Backend Status")
    test_results['backend_status'] = test_backend_status()
    
    if not test_results['backend_status']:
        logger.error("❌ Backend not available - cannot continue testing")
        return test_results
    
    # Test 2: Enhanced Processing (Main Test)
    logger.info("\n🚀 TEST 2: Enhanced Processing Pipeline")
    test_results['enhanced_processing'] = test_enhanced_processing()
    
    # Test 3: Traditional Scraping (For Comparison)
    logger.info("\n🕷️  TEST 3: Traditional Scraping")
    test_results['traditional_scraping'] = test_traditional_scraping()
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("🏁 COMPREHENSIVE TEST RESULTS")
    logger.info("=" * 80)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    logger.info(f"\n📊 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! System is ready for production!")
    elif test_results.get('enhanced_processing'):
        logger.info("✅ CORE FUNCTIONALITY WORKING! Enhanced processing pipeline is operational!")
    else:
        logger.error("❌ CRITICAL ISSUES DETECTED! Enhanced processing needs attention!")
    
    return test_results

if __name__ == "__main__":
    results = run_comprehensive_test()
    
    # Exit with appropriate code
    if results.get('enhanced_processing'):
        exit(0)  # Success
    else:
        exit(1)  # Failure
