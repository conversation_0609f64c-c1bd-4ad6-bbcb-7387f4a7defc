#!/usr/bin/env python3
"""
Debug script to test duplicate detection logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_item_processor import EnhancedItemProcessor
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_duplicate_detection():
    """Test the duplicate detection with known entities"""
    
    print("🔍 Testing Duplicate Detection Logic")
    print("=" * 50)
    
    try:
        # Initialize the processor
        client = AINavigatorClient()
        taxonomy_service = EnhancedTaxonomyService(client)
        processor = EnhancedItemProcessor(client, taxonomy_service)
        
        # Test cases - these should be found as duplicates
        test_cases = [
            {"name": "Gadget", "url": "https://futuretools.link/gadget-dev"},
            {"name": "Chronicle", "url": "https://futuretools.link/chroniclehq-com"},
            {"name": "Klarops", "url": "https://futuretools.link/klarops-com"},
            {"name": "Broadn", "url": "https://futuretools.link/broadn-io"},
            {"name": "Shiplo", "url": "https://futuretools.link/tryshiplo-com"},
        ]
        
        print(f"Testing {len(test_cases)} known entities...")
        print()
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            url = test_case["url"]
            
            print(f"🧪 Test {i}: {name}")
            print(f"   URL: {url}")
            
            # Test the duplicate detection
            existing_entity = processor._find_existing_entity(name, url)
            
            if existing_entity:
                print(f"   ✅ DUPLICATE DETECTED - Entity ID: {existing_entity.get('id')}")
                print(f"   💰 API credits would be SAVED")
            else:
                print(f"   ❌ NO DUPLICATE FOUND - Would proceed with enhancement")
                print(f"   💸 API credits would be WASTED")
            
            print()
        
        print("🏁 Duplicate detection test complete")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_duplicate_detection()
