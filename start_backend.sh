#!/bin/bash

# This script activates the Python virtual environment
# and starts the enhanced backend server.

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Define the virtual environment directory
VENV_DIR="$SCRIPT_DIR/venv"

# Check if the virtual environment exists
if [ ! -d "$VENV_DIR" ]; then
    echo "Virtual environment not found at $VENV_DIR."
    echo "Please create it first by running: python3 -m venv venv"
    exit 1
fi

# Activate the virtual environment
echo "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

# Verify that we are using the venv python
echo "Python interpreter: $(which python)"

# Install/update dependencies from the root requirements.txt
echo "Installing dependencies from requirements.txt..."
pip install -r "$SCRIPT_DIR/requirements.txt"

# Check if installation was successful
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies. Please check the error messages above."
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Navigate to the project directory
cd "$SCRIPT_DIR"

# Start the enhanced backend server
echo "🚀 Starting Enhanced Backend Server..."
echo "🌐 Server will be available at: http://localhost:8001"
echo "📱 Frontend should connect to: http://localhost:8001"
echo ""
echo "🔧 Features enabled:"
echo "   ✅ Traditional Scraping (with Max Items support)"
echo "   ✅ Enhanced AI Processing"
echo "   ✅ Automatic Taxonomy Mapping"
echo "   ✅ Real-time Job Monitoring"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=" * 50

python "$SCRIPT_DIR/backend/server.py"

# Deactivate the virtual environment on exit
deactivate
