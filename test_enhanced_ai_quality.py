#!/usr/bin/env python3
"""
Test script to verify enhanced AI data quality improvements
"""

import logging
import sys
import json

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('ai_quality_test.log')
    ]
)

logger = logging.getLogger(__name__)

def test_enhanced_ai_prompts():
    """Test the enhanced AI prompts with a variety of tools"""
    try:
        from enhanced_scraper_pipeline_phase3 import EnhancedScraperPipelinePhase3
        from config import config
        
        logger.info("🧪 Testing Enhanced AI Data Quality...")
        
        # Initialize pipeline
        pipeline = EnhancedScraperPipelinePhase3(config.api.xai_api_key)
        
        # Test with diverse tools to see quality improvements
        test_tools = [
            {'name': 'Notion AI', 'url': 'https://notion.so'},
            {'name': 'Midjourney', 'url': 'https://midjourney.com'},
            {'name': 'Tableau', 'url': 'https://tableau.com'},
        ]
        
        logger.info(f"Testing {len(test_tools)} tools for AI enhancement quality...")
        
        results = pipeline.process_tools_enhanced(test_tools)
        
        if results and results.get('successful_results'):
            logger.info("✅ SUCCESS! Analyzing AI enhancement quality...")
            
            for i, result in enumerate(results['successful_results']):
                tool_data = result.get('data', {})
                enhancement_data = tool_data.get('enhancement_data', {})
                
                logger.info(f"\n{'='*60}")
                logger.info(f"TOOL {i+1}: {enhancement_data.get('name', 'Unknown')}")
                logger.info(f"{'='*60}")
                
                # Analyze description quality
                description = enhancement_data.get('description', '')
                short_desc = enhancement_data.get('short_description', '')
                
                logger.info(f"📝 SHORT DESCRIPTION ({len(short_desc)} chars):")
                logger.info(f"   {short_desc}")
                
                logger.info(f"\n📖 FULL DESCRIPTION ({len(description)} chars):")
                logger.info(f"   {description}")
                
                # Analyze features quality
                features = enhancement_data.get('key_features', [])
                logger.info(f"\n🔧 KEY FEATURES ({len(features)} features):")
                for j, feature in enumerate(features, 1):
                    logger.info(f"   {j}. {feature}")
                
                # Analyze use cases quality
                use_cases = enhancement_data.get('use_cases', [])
                logger.info(f"\n💼 USE CASES ({len(use_cases)} use cases):")
                for j, use_case in enumerate(use_cases, 1):
                    logger.info(f"   {j}. {use_case}")
                
                # Analyze taxonomy
                categories = enhancement_data.get('categories', [])
                tags = enhancement_data.get('tags', [])
                logger.info(f"\n🏷️  TAXONOMY:")
                logger.info(f"   Categories: {categories}")
                logger.info(f"   Tags: {tags}")
                
                # Check for generic phrases (quality indicators)
                generic_phrases = [
                    'ai-powered tool', 'enhance productivity', 'improve workflow',
                    'user-friendly interface', 'business automation', 'personal productivity'
                ]
                
                full_text = f"{short_desc} {description} {' '.join(features)} {' '.join(use_cases)}".lower()
                found_generic = [phrase for phrase in generic_phrases if phrase in full_text]
                
                if found_generic:
                    logger.warning(f"⚠️  QUALITY ISSUE: Found generic phrases: {found_generic}")
                else:
                    logger.info("✅ QUALITY CHECK: No generic phrases detected")
                
                # Check specificity
                if len(description) > 300 and len(features) >= 5 and len(use_cases) >= 3:
                    logger.info("✅ COMPLETENESS: Good detail level")
                else:
                    logger.warning(f"⚠️  COMPLETENESS: May need more detail (desc: {len(description)}, features: {len(features)}, use_cases: {len(use_cases)})")
            
            return True
        else:
            logger.error("❌ FAILED! No successful results from pipeline")
            return False
            
    except Exception as e:
        logger.error(f"❌ Exception during AI quality testing: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def analyze_quality_metrics(results):
    """Analyze quality metrics from the results"""
    logger.info("\n" + "="*80)
    logger.info("📊 QUALITY ANALYSIS SUMMARY")
    logger.info("="*80)
    
    if not results or not results.get('successful_results'):
        logger.error("No results to analyze")
        return
    
    total_tools = len(results['successful_results'])
    quality_scores = []
    
    for result in results['successful_results']:
        enhancement_data = result.get('data', {}).get('enhancement_data', {})
        
        # Calculate quality score based on multiple factors
        score = 0
        max_score = 100
        
        # Description quality (30 points)
        description = enhancement_data.get('description', '')
        if len(description) > 300:
            score += 15
        if len(description) > 500:
            score += 10
        if 'specific' in description.lower() or 'unique' in description.lower():
            score += 5
        
        # Features quality (25 points)
        features = enhancement_data.get('key_features', [])
        if len(features) >= 5:
            score += 15
        if any(len(f) > 30 for f in features):  # Detailed features
            score += 10
        
        # Use cases quality (25 points)
        use_cases = enhancement_data.get('use_cases', [])
        if len(use_cases) >= 3:
            score += 15
        if any(len(uc) > 40 for uc in use_cases):  # Detailed use cases
            score += 10
        
        # Taxonomy quality (20 points)
        categories = enhancement_data.get('categories', [])
        tags = enhancement_data.get('tags', [])
        if len(categories) >= 1:
            score += 10
        if len(tags) >= 3:
            score += 10
        
        quality_percentage = (score / max_score) * 100
        quality_scores.append(quality_percentage)
        
        logger.info(f"Tool: {enhancement_data.get('name', 'Unknown')} - Quality Score: {quality_percentage:.1f}%")
    
    avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
    logger.info(f"\n📈 AVERAGE QUALITY SCORE: {avg_quality:.1f}%")
    
    if avg_quality >= 80:
        logger.info("🎉 EXCELLENT: AI enhancement quality is very high!")
    elif avg_quality >= 60:
        logger.info("✅ GOOD: AI enhancement quality is acceptable")
    else:
        logger.warning("⚠️  NEEDS IMPROVEMENT: AI enhancement quality could be better")

if __name__ == "__main__":
    logger.info("🚀 Starting AI Data Quality Testing...")
    logger.info("=" * 80)
    
    success = test_enhanced_ai_prompts()
    
    logger.info("\n" + "=" * 80)
    logger.info("🏁 AI QUALITY TESTING COMPLETE")
    logger.info(f"Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    logger.info("📋 Full test log saved to: ai_quality_test.log")
