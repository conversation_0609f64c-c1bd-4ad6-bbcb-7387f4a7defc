"""
Test script for Technical Level Classifier
Tests the classification accuracy with sample tools
"""

import sys
sys.path.append('/app')

from technical_level_classifier import TechnicalLevelClassifier
import j<PERSON>

def test_technical_classifier():
    """Test the technical level classifier with sample tools"""
    
    print("🧪 TESTING TECHNICAL LEVEL CLASSIFIER")
    print("=" * 50)
    
    # Initialize classifier with working API key
    classifier = TechnicalLevelClassifier('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')
    
    # Test cases with expected classifications
    test_cases = [
        {
            "name": "Canva",
            "description": "Drag-and-drop design tool for creating graphics, presentations, and social media posts",
            "key_features": ["drag-and-drop interface", "templates", "one-click design", "no design skills needed"],
            "use_cases": ["social media graphics", "presentations", "marketing materials"],
            "target_audience": ["small businesses", "marketers", "non-designers"],
            "expected": "BEGINNER"
        },
        {
            "name": "Zapier",
            "description": "Automation platform that connects apps and services through workflows",
            "key_features": ["API integrations", "workflow automation", "webhook support", "app connections"],
            "use_cases": ["business automation", "data synchronization", "workflow optimization"],
            "target_audience": ["business users", "operations teams", "small businesses"],
            "expected": "INTERMEDIATE"
        },
        {
            "name": "TensorFlow",
            "description": "Open-source machine learning framework for building and training neural networks",
            "key_features": ["neural networks", "model training", "deep learning", "custom algorithms", "research-grade"],
            "use_cases": ["machine learning research", "AI model development", "deep learning projects"],
            "target_audience": ["data scientists", "ML engineers", "researchers"],
            "expected": "EXPERT"
        },
        {
            "name": "GitHub Copilot",
            "description": "AI-powered code completion tool that suggests code as you type",
            "key_features": ["code completion", "programming assistance", "IDE integration", "multiple languages"],
            "use_cases": ["software development", "code writing", "programming productivity"],
            "target_audience": ["developers", "programmers", "software engineers"],
            "expected": "ADVANCED"
        }
    ]
    
    correct_predictions = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['name']}")
        print(f"   Expected: {test_case['expected']}")
        
        # Classify
        classification = classifier.classify_technical_level(test_case)
        confidence = classifier.get_classification_confidence(test_case, classification)
        
        print(f"   Predicted: {classification} (confidence: {confidence:.2f})")
        
        # Check if correct
        is_correct = classification == test_case['expected']
        if is_correct:
            correct_predictions += 1
            print("   ✅ CORRECT")
        else:
            print("   ❌ INCORRECT")
    
    # Calculate accuracy
    accuracy = (correct_predictions / total_tests) * 100
    print(f"\n📊 RESULTS:")
    print(f"   Correct: {correct_predictions}/{total_tests}")
    print(f"   Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 85:
        print("   🎉 TARGET ACCURACY ACHIEVED (85%+)")
    else:
        print("   ⚠️  Below target accuracy - needs improvement")
    
    return accuracy >= 85

if __name__ == "__main__":
    test_technical_classifier()
