"""
Manual Tool Processing Script
Process 10 tools from FutureTools with enhanced taxonomy and basic enrichment
"""

import sys
sys.path.append('/app')

import json
import logging
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
from entity_type_detector import EntityTypeDetector
from advanced_data_extractor import AdvancedDataExtractor
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import re

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualToolProcessor:
    def __init__(self):
        self.client = AINavigatorClient()
        self.taxonomy_service = EnhancedTaxonomyService(self.client)
        self.entity_detector = EntityTypeDetector()
        self.data_extractor = AdvancedDataExtractor()
        
    def get_entity_type_id(self, entity_type_slug):
        """Get entity type ID dynamically from API"""
        try:
            # Make a request to get entity types
            response = requests.get(
                f"{self.client.base_url}/entity-types",
                headers=self.client._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entity_types = response.json()
                for et in entity_types:
                    if et.get('slug') == entity_type_slug:
                        return et.get('id')
            
            logger.warning(f"Could not find entity type ID for: {entity_type_slug}")
            return None
                        
        except Exception as e:
            logger.error(f"Error getting entity type ID: {str(e)}")
            return None

    def resolve_redirect_url(self, url):
        """Resolve redirect URLs to get the actual website"""
        try:
            response = requests.head(url, allow_redirects=True, timeout=10)
            return response.url
        except:
            return url

    def extract_basic_website_data(self, url):
        """Extract basic data from website"""
        try:
            response = requests.get(url, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract title
                title = soup.find('title')
                title_text = title.get_text().strip() if title else ""
                
                # Extract meta description
                meta_desc = soup.find('meta', attrs={'name': 'description'})
                description = meta_desc.get('content', '').strip() if meta_desc else ""
                
                return {
                    'title': title_text,
                    'description': description,
                    'scraped_content': response.text[:1000]  # First 1000 chars
                }
        except Exception as e:
            logger.warning(f"Could not extract website data from {url}: {str(e)}")
            return {}

    def create_basic_enhanced_data(self, tool_name, website_url, website_data):
        """Create basic enhanced data without Perplexity"""
        
        # Extract clean tool name
        clean_name = re.sub(r'[\s\-_]+', ' ', tool_name).strip()
        
        # Create basic description
        title = website_data.get('title', '')
        meta_desc = website_data.get('description', '')
        
        description = meta_desc if meta_desc else f"{clean_name} is an AI-powered tool"
        if len(description) < 100 and title:
            description = f"{description}. {title}"
        
        # Basic categorization based on name and URL patterns
        categories = []
        tags = []
        key_features = []
        
        name_lower = tool_name.lower()
        url_lower = website_url.lower()
        
        # Categorize based on keywords
        if any(word in name_lower for word in ['chat', 'assistant', 'gpt']):
            categories.append('AI Assistants')
            tags.extend(['Conversational AI', 'AI Assistant'])
            key_features.extend(['Natural Language Processing', 'Chat Interface'])
        elif any(word in name_lower for word in ['image', 'photo', 'visual']):
            categories.append('Computer Vision')
            tags.extend(['Image Processing', 'Visual AI'])
            key_features.extend(['Image Analysis', 'Visual Recognition'])
        elif any(word in name_lower for word in ['content', 'write', 'text']):
            categories.append('Content Creation')
            tags.extend(['Content Generation', 'Writing Assistant'])
            key_features.extend(['Text Generation', 'Content Creation'])
        elif any(word in name_lower for word in ['data', 'analytics', 'insight']):
            categories.append('Analytics')
            tags.extend(['Data Analysis', 'Business Intelligence'])
            key_features.extend(['Data Processing', 'Analytics Dashboard'])
        else:
            categories.append('Business & Productivity')
            tags.extend(['AI-Powered', 'Productivity'])
            key_features.extend(['Automation', 'Efficiency'])
        
        # Add common features
        key_features.extend(['User-Friendly Interface', 'Cloud-Based'])
        tags.extend(['AI', 'Software'])
        
        return {
            'short_description': description[:150],
            'description': description,
            'key_features': key_features,
            'use_cases': ['Business Applications', 'Productivity Enhancement'],
            'categories': categories,
            'tags': tags,
            'pricing_model': 'FREEMIUM',
            'has_free_tier': True,
            'price_range': 'MEDIUM',
            'target_audience': ['Business Professionals', 'Individuals'],
            'founded_year': 2023,
            'employee_count_range': 'C11_50'
        }

    def process_tool(self, tool_data):
        """Process a single tool"""
        tool_name = tool_data.get('tool_name_on_directory', '')
        external_url = tool_data.get('external_website_url', '')
        
        logger.info(f"🚀 Processing: {tool_name}")
        
        # Step 1: Resolve URL
        actual_url = self.resolve_redirect_url(external_url)
        logger.info(f"  📍 Resolved URL: {actual_url}")
        
        # Step 2: Detect entity type
        entity_type = self.entity_detector.detect_entity_type(tool_name, actual_url)
        logger.info(f"  🎯 Detected type: {entity_type}")
        
        # Step 3: Get entity type ID
        entity_type_id = self.get_entity_type_id(entity_type)
        if not entity_type_id:
            logger.error(f"  ❌ Could not get entity type ID for {entity_type}")
            return None
        
        # Step 4: Extract website data
        website_data = self.extract_basic_website_data(actual_url)
        
        # Step 5: Create enhanced data
        enhanced_data = self.create_basic_enhanced_data(tool_name, actual_url, website_data)
        
        # Step 6: Map taxonomy
        category_ids = self.taxonomy_service.map_categories(enhanced_data.get('categories', []))
        tag_ids = self.taxonomy_service.map_tags(enhanced_data.get('tags', []))
        feature_ids = self.taxonomy_service.map_features(enhanced_data.get('key_features', []))
        
        logger.info(f"  🏷️ Mapped: {len(category_ids)} categories, {len(tag_ids)} tags, {len(feature_ids)} features")
        
        # Step 7: Extract social links and logo
        social_links = {}
        logo_url = None
        
        try:
            social_links = self.data_extractor.extract_clean_social_links(website_data.get('scraped_content', ''))
            logo_url = self.data_extractor.extract_logo_url(actual_url, website_data.get('scraped_content', ''))
        except Exception as e:
            logger.warning(f"  ⚠️ Error extracting social/logo data: {str(e)}")
        
        # Step 8: Build entity DTO
        entity_dto = {
            'name': tool_name,
            'website_url': actual_url,
            'entity_type_id': entity_type_id,
            'short_description': enhanced_data['short_description'],
            'description': enhanced_data['description'],
            'category_ids': category_ids,
            'tag_ids': tag_ids,
            'feature_ids': feature_ids,
            'meta_title': f"{tool_name} | AI Navigator",
            'meta_description': enhanced_data['short_description'],
            'ref_link': actual_url,
            'affiliate_status': 'NONE',
            'status': 'PENDING',
            'tool_details': {
                'learning_curve': 'MEDIUM',
                'key_features': enhanced_data['key_features'][:10],  # Limit to 10
                'has_free_tier': enhanced_data['has_free_tier'],
                'use_cases': enhanced_data['use_cases'],
                'pricing_model': enhanced_data['pricing_model'],
                'price_range': enhanced_data['price_range'],
                'target_audience': enhanced_data['target_audience'],
                'mobile_support': False,
                'api_access': False,
                'customization_level': 'Medium',
                'trial_available': False,
                'demo_available': False,
                'open_source': False,
                'support_channels': ['Email', 'Documentation']
            }
        }
        
        # Add social links if found
        if social_links:
            entity_dto['social_links'] = social_links
            
        # Add logo if found
        if logo_url:
            entity_dto['logo_url'] = logo_url
            
        # Add optional fields
        if enhanced_data.get('founded_year'):
            entity_dto['founded_year'] = enhanced_data['founded_year']
            
        if enhanced_data.get('employee_count_range'):
            entity_dto['employee_count_range'] = enhanced_data['employee_count_range']
        
        return entity_dto

    def submit_entity(self, entity_dto):
        """Submit entity to AI Navigator API"""
        try:
            result = self.client.create_entity(entity_dto)
            if result:
                logger.info(f"  ✅ Successfully created entity: {entity_dto['name']}")
                return True
            else:
                logger.error(f"  ❌ Failed to create entity: {entity_dto['name']}")
                return False
        except Exception as e:
            logger.error(f"  ❌ Error submitting entity: {str(e)}")
            return False

def main():
    processor = ManualToolProcessor()
    
    # Load tools from file
    tools_file = '/app/ai-navigator-scrapers/futuretools_leads.jsonl'
    tools = []
    
    with open(tools_file, 'r') as f:
        for line in f:
            tools.append(json.loads(line.strip()))
    
    logger.info(f"📊 Loaded {len(tools)} tools, processing first 10...")
    
    success_count = 0
    
    for i, tool in enumerate(tools[:10]):
        logger.info(f"\n{'='*60}")
        logger.info(f"Processing tool {i+1}/10")
        
        try:
            entity_dto = processor.process_tool(tool)
            if entity_dto:
                success = processor.submit_entity(entity_dto)
                if success:
                    success_count += 1
            else:
                logger.error(f"Failed to process tool: {tool.get('tool_name_on_directory', 'Unknown')}")
                
        except Exception as e:
            logger.error(f"Error processing tool: {str(e)}")
    
    logger.info(f"\n{'='*60}")
    logger.info(f"🎉 PROCESSING COMPLETE!")
    logger.info(f"✅ Successfully processed: {success_count}/10 tools")
    logger.info(f"📊 Success rate: {(success_count/10)*100:.1f}%")

if __name__ == "__main__":
    main()