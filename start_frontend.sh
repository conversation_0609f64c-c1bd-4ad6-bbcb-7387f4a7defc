#!/bin/bash

# Start Frontend for AI Navigator Scrapers
echo "🌐 Starting AI Navigator Scrapers Frontend..."

# Check if we're in the right directory
if [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected: Should contain 'frontend' directory"
    exit 1
fi

# Kill any existing process on port 3000
echo "🔄 Checking for existing processes on port 3000..."
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "   ⚠️  Port 3000 is in use. Stopping existing process..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    # Try different npm paths
    if command -v npm >/dev/null 2>&1; then
        npm install
    elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
        /Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm install
    else
        echo "❌ Error: npm not found. Please install Node.js and npm"
        exit 1
    fi
fi

echo "🚀 Starting React development server..."

# Try to start with different node/npm paths
if command -v npm >/dev/null 2>&1; then
    npm start
elif [ -f "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/npm" ]; then
    # Set up environment for nvm node
    export PATH="/Users/<USER>/.nvm/versions/node/v22.14.0/bin:$PATH"
    npm start
else
    echo "❌ Error: npm not found. Please install Node.js and npm"
    exit 1
fi
