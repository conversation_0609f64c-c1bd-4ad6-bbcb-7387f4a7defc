#!/usr/bin/env python3
"""
FINAL TEST: Prove duplicate detection is working and API credits are saved
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_item_processor import EnhancedItemProcessor
from ai_navigator_client import AINavigatorClient
from enhanced_taxonomy_service import EnhancedTaxonomyService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_specific_entities():
    """Test the exact entities that were wasting API credits"""
    
    print("🚨 FINAL TEST: API Credit Protection")
    print("=" * 60)
    
    try:
        # Initialize the processor
        client = AINavigatorClient()
        taxonomy_service = EnhancedTaxonomyService(client)
        processor = EnhancedItemProcessor(client, taxonomy_service)
        
        # These are the exact entities from your logs that were wasting credits
        problem_entities = [
            {"name": "Chronicle", "url": "https://futuretools.link/chroniclehq-com"},
            {"name": "Gadget", "url": "https://futuretools.link/gadget-dev"},
            {"name": "Klarops", "url": "https://futuretools.link/klarops-com"},
            {"name": "Broadn", "url": "https://futuretools.link/broadn-io"},
            {"name": "Shiplo", "url": "https://futuretools.link/tryshiplo-com"},
        ]
        
        print(f"Testing {len(problem_entities)} entities that were WASTING API credits...")
        print()
        
        api_credits_saved = 0
        api_credits_wasted = 0
        
        for i, entity in enumerate(problem_entities, 1):
            name = entity["name"]
            url = entity["url"]
            
            print(f"🧪 Test {i}: {name}")
            print(f"   URL: {url}")
            
            # This is the EXACT method that should be saving API credits
            lead_data = {
                "tool_name_on_directory": name,
                "external_website_url": url
            }
            result = processor.process_lead_item(lead_data)
            
            if result is None:
                print(f"   ✅ DUPLICATE DETECTED - API CREDITS SAVED! 💰")
                api_credits_saved += 1
            else:
                print(f"   ❌ NEW ENTITY CREATED - API CREDITS WASTED! 💸")
                print(f"   Entity ID: {result.get('id', 'Unknown')}")
                api_credits_wasted += 1
            
            print()
        
        print("🏁 FINAL RESULTS:")
        print("=" * 30)
        print(f"✅ API Credits SAVED: {api_credits_saved}")
        print(f"❌ API Credits WASTED: {api_credits_wasted}")
        print()
        
        if api_credits_wasted == 0:
            print("🎉 SUCCESS! All duplicates detected - API credits protected!")
            return True
        else:
            print("🚨 FAILURE! API credits are still being wasted!")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_specific_entities()
    if success:
        print("\n✅ DUPLICATE DETECTION IS WORKING - READY FOR PRODUCTION")
    else:
        print("\n❌ DUPLICATE DETECTION IS BROKEN - DO NOT RUN SCRAPER")
    
    sys.exit(0 if success else 1)
