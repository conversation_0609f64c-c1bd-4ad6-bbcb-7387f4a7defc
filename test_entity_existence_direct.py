#!/usr/bin/env python3
"""
Direct test of entity existence check functionality
Tests the improved _find_existing_entity method
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ai_navigator_client import AINavigatorClient
    from enhanced_item_processor import EnhancedItemProcessor
    from enhanced_taxonomy_service import EnhancedTaxonomyService
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are installed")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_entity_existence_check():
    """Test the entity existence check directly"""
    
    logger.info("🧪 Testing Entity Existence Check Directly")
    logger.info("=" * 50)
    
    try:
        # Initialize the client and processor
        logger.info("🔧 Initializing AI Navigator client...")
        client = AINavigatorClient()
        
        logger.info("🔧 Initializing taxonomy service...")
        taxonomy_service = EnhancedTaxonomyService(client)
        
        logger.info("🔧 Initializing enhanced item processor...")
        processor = EnhancedItemProcessor(client, taxonomy_service)
        
        # Test entities that likely exist based on error logs
        test_entities = [
            ("Gadget", "https://gadget.dev"),
            ("ChatGPT", "https://chat.openai.com"),
            ("TestTool_" + str(int(time.time())), "https://example.com/unique")  # This should be unique
        ]
        
        for name, url in test_entities:
            logger.info(f"🔍 Testing entity existence check for: {name}")
            
            # Call the _find_existing_entity method directly
            existing_entity = processor._find_existing_entity(name, url)
            
            if existing_entity:
                logger.info(f"   ✅ Found existing entity: {existing_entity.get('id')} (Name: '{existing_entity.get('name')}')")
                logger.info(f"   💰 This would save API credits by skipping AI enhancement")
            else:
                logger.info(f"   ✅ No existing entity found - would proceed with AI enhancement")
            
            logger.info("-" * 30)
        
        logger.info("🎉 Entity existence check test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during entity existence check test: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    import time
    success = test_entity_existence_check()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("The entity existence check is working properly.")
        print("This should prevent unique constraint violations.")
    else:
        print("\n❌ Test failed!")
        print("There may be issues with the entity existence check.")
