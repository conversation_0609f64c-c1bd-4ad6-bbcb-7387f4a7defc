#!/usr/bin/env python3

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append('.')

from ai_navigator_client import AINavigatorClient
from taxonomy_manager import TaxonomyManager

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_feature_creation():
    """Test feature creation specifically"""
    print("🧪 Testing Feature Creation")
    print("=" * 50)
    
    # Initialize client
    client = AINavigatorClient()
    
    # Test login
    if not client.login():
        print("❌ Failed to login to AI Navigator API")
        return False
    
    print("✅ Successfully logged in to AI Navigator API")
    
    # Initialize taxonomy manager
    taxonomy_manager = TaxonomyManager(
        base_url="https://ai-nav.onrender.com",
        auth_token=client.auth_token
    )
    
    # Test feature creation
    test_features = [
        "Real-time Data Processing",
        "Machine Learning Integration", 
        "API-First Architecture"
    ]
    
    print(f"\n🎯 Testing creation of {len(test_features)} features...")
    
    created_features = []
    for feature_name in test_features:
        print(f"\n📝 Creating feature: {feature_name}")
        try:
            feature_item = taxonomy_manager._create_feature(feature_name)
            if feature_item:
                created_features.append(feature_item)
                print(f"   ✅ Created: {feature_item.name} (UUID: {feature_item.uuid})")
            else:
                print(f"   ❌ Failed to create feature: {feature_name}")
        except Exception as e:
            print(f"   ❌ Error creating feature: {str(e)}")
    
    print(f"\n📊 Results:")
    print(f"   - Features attempted: {len(test_features)}")
    print(f"   - Features created: {len(created_features)}")
    print(f"   - Success rate: {len(created_features)/len(test_features)*100:.1f}%")
    
    if created_features:
        print(f"\n🎉 Successfully created features:")
        for feature in created_features:
            print(f"   - {feature.name} ({feature.uuid})")
        return True
    else:
        print(f"\n❌ No features were created successfully")
        return False

if __name__ == "__main__":
    success = test_feature_creation()
    sys.exit(0 if success else 1)