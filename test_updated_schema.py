"""
Test Updated Schema Integration
Verify that our updated scrapers work with the new advanced schema
"""

import json
import sys
sys.path.append('/app')

from enhanced_item_processor import EnhancedItemProcessor
from ai_navigator_client import AINavigatorClient
from data_enrichment_service import DataEnrichmentService
from taxonomy_service import TaxonomyService

def test_new_schema():
    """
    Test the updated system with new advanced schema
    """
    
    print("🧪 TESTING UPDATED SCHEMA INTEGRATION")
    print("=" * 60)
    
    # Initialize services
    print("🔧 Initializing updated services...")
    client = AINavigatorClient()
    enrichment = DataEnrichmentService('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')
    taxonomy = TaxonomyService(client)
    processor = EnhancedItemProcessor(client, enrichment, taxonomy)
    
    print("✅ Services initialized with updated schema support")
    
    # Test with a sample tool
    test_lead = {
        'tool_name_on_directory': 'Schema Test Tool',
        'external_website_url': 'https://example.com',
        'source_directory': 'test'
    }
    
    print(f"\n📝 Testing with: {test_lead['tool_name_on_directory']}")
    
    try:
        # Process with updated schema
        entity_dto = processor.process_lead_item(test_lead)
        
        if entity_dto:
            print("✅ Entity DTO created successfully with new schema!")
            
            # Check required fields
            required_fields = ['name', 'website_url', 'entity_type_id']
            for field in required_fields:
                if field in entity_dto:
                    print(f"   ✅ Required field '{field}': {entity_dto[field]}")
                else:
                    print(f"   ❌ Missing required field: {field}")
            
            # Check new schema fields
            new_fields = [
                'short_description', 'description', 'logo_url', 
                'meta_title', 'meta_description',
                'employee_count_range', 'funding_stage', 'location_summary',
                'affiliate_status', 'status',
                'scraped_review_sentiment_label', 'scraped_review_sentiment_score', 'scraped_review_count'
            ]
            
            print(f"\n📊 New Schema Fields Check:")
            for field in new_fields:
                value = entity_dto.get(field)
                if value is not None:
                    print(f"   ✅ {field}: {str(value)[:50]}...")
                else:
                    print(f"   ⚪ {field}: None")
            
            # Check tool_details structure
            tool_details = entity_dto.get('tool_details', {})
            if tool_details:
                print(f"\n🔧 Tool Details Fields ({len(tool_details)} fields):")
                for key, value in list(tool_details.items())[:10]:  # Show first 10
                    print(f"   • {key}: {str(value)[:50]}...")
                if len(tool_details) > 10:
                    print(f"   ... and {len(tool_details) - 10} more fields")
            
            # Check status field (should be PENDING)
            status = entity_dto.get('status')
            if status == 'PENDING':
                print(f"   ✅ Status correctly set to: {status}")
            else:
                print(f"   ⚠️  Status is: {status} (expected PENDING)")
            
            print(f"\n🎯 Schema Validation Summary:")
            print(f"   ✅ Entity structure: Valid")
            print(f"   ✅ Required fields: Present")
            print(f"   ✅ Tool details: {len(tool_details)} fields")
            print(f"   ✅ New schema: Compatible")
            
            return True
            
        else:
            print("❌ Entity DTO creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_data_enrichment():
    """
    Test the updated data enrichment with new fields
    """
    
    print(f"\n🔍 TESTING UPDATED DATA ENRICHMENT")
    print("=" * 50)
    
    enrichment = DataEnrichmentService('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')
    
    # Test enrichment
    result = enrichment.enrich_tool_data("Test AI Tool", "https://example.com", "A test AI tool")
    
    if result:
        print("✅ Data enrichment working with new schema")
        
        # Check for new fields
        new_enrichment_fields = [
            'programming_languages', 'frameworks', 'libraries',
            'deployment_options', 'supported_os',
            'has_live_chat', 'review_sentiment_label', 'review_sentiment_score'
        ]
        
        print(f"📊 New Enrichment Fields:")
        for field in new_enrichment_fields:
            if field in result:
                print(f"   ✅ {field}: {result[field]}")
            else:
                print(f"   ⚪ {field}: Not present")
        
        return True
    else:
        print("❌ Data enrichment failed")
        return False

def main():
    """
    Main test function
    """
    
    print("🚀 UPDATED SCHEMA INTEGRATION TEST")
    print("Testing all components with new advanced schema")
    print("\n🎯 Changes made:")
    print("   • Updated CreateEntityDto structure")
    print("   • Enhanced tool_details fields")
    print("   • Added review sentiment fields")
    print("   • Improved data enrichment prompts")
    print("   • Updated status to PENDING")
    
    # Run tests
    schema_test = test_new_schema()
    enrichment_test = test_data_enrichment()
    
    # Final results
    print(f"\n" + "=" * 60)
    print("📊 SCHEMA UPDATE TEST RESULTS")
    print("=" * 60)
    
    if schema_test and enrichment_test:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Schema integration successful")
        print("✅ Data enrichment updated")
        print("✅ Ready for production scraping")
        
        print(f"\n🚀 READY TO PROCESS ALL FUTURETOOLS:")
        print(f"   • Updated schema support")
        print(f"   • Enhanced data fields")
        print(f"   • Advanced tool details")
        print(f"   • Review sentiment tracking")
        print(f"   • Improved categorization")
        
    else:
        print("❌ Some tests failed")
        print("⚠️  Schema integration needs attention")
    
    return schema_test and enrichment_test

if __name__ == "__main__":
    main()