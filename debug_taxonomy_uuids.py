#!/usr/bin/env python3
"""
Debug script to check taxonomy UUIDs and identify invalid ones
"""

import logging
import sys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def debug_taxonomy_uuids():
    """Debug taxonomy UUIDs to find invalid ones"""
    try:
        from ai_navigator_client import AINavigatorClient
        import uuid
        
        logger.info("🔍 Debugging Taxonomy UUIDs...")
        
        # Initialize client
        client = AINavigatorClient()
        
        # Check categories
        logger.info("\n📂 CATEGORIES:")
        categories = client.get_categories()
        for i, category in enumerate(categories[:10]):  # Show first 10
            cat_id = category.get('id')
            cat_name = category.get('name')
            try:
                uuid.UUID(cat_id)
                valid = "✅"
            except (ValueError, TypeError):
                valid = "❌ INVALID"
            logger.info(f"  {i+1}. {cat_name}: {cat_id} {valid}")
        
        # Check tags
        logger.info("\n🏷️  TAGS:")
        tags = client.get_tags()
        for i, tag in enumerate(tags[:10]):  # Show first 10
            tag_id = tag.get('id')
            tag_name = tag.get('name')
            try:
                uuid.UUID(tag_id)
                valid = "✅"
            except (ValueError, TypeError):
                valid = "❌ INVALID"
            logger.info(f"  {i+1}. {tag_name}: {tag_id} {valid}")
        
        # Check features
        logger.info("\n🔧 FEATURES:")
        features = client.get_features()
        for i, feature in enumerate(features[:10]):  # Show first 10
            feature_id = feature.get('id')
            feature_name = feature.get('name')
            try:
                uuid.UUID(feature_id)
                valid = "✅"
            except (ValueError, TypeError):
                valid = "❌ INVALID"
            logger.info(f"  {i+1}. {feature_name}: {feature_id} {valid}")
        
        # Find the problematic UUID
        logger.info("\n🔍 SEARCHING FOR PROBLEMATIC UUID: *************-0000-0000-000000000003")
        
        all_items = []
        all_items.extend([('category', cat['name'], cat['id']) for cat in categories])
        all_items.extend([('tag', tag['name'], tag['id']) for tag in tags])
        all_items.extend([('feature', feat['name'], feat['id']) for feat in features])
        
        found_problematic = False
        for item_type, name, item_id in all_items:
            if item_id == "*************-0000-0000-000000000003":
                logger.error(f"❌ FOUND PROBLEMATIC UUID: {item_type} '{name}' has invalid UUID: {item_id}")
                found_problematic = True
        
        if not found_problematic:
            logger.info("✅ Problematic UUID not found in current taxonomy")
        
        # Check for any invalid UUIDs (more strict validation)
        logger.info("\n🔍 CHECKING FOR ALL INVALID UUIDs:")
        invalid_count = 0
        for item_type, name, item_id in all_items:
            try:
                parsed_uuid = uuid.UUID(item_id)
                # Check if it's a proper UUID format (not just parseable)
                if str(parsed_uuid) != item_id:
                    logger.error(f"❌ MALFORMED UUID: {item_type} '{name}' has malformed UUID: {item_id} (parsed as: {parsed_uuid})")
                    invalid_count += 1
                elif item_id.startswith('*************-0000-0000-'):
                    logger.error(f"❌ SUSPICIOUS UUID: {item_type} '{name}' has suspicious UUID pattern: {item_id}")
                    invalid_count += 1
            except (ValueError, TypeError):
                logger.error(f"❌ INVALID UUID: {item_type} '{name}' has invalid UUID: {item_id}")
                invalid_count += 1

        if invalid_count == 0:
            logger.info("✅ All UUIDs are valid!")
        else:
            logger.error(f"❌ Found {invalid_count} problematic UUIDs")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Exception during UUID debugging: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Taxonomy UUID Debugging...")
    logger.info("=" * 80)
    
    success = debug_taxonomy_uuids()
    
    logger.info("\n" + "=" * 80)
    logger.info(f"🏁 UUID DEBUGGING COMPLETE: {'✅ SUCCESS' if success else '❌ FAILED'}")
