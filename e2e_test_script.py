#!/usr/bin/env python3
"""
End-to-End Test Script for AI Navigator Scrapers
Tests the complete pipeline: Scrape → Enhance → Save to DB
"""

import json
import logging
import sys
import os
from datetime import datetime
import requests
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('e2e_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get('http://localhost:8001/api/health', timeout=10)
        if response.status_code == 200:
            logger.info("✅ Backend health check passed")
            return True
        else:
            logger.error(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Backend health check failed: {e}")
        return False

def load_test_data(file_path, limit=3):
    """Load test data from scraped file"""
    try:
        with open(file_path, 'r') as f:
            tools = [json.loads(line.strip()) for line in f if line.strip()]
        
        # Take only the first few items for testing
        test_tools = tools[:limit]
        logger.info(f"📊 Loaded {len(test_tools)} tools for testing from {file_path}")
        
        for i, tool in enumerate(test_tools):
            logger.info(f"  {i+1}. {tool.get('tool_name_on_directory', 'Unknown')} - {tool.get('external_website_url', 'No URL')}")
        
        return test_tools
    except Exception as e:
        logger.error(f"❌ Failed to load test data: {e}")
        return []

def create_mock_traditional_job(tools):
    """Create a mock traditional job for testing"""
    try:
        # Create a temporary file with our test data
        test_file = f'test_data_{int(time.time())}.jsonl'
        with open(test_file, 'w') as f:
            for tool in tools:
                f.write(json.dumps(tool) + '\n')

        logger.info(f"📝 Created test data file: {test_file}")
        return test_file

    except Exception as e:
        logger.error(f"❌ Failed to create mock traditional job: {e}")
        return None

def test_enhancement_api(tools):
    """Test the enhancement API endpoint using enhanced scraping"""
    try:
        url = 'http://localhost:8001/api/start-enhanced-scraping'

        logger.info(f"🔄 Testing enhancement pipeline with {len(tools)} tools")

        # Create a test data file
        test_file = create_mock_traditional_job(tools)
        if not test_file:
            return False

        # Prepare the request data for enhanced scraping
        # Convert tools to the expected format
        formatted_tools = []
        for tool in tools:
            formatted_tool = {
                "name": tool.get("tool_name_on_directory", tool.get("name", "Unknown Tool")),
                "url": tool.get("external_website_url", tool.get("url", "")),
                "description": tool.get("description", ""),
                "categories": tool.get("categories", []),
                "tags": tool.get("tags", [])
            }
            formatted_tools.append(formatted_tool)

        request_data = {
            'tools': formatted_tools,
            'use_parallel': True,
            'use_phase3': True,
            'save_to_db': True
        }

        logger.info(f"📤 Starting enhanced scraping job with test data...")

        # Make the API call
        response = requests.post(url, json=request_data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            logger.info(f"✅ Enhanced scraping job started successfully")
            logger.info(f"   Job ID: {job_id}")

            # Wait for the job to complete and check results
            return monitor_job_completion(job_id)

        else:
            logger.error(f"❌ Enhanced scraping job failed to start: {response.status_code}")
            logger.error(f"   Response: {response.text}")
            return False

    except Exception as e:
        logger.error(f"❌ Enhancement API test failed: {e}")
        return False
    finally:
        # Clean up test file
        if 'test_file' in locals() and test_file and os.path.exists(test_file):
            os.remove(test_file)
            logger.info(f"🧹 Cleaned up test file: {test_file}")

def monitor_job_completion(job_id):
    """Monitor job completion and return results"""
    try:
        max_wait_time = 120  # 2 minutes
        check_interval = 5   # 5 seconds
        elapsed_time = 0

        logger.info(f"⏳ Monitoring job {job_id} completion...")

        while elapsed_time < max_wait_time:
            # Check job status
            status_url = f'http://localhost:8001/api/job-status/{job_id}'
            response = requests.get(status_url, timeout=10)

            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get('status', 'unknown')

                logger.info(f"   Job status: {status} (elapsed: {elapsed_time}s)")

                if status == 'completed':
                    logger.info(f"✅ Job completed successfully!")

                    # Get detailed results
                    results_url = f'http://localhost:8001/api/enhanced-results/{job_id}'
                    results_response = requests.get(results_url, timeout=10)

                    if results_response.status_code == 200:
                        results = results_response.json()
                        logger.info(f"   Total processed: {results.get('total_processed', 0)}")
                        logger.info(f"   Successfully enhanced: {results.get('successful', 0)}")
                        logger.info(f"   Failed: {results.get('failed', 0)}")
                        logger.info(f"   Saved to database: {results.get('saved_to_db', 0)}")

                        return True
                    else:
                        logger.warning(f"⚠️ Could not get detailed results: {results_response.status_code}")
                        return True  # Job completed even if we can't get details

                elif status == 'failed':
                    logger.error(f"❌ Job failed")
                    return False

                elif status in ['running', 'processing']:
                    # Job is still running, continue waiting
                    pass
                else:
                    logger.warning(f"⚠️ Unknown job status: {status}")

            time.sleep(check_interval)
            elapsed_time += check_interval

        logger.error(f"❌ Job did not complete within {max_wait_time} seconds")
        return False

    except Exception as e:
        logger.error(f"❌ Error monitoring job completion: {e}")
        return False

def test_database_verification():
    """Test that data was actually saved to the database"""
    try:
        # Check the jobs endpoint to see recent activity
        jobs_url = 'http://localhost:8001/api/jobs'
        response = requests.get(jobs_url, timeout=10)

        if response.status_code == 200:
            jobs_data = response.json()
            logger.info(f"✅ Database verification - Jobs endpoint accessible")
            logger.info(f"   Enhanced jobs: {len(jobs_data.get('enhanced_jobs', {}))}")
            logger.info(f"   Traditional jobs: {len(jobs_data.get('traditional_jobs', {}))}")

            # Show recent enhanced jobs
            enhanced_jobs = jobs_data.get('enhanced_jobs', {})
            if enhanced_jobs:
                logger.info("   Recent enhanced jobs:")
                for job_id, job in list(enhanced_jobs.items())[-3:]:  # Show last 3
                    logger.info(f"     {job_id}: {job.get('status', 'Unknown')} - {job.get('total_processed', 0)} tools")

            # Test the status endpoint
            status_url = 'http://localhost:8001/api/status'
            status_response = requests.get(status_url, timeout=10)

            if status_response.status_code == 200:
                status_data = status_response.json()
                logger.info(f"   Pipeline status: {status_data.get('status', 'Unknown')}")
                logger.info(f"   Total enhanced jobs: {status_data.get('metrics', {}).get('total_enhanced_jobs', 0)}")
                logger.info(f"   Total traditional jobs: {status_data.get('metrics', {}).get('total_traditional_jobs', 0)}")

            return True
        else:
            logger.error(f"❌ Database verification failed: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"❌ Database verification failed: {e}")
        return False

def main():
    """Run the complete end-to-end test"""
    logger.info("🚀 Starting End-to-End Test for AI Navigator Scrapers")
    logger.info("=" * 60)
    
    # Test 1: Backend Health Check
    logger.info("📋 Test 1: Backend Health Check")
    if not test_backend_health():
        logger.error("❌ Backend health check failed. Exiting.")
        sys.exit(1)
    
    # Test 2: Load Test Data
    logger.info("\n📋 Test 2: Load Test Data")
    test_data_file = 'ai-navigator-scrapers/futuretools_final_all.jsonl'
    tools = load_test_data(test_data_file, limit=3)
    
    if not tools:
        logger.error("❌ No test data loaded. Exiting.")
        sys.exit(1)
    
    # Test 3: Enhancement Pipeline
    logger.info("\n📋 Test 3: Enhancement Pipeline")
    if not test_enhancement_api(tools):
        logger.error("❌ Enhancement pipeline test failed. Exiting.")
        sys.exit(1)
    
    # Test 4: Database Verification
    logger.info("\n📋 Test 4: Database Verification")
    if not test_database_verification():
        logger.error("❌ Database verification failed. Exiting.")
        sys.exit(1)
    
    # Final Summary
    logger.info("\n" + "=" * 60)
    logger.info("🎉 END-TO-END TEST COMPLETED SUCCESSFULLY!")
    logger.info("✅ All pipeline components are working correctly:")
    logger.info("   • Backend server is healthy")
    logger.info("   • Test data loaded successfully")
    logger.info("   • Enhancement pipeline processed tools")
    logger.info("   • Data was saved to database")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
