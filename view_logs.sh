#!/bin/bash

# AI Navigator Scrapers - Log Viewer
# This script helps you view logs from both backend and frontend

echo "🔍 AI Navigator Scrapers - Log Viewer"
echo "====================================="

# Function to check if service is running
check_service() {
    local service=$1
    local pattern=$2
    if pgrep -f "$pattern" > /dev/null; then
        echo "✅ $service is running"
        return 0
    else
        echo "❌ $service is not running"
        return 1
    fi
}

echo ""
echo "📊 Service Status:"
check_service "Backend Server" "backend/server.py"
check_service "Frontend Server" "react-scripts"

echo ""
echo "🔗 Available Log Sources:"
echo "1. Backend API Status: curl http://localhost:8001/api/status"
echo "2. Backend Job Logs: curl http://localhost:8001/api/logs"
echo "3. Real-time Backend Process: Use 'tail -f' on backend process"
echo "4. Frontend Terminal: Check the terminal where you ran start script"

echo ""
echo "📋 Current Backend Status:"
if curl -s http://localhost:8001/api/status > /dev/null 2>&1; then
    curl -s http://localhost:8001/api/status | python -m json.tool 2>/dev/null || curl -s http://localhost:8001/api/status
else
    echo "❌ Backend API not responding"
fi

echo ""
echo "🚀 Quick Log Commands:"
echo "   Backend Status: curl -s http://localhost:8001/api/status | python -m json.tool"
echo "   Backend Logs: curl -s http://localhost:8001/api/logs | python -m json.tool"
echo "   Real-time Monitor: ./view_logs.sh status"
echo "   Process Info: ./view_logs.sh backend"

echo ""
echo "💡 For real-time monitoring, you can also:"
echo "   1. Open http://localhost:3000 and check the 'Logs' tab"
echo "   2. Use the Dashboard tab for visual monitoring"
echo "   3. Check the Results tab for completed jobs"

# If a parameter is provided, show specific logs
if [ "$1" = "status" ]; then
    echo ""
    echo "🔄 Real-time Status Monitoring (Press Ctrl+C to stop):"
    # Use a loop instead of watch (which isn't available on Mac by default)
    while true; do
        clear
        echo "🔄 Real-time Status Monitoring - $(date)"
        echo "================================================"
        curl -s http://localhost:8001/api/status | python -m json.tool 2>/dev/null || echo "❌ Backend not responding"
        echo ""
        echo "Press Ctrl+C to stop..."
        sleep 2
    done
elif [ "$1" = "backend" ]; then
    echo ""
    echo "🖥️  Backend Process Info:"
    ps aux | grep "backend/server.py" | grep -v grep
elif [ "$1" = "frontend" ]; then
    echo ""
    echo "🌐 Frontend Process Info:"
    ps aux | grep "react-scripts" | grep -v grep
fi 