#!/usr/bin/env python3
"""
Test script to verify that "status": "ACTIVE" is added as default to all POST /entities requests
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_create_entity_adds_status():
    """Test that create_entity method adds status: ACTIVE to all entity data"""
    
    # Import after path setup
    from ai_navigator_client import AINavigatorClient
    
    # Create client instance
    client = AINavigatorClient()
    
    # Mock the requests.post to capture the data being sent
    with patch('ai_navigator_client.requests.post') as mock_post:
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.json.return_value = {'id': 'test-id', 'name': 'Test Tool'}
        mock_post.return_value = mock_response
        
        # Mock the _get_headers method to avoid authentication
        with patch.object(client, '_get_headers', return_value={'Authorization': 'Bearer test-token'}):
            
            # Test entity data without status
            test_entity_data = {
                'name': 'Test AI Tool',
                'website_url': 'https://example.com',
                'description': 'A test AI tool',
                'category_ids': ['cat-1', 'cat-2'],
                'tag_ids': ['tag-1'],
                'feature_ids': ['feat-1', 'feat-2']
            }
            
            print("🧪 Testing create_entity with data:")
            print(json.dumps(test_entity_data, indent=2))
            
            # Call create_entity
            result = client.create_entity(test_entity_data)
            
            # Verify the method was called
            assert mock_post.called, "requests.post should have been called"
            
            # Get the actual data that was sent in the POST request
            call_args = mock_post.call_args
            sent_data = call_args[1]['json']  # The json parameter
            
            print("\n📤 Data sent to POST /entities:")
            print(json.dumps(sent_data, indent=2))
            
            # Verify status was added
            assert 'status' in sent_data, "Status field should be present in sent data"
            assert sent_data['status'] == 'ACTIVE', "Status should be 'ACTIVE'"
            
            # Verify original data is preserved
            assert sent_data['name'] == test_entity_data['name'], "Original name should be preserved"
            assert sent_data['website_url'] == test_entity_data['website_url'], "Original website_url should be preserved"
            assert sent_data['description'] == test_entity_data['description'], "Original description should be preserved"
            assert sent_data['category_ids'] == test_entity_data['category_ids'], "Original category_ids should be preserved"
            assert sent_data['tag_ids'] == test_entity_data['tag_ids'], "Original tag_ids should be preserved"
            assert sent_data['feature_ids'] == test_entity_data['feature_ids'], "Original feature_ids should be preserved"
            
            # Verify original data wasn't modified
            assert 'status' not in test_entity_data, "Original entity_data should not be modified"
            
            print("\n✅ All tests passed!")
            print("✅ Status 'ACTIVE' is automatically added to all POST /entities requests")
            print("✅ Original entity data is preserved and not modified")
            
            return True

def test_create_entity_with_existing_status():
    """Test that existing status is overridden with ACTIVE"""
    
    from ai_navigator_client import AINavigatorClient
    
    client = AINavigatorClient()
    
    with patch('ai_navigator_client.requests.post') as mock_post:
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.json.return_value = {'id': 'test-id', 'name': 'Test Tool'}
        mock_post.return_value = mock_response
        
        with patch.object(client, '_get_headers', return_value={'Authorization': 'Bearer test-token'}):
            
            # Test entity data WITH existing status
            test_entity_data = {
                'name': 'Test AI Tool',
                'website_url': 'https://example.com',
                'status': 'INACTIVE'  # This should be overridden
            }
            
            print("\n🧪 Testing create_entity with existing status:")
            print(json.dumps(test_entity_data, indent=2))
            
            result = client.create_entity(test_entity_data)
            
            call_args = mock_post.call_args
            sent_data = call_args[1]['json']
            
            print("\n📤 Data sent to POST /entities:")
            print(json.dumps(sent_data, indent=2))
            
            # Verify status was overridden to ACTIVE
            assert sent_data['status'] == 'ACTIVE', "Status should be overridden to 'ACTIVE'"
            
            # Verify original data still has the old status (not modified)
            assert test_entity_data['status'] == 'INACTIVE', "Original entity_data should not be modified"
            
            print("\n✅ Status override test passed!")
            print("✅ Existing status is properly overridden with 'ACTIVE'")
            
            return True

if __name__ == '__main__':
    print("🚀 Testing POST /entities status default implementation")
    print("=" * 60)
    
    try:
        # Run tests
        test_create_entity_adds_status()
        test_create_entity_with_existing_status()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Implementation successfully adds 'status': 'ACTIVE' to all POST /entities requests")
        print("✅ This covers both traditional and enhanced scraping workflows")
        print("✅ Original entity data is preserved and not modified")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)