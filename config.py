"""
Configuration Management for AI Navigator Scrapers
Handles environment variables and secure configuration loading
"""

import os
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str
    timeout: int = 30

@dataclass
class APIConfig:
    """API configuration"""
    xai_api_key: str
    perplexity_api_key: str
    ai_navigator_base_url: str
    ai_navigator_admin_email: str
    ai_navigator_admin_password: str

@dataclass
class ScrapingConfig:
    """Scraping configuration"""
    user_agent: str
    delay_range: tuple
    concurrent_requests: int
    timeout: int
    respect_robots_txt: bool

@dataclass
class ServerConfig:
    """Server configuration"""
    host: str
    port: int
    debug: bool
    cors_origins: list
    log_level: str

class Config:
    """Main configuration class that loads and validates all settings"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._load_config()
        self._validate_config()

    def _load_config(self):
        """Load configuration from environment variables"""

        # Database configuration
        self.database = DatabaseConfig(
            url=self._get_env_var("DATABASE_URL", ""),
            timeout=int(self._get_env_var("DATABASE_TIMEOUT", "30"))
        )

        # API configuration
        self.api = APIConfig(
            xai_api_key=self._get_env_var("XAI_API_KEY", ""),
            perplexity_api_key=self._get_env_var("PERPLEXITY_API_KEY", ""),
            ai_navigator_base_url=self._get_env_var("AI_NAVIGATOR_BASE_URL", "https://ai-nav.onrender.com"),
            ai_navigator_admin_email=self._get_env_var("AI_NAVIGATOR_ADMIN_EMAIL", ""),
            ai_navigator_admin_password=self._get_env_var("AI_NAVIGATOR_ADMIN_PASSWORD", "")
        )

        # Scraping configuration
        self.scraping = ScrapingConfig(
            user_agent=self._get_env_var("SCRAPING_USER_AGENT", "AI Navigator Bot 1.0"),
            delay_range=(
                float(self._get_env_var("SCRAPING_MIN_DELAY", "1.0")),
                float(self._get_env_var("SCRAPING_MAX_DELAY", "3.0"))
            ),
            concurrent_requests=int(self._get_env_var("SCRAPING_CONCURRENT_REQUESTS", "8")),
            timeout=int(self._get_env_var("SCRAPING_TIMEOUT", "30")),
            respect_robots_txt=self._get_env_var("SCRAPING_RESPECT_ROBOTS", "true").lower() == "true"
        )

        # Server configuration
        cors_origins_str = self._get_env_var("CORS_ORIGINS", "http://localhost:3000")
        cors_origins = [origin.strip() for origin in cors_origins_str.split(",") if origin.strip()]

        self.server = ServerConfig(
            host=self._get_env_var("SERVER_HOST", "0.0.0.0"),
            port=int(self._get_env_var("SERVER_PORT", "8001")),
            debug=self._get_env_var("DEBUG", "false").lower() == "true",
            cors_origins=cors_origins,
            log_level=self._get_env_var("LOG_LEVEL", "INFO")
        )

        # File paths - Use local paths for development, container paths for production
        base_dir = os.path.dirname(os.path.abspath(__file__))  # Get the directory where config.py is located

        # Default to local development paths
        default_scrapy_dir = os.path.join(base_dir, "ai-navigator-scrapers")
        default_output_dir = os.path.join(base_dir, "output")
        default_log_dir = os.path.join(base_dir, "logs")

        self.scrapy_dir = self._get_env_var("SCRAPY_DIR", default_scrapy_dir)
        self.output_dir = self._get_env_var("OUTPUT_DIR", default_output_dir)
        self.log_dir = self._get_env_var("LOG_DIR", default_log_dir)

    def _get_env_var(self, key: str, default: str = "") -> str:
        """Get environment variable with optional default"""
        value = os.getenv(key, default)
        if not value and not default:
            self.logger.warning(f"Environment variable {key} is not set")
        return value

    def _validate_config(self):
        """Validate critical configuration values"""
        errors = []

        # Validate required API keys
        if not self.api.xai_api_key:
            errors.append("XAI_API_KEY is required")

        if not self.api.perplexity_api_key:
            errors.append("PERPLEXITY_API_KEY is required")

        if not self.api.ai_navigator_admin_email:
            errors.append("AI_NAVIGATOR_ADMIN_EMAIL is required")

        if not self.api.ai_navigator_admin_password:
            errors.append("AI_NAVIGATOR_ADMIN_PASSWORD is required")

        # Validate server configuration
        if self.server.port < 1 or self.server.port > 65535:
            errors.append("SERVER_PORT must be between 1 and 65535")

        # Validate scraping configuration
        if self.scraping.delay_range[0] < 0 or self.scraping.delay_range[1] < self.scraping.delay_range[0]:
            errors.append("Invalid scraping delay range")

        if self.scraping.concurrent_requests < 1:
            errors.append("SCRAPING_CONCURRENT_REQUESTS must be at least 1")

        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"  - {error}" for error in errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def get_scrapy_settings(self) -> Dict[str, Any]:
        """Get Scrapy-specific settings"""
        return {
            'USER_AGENT': self.scraping.user_agent,
            'DOWNLOAD_DELAY': self.scraping.delay_range[0],
            'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
            'CONCURRENT_REQUESTS': self.scraping.concurrent_requests,
            'DOWNLOAD_TIMEOUT': self.scraping.timeout,
            'ROBOTSTXT_OBEY': self.scraping.respect_robots_txt,
        }

    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self._get_env_var("ENVIRONMENT", "development").lower() == "production"

# Global configuration instance
config = Config()