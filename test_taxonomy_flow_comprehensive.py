#!/usr/bin/env python3
"""
Comprehensive Taxonomy Flow Test
Tests the complete taxonomy management flow from suggestions to database saves
"""

import sys
import os
import json
import logging
import time
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_navigator_client import AINavigatorClient
from taxonomy_manager import TaxonomyManager, TaxonomyResult

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def test_taxonomy_management():
    """Test the complete taxonomy management flow"""
    print("🚀 COMPREHENSIVE TAXONOMY MANAGEMENT TEST")
    print("=" * 60)
    
    try:
        # Initialize client
        print("🔧 Initializing AI Navigator client...")
        client = AINavigatorClient()
        print("✅ AI Navigator client initialized")
        
        # Test 1: Basic taxonomy processing
        print("\n🧪 TEST 1: Basic Taxonomy Processing")
        print("-" * 40)
        
        test_categories = ["AI Tools", "Content Creation", "Business Analytics"]
        test_features = ["Natural Language Processing", "Image Generation", "Real-time Analytics"]
        test_tags = ["Machine Learning", "Automation", "Cloud-based"]
        
        print(f"📝 Testing with:")
        print(f"   Categories: {test_categories}")
        print(f"   Features: {test_features}")
        print(f"   Tags: {test_tags}")
        
        result = client.process_taxonomy_suggestions(
            suggested_categories=test_categories,
            suggested_features=test_features,
            suggested_tags=test_tags
        )
        
        print(f"\n📊 Results:")
        print(f"   Categories processed: {len(result.categories)}")
        print(f"   Features processed: {len(result.features)}")
        print(f"   Tags processed: {len(result.tags)}")
        print(f"   Errors: {len(result.errors)}")
        
        if result.errors:
            print(f"   ⚠️  Error details: {result.errors}")
        
        # Test 2: UUID validation
        print("\n🧪 TEST 2: UUID Validation")
        print("-" * 40)
        
        category_uuids, feature_uuids, tag_uuids = client.taxonomy_manager.get_taxonomy_uuids(result)
        
        print(f"🔑 Extracted UUIDs:")
        print(f"   Category UUIDs: {len(category_uuids)}")
        print(f"   Feature UUIDs: {len(feature_uuids)}")
        print(f"   Tag UUIDs: {len(tag_uuids)}")
        
        # Validate UUID format
        import uuid
        valid_uuids = 0
        total_uuids = 0
        
        for uuid_list, name in [(category_uuids, "categories"), (feature_uuids, "features"), (tag_uuids, "tags")]:
            for uuid_str in uuid_list:
                total_uuids += 1
                try:
                    uuid.UUID(uuid_str)
                    valid_uuids += 1
                    print(f"   ✅ Valid {name} UUID: {uuid_str}")
                except ValueError:
                    print(f"   ❌ Invalid {name} UUID: {uuid_str}")
        
        # Test 3: Entity creation with taxonomy
        print("\n🧪 TEST 3: Entity Creation with Taxonomy")
        print("-" * 40)
        
        # Get AI tool entity type ID
        entity_type_id = client.get_ai_tool_entity_type_id()
        if not entity_type_id:
            print("❌ Could not get AI tool entity type ID")
            return False
        
        print(f"✅ Got AI tool entity type ID: {entity_type_id}")
        
        # Test entity data
        test_entity_data = {
            "name": f"Test AI Tool {int(time.time())}",
            "website_url": "https://example-ai-tool.com",
            "entity_type_id": entity_type_id,
            "short_description": "A test AI tool for taxonomy validation",
            "description": "This is a comprehensive test AI tool designed to validate our taxonomy management flow.",
            "status": "ACTIVE"
        }
        
        # Test taxonomy suggestions
        test_categories = ["AI Tools", "Content Generation"]
        test_features = ["Text Generation", "Analytics Dashboard"]
        test_tags = ["Automated", "Cloud-based", "Enterprise"]
        
        print(f"🏗️  Creating entity with taxonomy...")
        print(f"   Entity: {test_entity_data['name']}")
        print(f"   Categories: {test_categories}")
        print(f"   Features: {test_features}")
        print(f"   Tags: {test_tags}")
        
        # Create entity with taxonomy
        created_entity = client.create_entity_with_taxonomy(
            entity_data=test_entity_data,
            suggested_categories=test_categories,
            suggested_features=test_features,
            suggested_tags=test_tags
        )
        
        if created_entity:
            print(f"✅ Entity created successfully!")
            print(f"   Entity ID: {created_entity.get('id')}")
            print(f"   Name: {created_entity.get('name')}")
            
            # Check if entity has taxonomy associations
            if created_entity.get('categories') or created_entity.get('features') or created_entity.get('tags'):
                print(f"   ✅ Entity has taxonomy associations")
            else:
                print(f"   ⚠️  Entity created but no taxonomy associations visible in response")
            
        else:
            print("❌ Entity creation returned None")
            return False
        
        # Test 4: Cache performance
        print("\n🧪 TEST 4: Cache Performance")
        print("-" * 40)
        
        test_categories = ["AI Tools", "Content Creation"]
        
        # First request (should create/cache)
        start_time = time.time()
        result1 = client.process_taxonomy_suggestions(suggested_categories=test_categories)
        first_time = time.time() - start_time
        
        # Second request (should use cache)
        start_time = time.time()
        result2 = client.process_taxonomy_suggestions(suggested_categories=test_categories)
        second_time = time.time() - start_time
        
        print(f"⏱️  Performance Results:")
        print(f"   First request: {first_time:.3f}s")
        print(f"   Second request: {second_time:.3f}s")
        if second_time > 0:
            print(f"   Speedup: {first_time/second_time:.1f}x")
        
        # Check cache stats
        cache_stats = client.get_taxonomy_cache_stats()
        print(f"📊 Cache Stats: {cache_stats}")
        
        # Final results
        print("\n" + "=" * 60)
        print("🎯 FINAL TEST RESULTS")
        print("=" * 60)
        
        success_criteria = [
            (len(result.categories) > 0 or len(result.features) > 0 or len(result.tags) > 0, "Taxonomy processing"),
            (valid_uuids == total_uuids and total_uuids > 0, "UUID validation"),
            (created_entity is not None, "Entity creation"),
            (first_time > 0 and second_time >= 0, "Performance test")
        ]
        
        passed = sum(1 for success, _ in success_criteria if success)
        total = len(success_criteria)
        
        for success, test_name in success_criteria:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"   {status}: {test_name}")
        
        print(f"\n📊 Overall Results:")
        print(f"   Tests Passed: {passed}/{total}")
        print(f"   Success Rate: {(passed/total*100):.1f}%")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Taxonomy flow is working correctly.")
            return True
        else:
            print(f"⚠️  {total-passed} test(s) failed. Please check the details above.")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main test execution"""
    success = test_taxonomy_management()
    
    if success:
        print("\n🎉 TAXONOMY FLOW TEST COMPLETED SUCCESSFULLY!")
        print("The complete taxonomy management flow is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ TAXONOMY FLOW TEST FAILED!")
        print("Please check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
