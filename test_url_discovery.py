"""
Test script for URL Discovery Service
Tests URL discovery with real websites
"""

import sys
sys.path.append('/app')

from url_discovery_service import URLDiscoveryService
import time

def test_url_discovery():
    """Test the URL discovery service with real websites"""
    
    print("🔍 TESTING URL DISCOVERY SERVICE")
    print("=" * 50)
    
    # Initialize service
    discoverer = URLDiscoveryService()
    
    # Test cases with known URLs
    test_websites = [
        {
            "name": "OpenAI",
            "url": "https://openai.com",
            "expected_urls": {
                "documentation_url": True,  # Expect to find docs
                "contact_url": True,        # Expect to find contact
                "privacy_policy_url": True, # Expect to find privacy
                "pricing_url": True         # Expect to find pricing
            }
        },
        {
            "name": "GitHub",
            "url": "https://github.com",
            "expected_urls": {
                "documentation_url": True,
                "contact_url": True,
                "privacy_policy_url": True,
                "pricing_url": True
            }
        },
        {
            "name": "Notion",
            "url": "https://notion.so",
            "expected_urls": {
                "documentation_url": True,
                "contact_url": True,
                "privacy_policy_url": True,
                "pricing_url": True
            }
        }
    ]
    
    total_expected = 0
    total_found = 0
    
    for i, test_case in enumerate(test_websites, 1):
        print(f"\n🌐 Test {i}: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        
        try:
            # Discover URLs
            start_time = time.time()
            discovered_urls = discoverer.discover_urls(test_case['url'])
            discovery_time = time.time() - start_time
            
            print(f"   Time: {discovery_time:.2f}s")
            print(f"   Discovered URLs:")
            
            case_expected = 0
            case_found = 0
            
            for url_type, url in discovered_urls.items():
                expected = test_case['expected_urls'].get(url_type, False)
                if expected:
                    case_expected += 1
                    if url:
                        case_found += 1
                        print(f"     ✅ {url_type}: {url}")
                    else:
                        print(f"     ❌ {url_type}: Not found")
                elif url:
                    print(f"     ℹ️  {url_type}: {url} (bonus)")
            
            case_success_rate = (case_found / case_expected * 100) if case_expected > 0 else 0
            print(f"   Success rate: {case_success_rate:.1f}% ({case_found}/{case_expected})")
            
            total_expected += case_expected
            total_found += case_found
            
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
        
        # Small delay to be respectful
        time.sleep(1)
    
    # Calculate overall success rate
    overall_success_rate = (total_found / total_expected * 100) if total_expected > 0 else 0
    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Total found: {total_found}/{total_expected}")
    print(f"   Overall success rate: {overall_success_rate:.1f}%")
    
    if overall_success_rate >= 70:
        print("   🎉 TARGET SUCCESS RATE ACHIEVED (70%+)")
    else:
        print("   ⚠️  Below target success rate - needs improvement")
    
    return overall_success_rate >= 70

def test_pattern_discovery():
    """Test pattern-based URL discovery"""
    
    print("\n🔧 TESTING PATTERN-BASED DISCOVERY")
    print("=" * 50)
    
    discoverer = URLDiscoveryService()
    
    # Test with a website that likely has standard patterns
    test_url = "https://github.com"
    
    print(f"Testing pattern discovery on: {test_url}")
    
    # Test pattern discovery specifically
    pattern_results = discoverer._discover_by_patterns(test_url)
    
    print(f"Pattern-based discovery found:")
    found_count = 0
    for url_type, url in pattern_results.items():
        if url:
            print(f"  ✅ {url_type}: {url}")
            found_count += 1
        else:
            print(f"  ❌ {url_type}: Not found")
    
    print(f"Pattern discovery success: {found_count}/{len(pattern_results)} URLs found")
    return found_count > 0

def test_sample_content_discovery():
    """Test URL discovery with sample HTML content"""
    
    print("\n🧪 TESTING WITH SAMPLE HTML CONTENT")
    print("=" * 50)
    
    discoverer = URLDiscoveryService()
    
    # Sample HTML with various URL types
    sample_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Website</title>
    </head>
    <body>
        <nav>
            <a href="/docs">Documentation</a>
            <a href="/contact">Contact Us</a>
            <a href="/pricing">Pricing</a>
        </nav>
        <footer>
            <a href="/privacy-policy">Privacy Policy</a>
            <a href="/terms">Terms of Service</a>
            <a href="/help">Help Center</a>
        </footer>
    </body>
    </html>
    """
    
    base_url = "https://example.com"
    
    print(f"Testing with sample HTML content...")
    
    # Test link text discovery
    link_results = discoverer._discover_by_link_text(sample_html, base_url)
    
    print(f"Link text discovery found:")
    found_count = 0
    for url_type, url in link_results.items():
        if url:
            print(f"  ✅ {url_type}: {url}")
            found_count += 1
        else:
            print(f"  ❌ {url_type}: Not found")
    
    return found_count > 0

if __name__ == "__main__":
    # Test pattern discovery first
    pattern_success = test_pattern_discovery()
    
    # Test sample content discovery
    sample_success = test_sample_content_discovery()
    
    # Test with real websites
    real_website_success = test_url_discovery()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Pattern discovery: {'✅ PASS' if pattern_success else '❌ FAIL'}")
    print(f"   Sample content: {'✅ PASS' if sample_success else '❌ FAIL'}")
    print(f"   Real websites: {'✅ PASS' if real_website_success else '❌ FAIL'}")
    
    if pattern_success and sample_success and real_website_success:
        print("   🎉 ALL TESTS PASSED!")
    else:
        print("   ⚠️  Some tests failed - needs improvement")
