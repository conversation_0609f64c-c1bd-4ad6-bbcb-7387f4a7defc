#!/usr/bin/env python3
"""
Debug script to test the full pipeline with a small number of items
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraper_pipeline import ScraperPipeline
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_full_pipeline():
    """Test the full pipeline with just 3 items to see what happens"""
    
    print("🔍 Testing Full Pipeline with 3 Items")
    print("=" * 50)
    
    try:
        pipeline = ScraperPipeline()
        
        print("Running spider with max 3 items...")
        result = pipeline.run_spider('futuretools', 3)
        
        print("\n🏁 PIPELINE RESULT:")
        print("=" * 30)
        print(f"Success: {result.get('success', 'Unknown')}")
        print(f"Total Processed: {result.get('stats', {}).get('total_processed', 'Unknown')}")
        print(f"Successful Submissions: {result.get('stats', {}).get('successful_submissions', 'Unknown')}")
        print(f"Failed Submissions: {result.get('stats', {}).get('failed_submissions', 'Unknown')}")
        print(f"Duplicates Skipped: {result.get('stats', {}).get('duplicates_skipped', 'Unknown')}")
        print(f"Errors: {result.get('stats', {}).get('errors', [])}")
        
        # Check if stats are accurate
        total = result.get('stats', {}).get('total_processed', 0)
        success = result.get('stats', {}).get('successful_submissions', 0)
        failed = result.get('stats', {}).get('failed_submissions', 0)
        skipped = result.get('stats', {}).get('duplicates_skipped', 0)
        
        print(f"\n📊 STATS ANALYSIS:")
        print(f"Total should equal Success + Failed + Skipped: {total} = {success} + {failed} + {skipped}")
        print(f"Math check: {total} == {success + failed + skipped} = {total == success + failed + skipped}")
        
        if skipped == 0 and (success > 0 or failed > 0):
            print("🚨 WARNING: No duplicates skipped but items were processed - duplicate detection may not be working!")
        elif skipped > 0:
            print("✅ Duplicates were properly skipped - duplicate detection is working!")
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_pipeline()
