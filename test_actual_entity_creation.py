#!/usr/bin/env python3

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append('.')

from ai_navigator_client import AINavigatorClient

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_actual_entity_creation():
    """Test actual entity creation with feature_ids to see what happens"""
    print("🧪 Testing Actual Entity Creation with Feature IDs")
    print("=" * 60)
    
    # Initialize client
    client = AINavigatorClient()
    
    # Test login
    if not client._refresh_token():
        print("❌ Failed to login to AI Navigator API")
        return False
    
    print("✅ Successfully logged in to AI Navigator API")
    
    # Get entity type ID for ai-tool
    entity_types = client.get_entity_types()
    ai_tool_type = None
    for et in entity_types:
        if et.get('slug') == 'ai-tool':
            ai_tool_type = et
            break
    
    if not ai_tool_type:
        print("❌ Could not find ai-tool entity type")
        return False
    
    print(f"✅ Found ai-tool entity type: {ai_tool_type['id']}")
    
    # Create a simple test entity with feature_ids
    test_entity = {
        "name": "Feature Test Tool",
        "website_url": "https://example.com/feature-test",
        "entity_type_id": ai_tool_type['id'],
        "description": "A test tool to verify feature_ids are working",
        "feature_ids": [
            "71e0c8d7-64c0-4e75-bad6-9e47636d64d1",  # Text Generation (from previous test)
            "36008fcd-fed1-4a39-b23b-2d49a4704916"   # API Integration (from previous test)
        ],
        "category_ids": ["e52307e3-46d3-4516-a8d0-5ad7fde336b6"],  # Fallback category
        "tag_ids": ["954d898e-0860-41b0-89bf-073db78d4c19"],       # Fallback tag
        "status": "ACTIVE"
    }
    
    print(f"\n📤 Sending entity creation request:")
    print(f"   Name: {test_entity['name']}")
    print(f"   Feature IDs: {test_entity['feature_ids']}")
    print(f"   Category IDs: {test_entity['category_ids']}")
    print(f"   Tag IDs: {test_entity['tag_ids']}")
    
    try:
        # Create the entity
        response = client.create_entity(test_entity)
        
        if response:
            print(f"\n✅ Entity created successfully!")
            print(f"   Entity ID: {response.get('id')}")
            print(f"   Name: {response.get('name')}")
            print(f"   Status: {response.get('status')}")
            
            # Check if features are in the response
            features = response.get('features', [])
            print(f"\n🎯 Features in response: {len(features)}")
            if features:
                for feature in features:
                    print(f"   - {feature.get('name')} ({feature.get('id')})")
            else:
                print("   ❌ No features found in response")
            
            # Get the entity by ID to see full details
            print(f"\n🔍 Fetching entity details...")
            try:
                import requests
                headers = client._get_headers()
                detail_response = requests.get(
                    f"{client.base_url}/entities/{response['id']}",
                    headers=headers,
                    timeout=10
                )
                
                if detail_response.status_code == 200:
                    entity_details = detail_response.json()
                    features_detail = entity_details.get('features', [])
                    print(f"   Features in detailed response: {len(features_detail)}")
                    if features_detail:
                        for feature in features_detail:
                            print(f"   - {feature.get('name')} ({feature.get('id')})")
                    else:
                        print("   ❌ Still no features in detailed response")
                    
                    # Print the full response for debugging
                    print(f"\n📋 Full entity response:")
                    print(json.dumps(entity_details, indent=2))
                else:
                    print(f"   ❌ Failed to fetch entity details: {detail_response.status_code}")
            except Exception as e:
                print(f"   ❌ Error fetching entity details: {str(e)}")
            
            return True
        else:
            print("❌ Failed to create entity")
            return False
            
    except Exception as e:
        print(f"❌ Error creating entity: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_actual_entity_creation()
    sys.exit(0 if success else 1)